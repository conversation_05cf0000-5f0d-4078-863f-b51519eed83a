#!/usr/bin/env python3
"""
测试News API连接修复
"""

import os
import sys
import time
import requests
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
from dotenv import load_dotenv

def make_robust_request(url, params=None, headers=None, method='GET', json_data=None, max_retries=3, timeout=(15, 45)):
    """
    创建一个更强大的网络请求方法，具有更好的错误处理和重试机制
    """
    for attempt in range(max_retries):
        try:
            # 创建会话对象
            session = requests.Session()
            
            # 配置重试策略
            retry_strategy = Retry(
                total=3,
                backoff_factor=2,  # 指数退避
                status_forcelist=[429, 500, 502, 503, 504, 520, 521, 522, 523, 524],
                allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
            )
            
            # 配置适配器
            adapter = HTTPAdapter(
                max_retries=retry_strategy,
                pool_connections=10,
                pool_maxsize=20
            )
            
            session.mount("http://", adapter)
            session.mount("https://", adapter)
            
            # 设置用户代理
            if headers is None:
                headers = {}
            headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })
            
            # 发送请求
            if method.upper() == 'GET':
                response = session.get(url, params=params, headers=headers, timeout=timeout, verify=True)
            elif method.upper() == 'POST':
                response = session.post(url, params=params, headers=headers, json=json_data, timeout=timeout, verify=True)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            # 检查响应状态
            response.raise_for_status()
            return response
            
        except requests.exceptions.Timeout as e:
            print(f"请求超时 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1  # 指数退避
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise
                
        except requests.exceptions.ConnectionError as e:
            print(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise
                
        except requests.exceptions.HTTPError as e:
            print(f"HTTP错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if e.response.status_code in [429, 500, 502, 503, 504] and attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise
                
        except Exception as e:
            print(f"未知错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                raise
    
    raise Exception(f"在 {max_retries} 次尝试后仍然失败")

def test_news_api():
    """测试News API连接"""
    print("=== News API连接测试 ===")
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('NEWS_API_KEY')
    
    if not api_key:
        print("❌ 未找到NEWS_API_KEY环境变量")
        return False
    
    try:
        # 测试参数
        url = 'https://newsapi.org/v2/everything'
        params = {
            'q': 'cryptocurrency',
            'pageSize': 5,
            'language': 'en',
            'apiKey': api_key
        }
        
        print("🔄 开始测试News API连接...")
        print(f"📡 URL: {url}")
        print(f"🔑 API Key: {api_key[:10]}...")
        print(f"⏱️  超时设置: 连接15秒, 读取45秒")
        
        # 使用强大的请求方法
        response = make_robust_request(
            url=url,
            params=params,
            timeout=(15, 45),
            max_retries=3
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok':
                articles = data.get('articles', [])
                print(f"✅ News API连接成功!")
                print(f"📰 获取到 {len(articles)} 条新闻")
                
                # 显示前3条新闻标题
                for i, article in enumerate(articles[:3], 1):
                    title = article.get('title', 'No title')
                    print(f"   {i}. {title[:80]}...")
                
                return True
            else:
                error_msg = data.get('message', '未知错误')
                print(f"❌ API返回错误: {error_msg}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def test_different_queries():
    """测试不同的查询参数"""
    print("\n=== 测试不同查询参数 ===")
    
    load_dotenv()
    api_key = os.getenv('NEWS_API_KEY')
    
    if not api_key:
        print("❌ 未找到NEWS_API_KEY环境变量")
        return
    
    # 测试不同的查询
    test_queries = [
        'bitcoin',
        'ethereum',
        'cryptocurrency market',
        'crypto regulation'
    ]
    
    for query in test_queries:
        try:
            print(f"\n🔍 测试查询: '{query}'")
            
            url = 'https://newsapi.org/v2/everything'
            params = {
                'q': query,
                'pageSize': 3,
                'language': 'en',
                'sortBy': 'publishedAt',
                'apiKey': api_key
            }
            
            response = make_robust_request(
                url=url,
                params=params,
                timeout=(15, 45),
                max_retries=2
            )
            
            if response.status_code == 200:
                data = response.json()
                articles = data.get('articles', [])
                print(f"   ✅ 成功获取 {len(articles)} 条新闻")
            else:
                print(f"   ❌ 失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始News API连接修复测试\n")
    
    # 基本连接测试
    success = test_news_api()
    
    if success:
        # 如果基本测试成功，进行更多测试
        test_different_queries()
        print("\n🎉 所有测试完成!")
    else:
        print("\n❌ 基本连接测试失败，请检查API密钥和网络连接")
    
    print("\n📋 修复总结:")
    print("1. ✅ 增加了连接超时时间 (15秒)")
    print("2. ✅ 增加了读取超时时间 (45秒)")
    print("3. ✅ 添加了指数退避重试机制")
    print("4. ✅ 改进了错误处理和日志记录")
    print("5. ✅ 添加了更强大的会话管理")
