#!/usr/bin/env python3
"""
快速系统检查 - 检查关键功能和潜在问题
"""

import os
import json
import sys
from datetime import datetime

def check_files():
    """检查关键文件"""
    print("📁 文件检查")
    print("-" * 40)
    
    files_to_check = {
        'main_window.py': '主程序文件',
        '.env': '环境配置文件',
        'config.json': '配置文件',
        'trading_settings.json': '交易设置文件',
        'requirements.txt': '依赖文件'
    }
    
    all_good = True
    for file_name, description in files_to_check.items():
        if os.path.exists(file_name):
            print(f"✅ {file_name}: {description}")
        else:
            print(f"❌ {file_name}: 缺失 - {description}")
            all_good = False
    
    return all_good

def check_config_integrity():
    """检查配置文件完整性"""
    print("\n⚙️  配置完整性检查")
    print("-" * 40)
    
    # 检查config.json
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        required_keys = ['tp_percent', 'sl_percent']
        for key in required_keys:
            if key in config:
                print(f"✅ config.json:{key} = {config[key]}%")
            else:
                print(f"❌ config.json:{key} 缺失")
                
    except Exception as e:
        print(f"❌ config.json 读取失败: {str(e)}")
    
    # 检查trading_settings.json
    try:
        with open('trading_settings.json', 'r') as f:
            settings = json.load(f)
        
        if 'tech_settings' in settings:
            print("✅ trading_settings.json:技术指标配置存在")
        else:
            print("❌ trading_settings.json:技术指标配置缺失")
            
    except Exception as e:
        print(f"❌ trading_settings.json 读取失败: {str(e)}")

def check_code_functions():
    """检查代码中的关键函数"""
    print("\n🔧 代码函数检查")
    print("-" * 40)
    
    if not os.path.exists('main_window.py'):
        print("❌ main_window.py 不存在")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    critical_functions = [
        'make_robust_request',
        'apply_tp_sl_settings', 
        'place_ai_order',
        'get_trading_signal',
        'auto_trading_loop'
    ]
    
    all_functions_exist = True
    for func in critical_functions:
        if f'def {func}' in content:
            print(f"✅ {func}(): 存在")
        else:
            print(f"❌ {func}(): 缺失")
            all_functions_exist = False
    
    return all_functions_exist

def check_recent_fixes():
    """检查最近的修复是否已应用"""
    print("\n🛠️  修复状态检查")
    print("-" * 40)
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    fixes = [
        ('保证金检查', 'required_margin', '自动保证金检查功能'),
        ('网络超时优化', 'timeout=(15, 45)', '网络请求超时优化'),
        ('保证金错误处理', 'Margin is insufficient', '保证金不足错误处理'),
        ('强大请求方法', 'make_robust_request', '增强的网络请求方法')
    ]
    
    for fix_name, pattern, description in fixes:
        if pattern in content:
            print(f"✅ {fix_name}: {description}")
        else:
            print(f"❌ {fix_name}: 未应用 - {description}")

def check_env_variables():
    """检查环境变量"""
    print("\n🔑 环境变量检查")
    print("-" * 40)
    
    if not os.path.exists('.env'):
        print("❌ .env文件不存在")
        return False
    
    # 读取.env文件
    env_vars = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                env_vars[key] = value
    
    required_vars = [
        'BINANCE_API_KEY',
        'BINANCE_SECRET_KEY', 
        'NEWS_API_KEY',
        'DEEPSEEK_API_KEY'
    ]
    
    all_vars_set = True
    for var in required_vars:
        if var in env_vars and env_vars[var]:
            print(f"✅ {var}: 已设置 ({env_vars[var][:10]}...)")
        else:
            print(f"❌ {var}: 未设置")
            all_vars_set = False
    
    return all_vars_set

def check_potential_issues():
    """检查潜在问题"""
    print("\n⚠️  潜在问题检查")
    print("-" * 40)
    
    issues = []
    
    # 检查文件大小
    if os.path.exists('main_window.py'):
        size = os.path.getsize('main_window.py')
        if size > 500000:  # 500KB
            issues.append("main_window.py文件过大，可能影响性能")
        elif size < 100000:  # 100KB
            issues.append("main_window.py文件过小，可能缺少功能")
    
    # 检查日志文件
    if os.path.exists('app_error.log'):
        size = os.path.getsize('app_error.log')
        if size > 10000000:  # 10MB
            issues.append("错误日志文件过大，建议清理")
    
    # 检查缓存文件
    if os.path.exists('__pycache__'):
        cache_files = os.listdir('__pycache__')
        if len(cache_files) > 20:
            issues.append("Python缓存文件过多，建议清理")
    
    if issues:
        for issue in issues:
            print(f"⚠️  {issue}")
    else:
        print("✅ 未发现明显问题")
    
    return len(issues) == 0

def generate_recommendations():
    """生成优化建议"""
    print("\n💡 优化建议")
    print("-" * 40)
    
    recommendations = [
        "1. 定期备份配置文件和重要数据",
        "2. 监控API配额使用情况",
        "3. 保持适当的账户余额缓冲",
        "4. 定期检查和更新依赖包",
        "5. 使用小额资金进行测试",
        "6. 定期清理日志和缓存文件",
        "7. 监控系统性能和内存使用",
        "8. 保持网络连接稳定"
    ]
    
    for rec in recommendations:
        print(rec)

def main():
    """主检查函数"""
    print("🔍 币安量化交易机器人 - 快速系统检查")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行检查
    checks = []
    checks.append(("文件完整性", check_files()))
    checks.append(("配置完整性", check_config_integrity()))
    checks.append(("代码函数", check_code_functions()))
    checks.append(("环境变量", check_env_variables()))
    checks.append(("潜在问题", check_potential_issues()))
    
    # 检查修复状态
    check_recent_fixes()
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 检查结果汇总")
    print("=" * 60)
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    for check_name, result in checks:
        status = "✅ 通过" if result else "❌ 需要关注"
        print(f"{check_name}: {status}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 系统状态优秀！")
        print("✨ 所有关键组件都正常工作")
        print("🚀 可以安全使用交易功能")
    elif passed >= total * 0.8:
        print("\n👍 系统状态良好")
        print("🔧 有少量问题需要关注")
        print("💡 建议查看具体问题并修复")
    else:
        print("\n⚠️  系统需要优化")
        print("🛠️  请优先修复标记的问题")
        print("📞 如需帮助请查看相关文档")
    
    # 生成建议
    generate_recommendations()
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
