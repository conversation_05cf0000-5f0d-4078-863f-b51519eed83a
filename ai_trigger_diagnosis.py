#!/usr/bin/env python3
"""
AI触发分析诊断工具
专门用于诊断和修复AI触发分析功能的问题
"""

import os
import json
import sys
from datetime import datetime

def check_main_window_code():
    """检查main_window.py中的AI触发分析代码"""
    print("=== 代码检查 ===")
    
    if not os.path.exists('main_window.py'):
        print("✗ main_window.py文件不存在")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键代码片段
    checks = [
        {
            'name': '触发阈值获取',
            'pattern': 'trigger_threshold = self.trigger_threshold_spinbox.value()',
            'description': '正确获取触发阈值'
        },
        {
            'name': 'AI分析调用',
            'pattern': 'analysis_result = self.get_trading_signal(base_symbol)',
            'description': '调用AI分析函数'
        },
        {
            'name': '价格变化计算',
            'pattern': 'price_change = abs((current_price - self.last_trigger_price) / self.last_trigger_price * 100)',
            'description': '计算价格变化百分比'
        },
        {
            'name': '触发条件判断',
            'pattern': 'if price_change >= trigger_threshold:',
            'description': '判断是否达到触发条件'
        },
        {
            'name': '自动交易循环',
            'pattern': 'def auto_trading_loop(self):',
            'description': '自动交易主循环函数'
        }
    ]
    
    all_passed = True
    for check in checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    # 检查是否有错误的ADX阈值使用
    if 'trigger_threshold = getattr(self, \'adx_threshold\', 25)' in content:
        print("✗ 发现错误: 使用了ADX阈值而不是触发阈值")
        all_passed = False
    else:
        print("✓ 触发阈值使用正确")
    
    return all_passed

def check_ui_components():
    """检查UI组件是否正确初始化"""
    print("\n=== UI组件检查 ===")
    
    if not os.path.exists('main_window.py'):
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    ui_checks = [
        {
            'name': '触发阈值输入框',
            'pattern': 'self.trigger_threshold_spinbox = QDoubleSpinBox()',
            'description': '触发阈值设置控件'
        },
        {
            'name': '自动交易按钮',
            'pattern': 'self.auto_trading_button = QPushButton',
            'description': '自动交易控制按钮'
        },
        {
            'name': 'AI状态显示',
            'pattern': 'self.trigger_status_label',
            'description': 'AI触发状态显示标签'
        },
        {
            'name': '波动显示',
            'pattern': 'self.current_volatility_label',
            'description': '当前波动显示标签'
        }
    ]
    
    all_passed = True
    for check in ui_checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def check_common_issues():
    """检查常见问题"""
    print("\n=== 常见问题检查 ===")
    
    issues = []
    
    # 检查配置文件
    if os.path.exists('indicator_settings.json'):
        with open('indicator_settings.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            adx_threshold = config.get('adx_threshold', 25)
            if adx_threshold > 5:  # ADX阈值过高
                issues.append(f"ADX阈值可能过高: {adx_threshold} (建议25-40)")
    
    # 检查环境变量
    if not os.path.exists('.env'):
        issues.append("缺少.env环境变量文件")
    else:
        with open('.env', 'r') as f:
            env_content = f.read()
            if 'DEEPSEEK_API_KEY=' not in env_content:
                issues.append("缺少DeepSeek API密钥")
            if 'NEWS_API_KEY=' not in env_content:
                issues.append("缺少News API密钥")
    
    if issues:
        print("发现以下问题:")
        for issue in issues:
            print(f"⚠️  {issue}")
        return False
    else:
        print("✓ 未发现常见问题")
        return True

def generate_fix_suggestions():
    """生成修复建议"""
    print("\n=== 修复建议 ===")
    
    suggestions = [
        "1. 确保已修复触发阈值获取问题（使用trigger_threshold_spinbox.value()）",
        "2. 检查自动交易是否已启动（点击'启动自动交易'按钮）",
        "3. 验证触发阈值设置是否合理（建议0.1%-1.0%）",
        "4. 确认所有API密钥都已正确配置",
        "5. 观察交易日志中是否有触发分析的记录",
        "6. 检查网络连接状态（状态栏显示）",
        "7. 验证价格波动是否达到设定的触发条件"
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def create_test_scenario():
    """创建测试场景"""
    print("\n=== 创建测试场景 ===")
    
    test_config = {
        "trigger_threshold": 0.25,
        "test_prices": [
            {"name": "初始价格", "price": 50000.0},
            {"name": "小幅上涨", "price": 50050.0, "change": 0.1},
            {"name": "达到阈值", "price": 50125.0, "change": 0.25},
            {"name": "大幅波动", "price": 51000.0, "change": 2.0}
        ]
    }
    
    print("测试场景配置:")
    print(f"触发阈值: {test_config['trigger_threshold']}%")
    print("\n价格变化测试:")
    
    last_price = test_config['test_prices'][0]['price']
    
    for i, scenario in enumerate(test_config['test_prices']):
        if i == 0:
            print(f"  {scenario['name']}: ${scenario['price']}")
            continue
        
        current_price = scenario['price']
        actual_change = abs((current_price - last_price) / last_price * 100)
        should_trigger = actual_change >= test_config['trigger_threshold']
        
        print(f"  {scenario['name']}: ${current_price} ({actual_change:.2f}%) {'✓触发' if should_trigger else '✗不触发'}")

def main():
    """主函数"""
    print("AI触发分析诊断工具")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    code_ok = check_main_window_code()
    ui_ok = check_ui_components()
    config_ok = check_common_issues()
    
    # 生成修复建议
    generate_fix_suggestions()
    
    # 创建测试场景
    create_test_scenario()
    
    # 总结
    print("\n=== 诊断总结 ===")
    print(f"代码检查: {'✓' if code_ok else '✗'}")
    print(f"UI组件: {'✓' if ui_ok else '✗'}")
    print(f"配置检查: {'✓' if config_ok else '✗'}")
    
    if code_ok and ui_ok and config_ok:
        print("\n🎉 AI触发分析功能代码正常！")
        print("如果仍有问题，请检查:")
        print("- 是否启动了自动交易")
        print("- 触发阈值是否设置得太高")
        print("- 市场波动是否足够大")
    else:
        print("\n⚠️  发现问题，请根据上述检查结果进行修复")

if __name__ == "__main__":
    main()
