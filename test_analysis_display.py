#!/usr/bin/env python3
"""
测试AI分析结果显示功能
验证新闻内容和指标信息是否正确显示
"""

import os
import re
from datetime import datetime

def check_analysis_display_code():
    """检查分析结果显示代码"""
    print("=== 分析结果显示代码检查 ===")
    
    if not os.path.exists('main_window.py'):
        print("✗ main_window.py文件不存在")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查显示功能
    display_checks = [
        {
            'name': '新闻分析详情显示',
            'pattern': '📰 新闻分析详情:',
            'description': '显示新闻分析详情标题'
        },
        {
            'name': '新闻情感得分显示',
            'pattern': 'f"新闻情感得分: {avg_news_sentiment:.2f}',
            'description': '显示新闻情感得分'
        },
        {
            'name': '重要新闻摘要显示',
            'pattern': '重要新闻摘要:',
            'description': '显示重要新闻摘要'
        },
        {
            'name': '技术指标详情显示',
            'pattern': '📊 技术指标详情:',
            'description': '显示技术指标详情标题'
        },
        {
            'name': 'RSI指标显示',
            'pattern': 'f"RSI({getattr(self, \'rsi_period\', 14)}): {rsi[-1]:.2f}',
            'description': '显示RSI指标详情'
        },
        {
            'name': 'MACD指标显示',
            'pattern': 'f"MACD: {macd[-1]:.2f}',
            'description': '显示MACD指标详情'
        },
        {
            'name': '布林带指标显示',
            'pattern': 'f"布林带: 上轨{upper[-1]:.2f}',
            'description': '显示布林带指标详情'
        },
        {
            'name': 'EMA指标显示',
            'pattern': 'f"EMA: 短期{ema20[-1]:.2f}',
            'description': '显示EMA指标详情'
        },
        {
            'name': 'ADX指标显示',
            'pattern': 'f"ADX: {adx[-1]:.2f}',
            'description': '显示ADX指标详情'
        },
        {
            'name': '关键技术信号显示',
            'pattern': '🎯 关键技术信号:',
            'description': '显示关键技术信号'
        },
        {
            'name': 'AI完整分析显示',
            'pattern': '🤖 AI完整分析:',
            'description': '显示AI完整分析内容'
        },
        {
            'name': '交易决策详情显示',
            'pattern': '🎯 交易决策详情:',
            'description': '显示交易决策详情'
        }
    ]
    
    all_passed = True
    for check in display_checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def check_news_content_processing():
    """检查新闻内容处理"""
    print("\n=== 新闻内容处理检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查新闻处理功能
    news_checks = [
        {
            'name': '新闻摘要生成',
            'pattern': 'news_summary = "最新加密货币相关新闻摘要：\\n\\n"',
            'description': '生成新闻摘要'
        },
        {
            'name': '新闻标题添加',
            'pattern': 'news_summary += f"- {title}\\n"',
            'description': '添加新闻标题到摘要'
        },
        {
            'name': '新闻描述添加',
            'pattern': 'news_summary += f"  摘要: {description[:200]}...\\n"',
            'description': '添加新闻描述到摘要'
        },
        {
            'name': '新闻情感标注',
            'pattern': 'news_summary += f"  情感倾向: {\'看多\' if sentiment_score > 0',
            'description': '标注新闻情感倾向'
        },
        {
            'name': '新闻传递给AI',
            'pattern': '{news_summary}',
            'description': '将新闻摘要传递给AI分析'
        }
    ]
    
    all_passed = True
    for check in news_checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def check_indicator_info_display():
    """检查指标信息显示"""
    print("\n=== 指标信息显示检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查AI提示中的指标信息
    indicator_info_checks = [
        {
            'name': '市场状态显示',
            'pattern': '**判定市场状态**：{market_state}',
            'description': '在AI提示中显示市场状态'
        },
        {
            'name': 'RSI信息显示',
            'pattern': 'RSI({getattr(self, \'rsi_period\', 14)}): {rsi[-1]:.2f}',
            'description': '在AI提示中显示RSI信息'
        },
        {
            'name': 'MACD信息显示',
            'pattern': 'MACD({getattr(self, \'macd_fast\', 12)},{getattr(self, \'macd_slow\', 26)}',
            'description': '在AI提示中显示MACD信息'
        },
        {
            'name': '布林带信息显示',
            'pattern': '布林带({getattr(self, \'bb_period\', 20)},{getattr(self, \'bb_std\', 2.0)})',
            'description': '在AI提示中显示布林带信息'
        },
        {
            'name': 'EMA信息显示',
            'pattern': 'EMA: {getattr(self, \'ema_short\', 20)}日 {ema20[-1]:.2f}',
            'description': '在AI提示中显示EMA信息'
        },
        {
            'name': '随机指标信息显示',
            'pattern': '随机指标: K({stoch_k[-1]:.2f}) D({stoch_d[-1]:.2f})',
            'description': '在AI提示中显示随机指标信息'
        },
        {
            'name': 'CCI信息显示',
            'pattern': 'CCI({getattr(self, \'cci_period\', 20)}): {cci[-1]:.2f}',
            'description': '在AI提示中显示CCI信息'
        },
        {
            'name': 'MFI信息显示',
            'pattern': 'MFI({getattr(self, \'mfi_period\', 14)}): {mfi[-1]:.2f}',
            'description': '在AI提示中显示MFI信息'
        }
    ]
    
    all_passed = True
    for check in indicator_info_checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def check_analysis_result_structure():
    """检查分析结果数据结构"""
    print("\n=== 分析结果数据结构检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查返回的分析结果结构
    result_structure_checks = [
        {
            'name': '基础交易信息',
            'pattern': "'trend': trend_match.group(1)",
            'description': '包含交易趋势信息'
        },
        {
            'name': '价格信息',
            'pattern': "'entry_price': entry_match.group(1)",
            'description': '包含入场价格信息'
        },
        {
            'name': 'AI完整分析',
            'pattern': "'analysis': analysis",
            'description': '包含AI完整分析内容'
        },
        {
            'name': '市场状态',
            'pattern': "'market_state': market_state",
            'description': '包含市场状态信息'
        },
        {
            'name': '趋势强度',
            'pattern': "'trend_strength': trend_strength",
            'description': '包含趋势强度信息'
        },
        {
            'name': '技术信号',
            'pattern': "'signals': trend_signals",
            'description': '包含技术信号列表'
        },
        {
            'name': '新闻情感',
            'pattern': "'sentiment_score': avg_news_sentiment",
            'description': '包含新闻情感得分'
        },
        {
            'name': 'AI建议',
            'pattern': "'ai_recommendation': trend_match.group(1)",
            'description': '包含AI建议信息'
        }
    ]
    
    all_passed = True
    for check in result_structure_checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def analyze_display_improvements():
    """分析显示改进效果"""
    print("\n=== 显示改进效果分析 ===")
    
    improvements = [
        "✅ 新闻分析详情: 显示新闻情感得分和趋势判断",
        "✅ 重要新闻摘要: 显示前5条重要新闻的标题和描述",
        "✅ 技术指标详情: 显示所有主要技术指标的当前值和状态",
        "✅ 关键技术信号: 显示前8个重要的技术分析信号",
        "✅ AI完整分析: 显示AI的完整分析内容",
        "✅ 交易决策详情: 在交易执行时显示详细的决策依据",
        "✅ 支持信号显示: 显示支持交易决策的关键信号",
        "✅ 新闻情感影响: 显示新闻情感对交易决策的影响"
    ]
    
    print("已实现的显示改进:")
    for improvement in improvements:
        print(f"  {improvement}")
    
    print("\n显示内容结构:")
    structure = [
        "1. 基础分析结果 (趋势、市场状态、强度)",
        "2. 新闻分析详情 (情感得分、重要新闻摘要)",
        "3. 技术指标详情 (RSI、MACD、布林带、EMA、ADX等)",
        "4. 关键技术信号 (前8个重要信号)",
        "5. AI完整分析 (AI的详细分析内容)",
        "6. 交易决策详情 (执行交易时的详细信息)"
    ]
    
    for item in structure:
        print(f"  {item}")

def generate_usage_examples():
    """生成使用示例"""
    print("\n=== 使用示例 ===")
    
    print("分析结果显示示例:")
    print("=" * 50)
    print("📰 新闻分析详情:")
    print("新闻情感得分: 1.2 (看多)")
    print("重要新闻摘要:")
    print("1. Bitcoin ETF获得SEC批准，市场情绪乐观")
    print("   美国证券交易委员会正式批准了多只比特币现货ETF...")
    print("2. 特朗普表示支持加密货币发展")
    print("   前总统特朗普在最新讲话中表达了对加密货币的支持...")
    print()
    print("📊 技术指标详情:")
    print("RSI(14): 65.32 (正常)")
    print("MACD: 1250.45 (金叉)")
    print("布林带: 上轨51200.00, 中轨50000.00, 下轨48800.00")
    print("EMA: 短期50100.00, 中期49800.00")
    print("ADX: 32.50 (+DI:25.30, -DI:18.70)")
    print()
    print("🎯 关键技术信号:")
    print("  • MACD金叉，强上涨趋势中，强烈看多")
    print("  • 价格突破布林上轨，强上涨趋势持续")
    print("  • EMA短穿中(金叉)，强上涨趋势中，看多")
    print("  • ADX极强上涨 (32.50)")
    print()
    print("🤖 AI完整分析:")
    print("基于当前技术指标和新闻面分析，市场呈现强烈的看多信号...")
    print("=" * 50)

def main():
    """主函数"""
    print("AI分析结果显示功能测试")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    display_ok = check_analysis_display_code()
    news_ok = check_news_content_processing()
    indicator_ok = check_indicator_info_display()
    structure_ok = check_analysis_result_structure()
    
    # 分析改进效果
    analyze_display_improvements()
    
    # 生成使用示例
    generate_usage_examples()
    
    # 总结
    print("\n=== 检查总结 ===")
    print(f"分析结果显示: {'✓' if display_ok else '✗'}")
    print(f"新闻内容处理: {'✓' if news_ok else '✗'}")
    print(f"指标信息显示: {'✓' if indicator_ok else '✗'}")
    print(f"结果数据结构: {'✓' if structure_ok else '✗'}")
    
    overall_status = display_ok and news_ok and indicator_ok and structure_ok
    
    if overall_status:
        print("\n🎉 分析结果显示功能完全正常！")
        print("\n主要特性:")
        print("✓ 详细的新闻分析显示")
        print("✓ 完整的技术指标信息")
        print("✓ 关键信号突出显示")
        print("✓ AI完整分析内容")
        print("✓ 交易决策详细说明")
        print("✓ 中文友好的显示格式")
        
        print("\n现在AI分析结果将显示:")
        print("• 新闻情感分析和重要新闻摘要")
        print("• 所有技术指标的详细数值和状态")
        print("• 支持决策的关键技术信号")
        print("• AI的完整分析推理过程")
        print("• 交易执行时的详细决策依据")
        
    else:
        print("\n⚠️  分析结果显示功能存在问题")
        print("请检查上述失败项目")

if __name__ == "__main__":
    main()
