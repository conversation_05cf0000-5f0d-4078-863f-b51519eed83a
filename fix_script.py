#!/usr/bin/env python3
"""
main_window.py 修复脚本

该脚本用于修复main_window.py中的网络检查功能中的格式问题，
删除额外的空行以确保代码格式正确。

作者: [李兴]
版本: 1.0.0
创建日期: 2024
"""

from typing import TextIO


def main() -> None:
    """
    主函数，读取文件内容，修复格式问题后重新写入
    """
    # 读取文件内容
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content: str = f.read()
    
    # 修复格式问题 - 删除额外的空行
    fixed_content: str = content.replace('            \n    \n    def', '            \n\n    def')

    # 写入修复后的内容
    with open('main_window.py', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print('文件格式已修复')


if __name__ == "__main__":
    main() 