#!/usr/bin/env python3
"""
币安量化交易机器人 - 全面系统检查
检查所有功能模块和潜在问题
"""

import os
import sys
import json
import time
import ccxt
import requests
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

class SystemChecker:
    def __init__(self):
        self.results = {}
        self.issues = []
        self.warnings = []
        
    def log_result(self, test_name, passed, message="", level="info"):
        """记录测试结果"""
        self.results[test_name] = passed
        if not passed:
            if level == "error":
                self.issues.append(f"{test_name}: {message}")
            else:
                self.warnings.append(f"{test_name}: {message}")
        print(f"{'✅' if passed else '❌'} {test_name}: {message}")

    def check_environment(self):
        """检查环境配置"""
        print("🔧 环境配置检查")
        print("-" * 50)
        
        # 检查.env文件
        if os.path.exists('.env'):
            load_dotenv()
            self.log_result("环境文件", True, ".env文件存在")
            
            # 检查必要的API密钥
            required_keys = ['BINANCE_API_KEY', 'BINANCE_SECRET_KEY', 'NEWS_API_KEY', 'DEEPSEEK_API_KEY']
            for key in required_keys:
                value = os.getenv(key)
                if value:
                    self.log_result(f"{key}", True, f"已配置 ({value[:10]}...)")
                else:
                    self.log_result(f"{key}", False, "未配置", "error")
        else:
            self.log_result("环境文件", False, ".env文件不存在", "error")

    def check_config_files(self):
        """检查配置文件"""
        print("\n📁 配置文件检查")
        print("-" * 50)
        
        config_files = {
            'config.json': ['tp_percent', 'sl_percent'],
            'trading_settings.json': ['tp_percent', 'sl_percent']
        }
        
        for file_name, required_keys in config_files.items():
            if os.path.exists(file_name):
                try:
                    with open(file_name, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    self.log_result(f"{file_name}格式", True, "JSON格式正确")
                    
                    for key in required_keys:
                        if key in config:
                            self.log_result(f"{file_name}:{key}", True, f"{config[key]}%")
                        else:
                            self.log_result(f"{file_name}:{key}", False, "缺失配置", "warning")
                            
                except Exception as e:
                    self.log_result(f"{file_name}格式", False, f"JSON解析失败: {str(e)}", "error")
            else:
                self.log_result(f"{file_name}存在", False, "文件不存在", "warning")

    def check_python_modules(self):
        """检查Python模块"""
        print("\n🐍 Python模块检查")
        print("-" * 50)
        
        required_modules = [
            'ccxt', 'requests', 'pandas', 'numpy', 'talib',
            'PyQt6', 'matplotlib', 'dotenv'
        ]
        
        for module in required_modules:
            try:
                __import__(module)
                self.log_result(f"模块:{module}", True, "已安装")
            except ImportError:
                self.log_result(f"模块:{module}", False, "未安装", "error")

    def check_main_window_code(self):
        """检查主窗口代码完整性"""
        print("\n📄 主窗口代码检查")
        print("-" * 50)
        
        if not os.path.exists('main_window.py'):
            self.log_result("主窗口文件", False, "main_window.py不存在", "error")
            return
        
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键函数
        key_functions = [
            'make_robust_request',
            'apply_tp_sl_settings', 
            'test_news_api',
            'get_trading_signal',
            'place_ai_order',
            'auto_trading_loop'
        ]
        
        for func in key_functions:
            if f'def {func}' in content:
                self.log_result(f"函数:{func}", True, "存在")
            else:
                self.log_result(f"函数:{func}", False, "缺失", "error")
        
        # 检查关键修复
        fixes_to_check = [
            ('保证金检查', 'required_margin'),
            ('超时优化', 'timeout=(15, 45)'),
            ('错误处理', 'Margin is insufficient'),
            ('重试机制', 'make_robust_request')
        ]
        
        for fix_name, pattern in fixes_to_check:
            if pattern in content:
                self.log_result(f"修复:{fix_name}", True, "已应用")
            else:
                self.log_result(f"修复:{fix_name}", False, "未应用", "warning")

    def check_api_connections(self):
        """检查API连接"""
        print("\n🌐 API连接检查")
        print("-" * 50)
        
        # 检查币安API
        try:
            api_key = os.getenv('BINANCE_API_KEY')
            secret_key = os.getenv('BINANCE_SECRET_KEY')
            
            if api_key and secret_key:
                exchange = ccxt.binance({
                    'apiKey': api_key,
                    'secret': secret_key,
                    'enableRateLimit': True,
                    'options': {'adjustForTimeDifference': True}
                })
                
                balance = exchange.fetch_balance()
                self.log_result("币安API连接", True, "连接成功")
                
                # 检查期货账户
                exchange.options['defaultType'] = 'future'
                future_balance = exchange.fetch_balance()
                usdt_balance = future_balance.get('USDT', {}).get('free', 0)
                
                if usdt_balance >= 10:
                    self.log_result("期货账户余额", True, f"${usdt_balance:.2f}")
                else:
                    self.log_result("期货账户余额", False, f"余额不足: ${usdt_balance:.2f}", "warning")
                    
            else:
                self.log_result("币安API连接", False, "API密钥未配置", "error")
                
        except Exception as e:
            self.log_result("币安API连接", False, f"连接失败: {str(e)}", "error")
        
        # 检查News API
        try:
            news_api_key = os.getenv('NEWS_API_KEY')
            if news_api_key:
                url = 'https://newsapi.org/v2/everything'
                params = {
                    'q': 'bitcoin',
                    'pageSize': 1,
                    'apiKey': news_api_key
                }
                response = requests.get(url, params=params, timeout=(15, 45))
                
                if response.status_code == 200:
                    self.log_result("News API连接", True, "连接成功")
                else:
                    self.log_result("News API连接", False, f"HTTP {response.status_code}", "error")
            else:
                self.log_result("News API连接", False, "API密钥未配置", "warning")
                
        except Exception as e:
            self.log_result("News API连接", False, f"连接失败: {str(e)}", "error")
        
        # 检查DeepSeek API
        try:
            deepseek_key = os.getenv('DEEPSEEK_API_KEY')
            if deepseek_key:
                url = "https://api.deepseek.com/v1/chat/completions"
                headers = {
                    'Authorization': f'Bearer {deepseek_key}',
                    'Content-Type': 'application/json'
                }
                data = {
                    "model": "deepseek-chat",
                    "messages": [{"role": "user", "content": "test"}],
                    "max_tokens": 5
                }
                response = requests.post(url, json=data, headers=headers, timeout=(15, 45))
                
                if response.status_code == 200:
                    self.log_result("DeepSeek API连接", True, "连接成功")
                else:
                    self.log_result("DeepSeek API连接", False, f"HTTP {response.status_code}", "error")
            else:
                self.log_result("DeepSeek API连接", False, "API密钥未配置", "warning")
                
        except Exception as e:
            self.log_result("DeepSeek API连接", False, f"连接失败: {str(e)}", "error")

    def check_trading_logic(self):
        """检查交易逻辑"""
        print("\n💹 交易逻辑检查")
        print("-" * 50)
        
        if not os.path.exists('main_window.py'):
            return
        
        with open('main_window.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查交易相关逻辑
        trading_checks = [
            ('止盈止损计算', 'tp_price_to_use'),
            ('订单创建', 'create_order'),
            ('杠杆设置', 'set_leverage'),
            ('保证金检查', 'required_margin'),
            ('错误处理', 'except Exception'),
            ('日志记录', 'log_trading')
        ]
        
        for check_name, pattern in trading_checks:
            count = content.count(pattern)
            if count > 0:
                self.log_result(f"交易逻辑:{check_name}", True, f"使用{count}次")
            else:
                self.log_result(f"交易逻辑:{check_name}", False, "未找到", "warning")

    def check_file_permissions(self):
        """检查文件权限"""
        print("\n🔐 文件权限检查")
        print("-" * 50)
        
        important_files = [
            'main_window.py', 'config.json', 'trading_settings.json', '.env'
        ]
        
        for file_name in important_files:
            if os.path.exists(file_name):
                try:
                    # 测试读权限
                    with open(file_name, 'r') as f:
                        f.read(1)
                    
                    # 测试写权限
                    with open(file_name, 'a') as f:
                        pass
                    
                    self.log_result(f"权限:{file_name}", True, "读写正常")
                except Exception as e:
                    self.log_result(f"权限:{file_name}", False, f"权限问题: {str(e)}", "error")

    def check_network_connectivity(self):
        """检查网络连接"""
        print("\n🌍 网络连接检查")
        print("-" * 50)
        
        test_urls = [
            ('币安API', 'https://api.binance.com/api/v3/ping'),
            ('NewsAPI', 'https://newsapi.org'),
            ('DeepSeek', 'https://api.deepseek.com'),
            ('Google DNS', 'https://8.8.8.8')
        ]
        
        for name, url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code < 400:
                    self.log_result(f"网络:{name}", True, f"连接正常 ({response.status_code})")
                else:
                    self.log_result(f"网络:{name}", False, f"HTTP {response.status_code}", "warning")
            except Exception as e:
                self.log_result(f"网络:{name}", False, f"连接失败: {str(e)}", "error")

    def generate_report(self):
        """生成检查报告"""
        print("\n" + "="*70)
        print("📊 全面系统检查报告")
        print("="*70)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result)
        
        print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总检查项: {total_tests}")
        print(f"通过项目: {passed_tests}")
        print(f"失败项目: {total_tests - passed_tests}")
        print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        # 显示问题
        if self.issues:
            print(f"\n🚨 严重问题 ({len(self.issues)}项):")
            for issue in self.issues:
                print(f"   ❌ {issue}")
        
        if self.warnings:
            print(f"\n⚠️  警告 ({len(self.warnings)}项):")
            for warning in self.warnings:
                print(f"   ⚠️  {warning}")
        
        # 生成建议
        print(f"\n💡 优化建议:")
        if passed_tests == total_tests:
            print("   🎉 系统状态优秀！所有检查都通过了。")
            print("   ✨ 可以安全使用所有功能。")
        elif passed_tests / total_tests >= 0.8:
            print("   👍 系统状态良好，有少量问题需要关注。")
            print("   🔧 建议修复警告项目以获得最佳性能。")
        else:
            print("   ⚠️  系统存在较多问题，建议优先修复严重问题。")
            print("   🛠️  请按照错误提示逐项修复。")
        
        return passed_tests / total_tests

def main():
    """主检查函数"""
    print("🔍 币安量化交易机器人 - 全面系统检查")
    print("="*70)
    
    checker = SystemChecker()
    
    # 执行所有检查
    checker.check_environment()
    checker.check_config_files()
    checker.check_python_modules()
    checker.check_main_window_code()
    checker.check_api_connections()
    checker.check_trading_logic()
    checker.check_file_permissions()
    checker.check_network_connectivity()
    
    # 生成报告
    success_rate = checker.generate_report()
    
    # 返回状态码
    return 0 if success_rate >= 0.8 else 1

if __name__ == "__main__":
    sys.exit(main())
