"""
币安量化交易机器人主窗口模块

该模块实现了一个基于PyQt6的图形界面量化交易机器人，支持：
- 实时市场数据显示和K线图表
- AI驱动的交易信号分析
- 自动化交易执行和风险管理
- 账户资产和持仓管理
- 技术指标分析
- 新闻情感分析

作者: [李兴]
版本: 2.0.0
创建日期: 2024
"""

import sys
import os
import json
import time
import threading
import re
import gc
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple, Union, cast, Callable

# 导入ADX交易策略
from adx_trading_strategy import ADXTradingStrategy, create_adx_strategy, validate_adx_signal

# PyQt6 imports
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QComboBox, QDoubleSpinBox,
    QTableWidget, QTableWidgetItem, QTabWidget,
    QGridLayout, QFrame, QHeaderView, QApplication,
    QMessageBox, QSplitter, QFormLayout, QLineEdit,
    QGroupBox, QTextEdit, QSpinBox, QDialog,
    QScrollArea, QStatusBar, QRadioButton, QCheckBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject, QRunnable, QThreadPool
from PyQt6.QtGui import QFont, QColor, QTextCursor

# Third-party imports
import ccxt
from ccxt import OrderNotFound
from dotenv import load_dotenv
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import pandas as pd
import talib

# Local imports
from chart_widget import ChartWidget

class CachedData:
    """
    缓存数据类，用于存储带有时间戳的数据并检查有效性

    Attributes:
        data: 缓存的数据，可以是任意类型
        timestamp: 数据的时间戳，用于判断数据是否过期
    """

    def __init__(self, data: Any = None) -> None:
        """
        初始化缓存数据对象

        Args:
            data: 要缓存的数据，默认为None
        """
        self.data: Any = data
        self.timestamp: Optional[datetime] = datetime.now() if data else None

    def is_valid(self, ttl_seconds: int = 120) -> bool:
        """
        检查缓存数据是否仍然有效

        Args:
            ttl_seconds: 数据的生存时间（秒），默认120秒（优化：延长缓存时间）

        Returns:
            bool: 如果数据仍然有效返回True，否则返回False
        """
        if not self.timestamp:
            return False
        return (datetime.now() - self.timestamp).seconds < ttl_seconds

class MainWindow(QMainWindow):
    """
    币安量化交易机器人主窗口类

    该类实现了一个完整的量化交易界面，包括：
    - 实时市场数据显示
    - K线图表分析
    - AI交易信号生成
    - 自动化交易执行
    - 账户管理和风险控制

    Signals:
        update_log_signal: 更新交易日志的信号
        show_message_signal: 显示消息框的信号
        network_status_signal: 网络状态更新信号
    """

    # 信号定义
    update_log_signal = pyqtSignal(str)  # 日志更新信号
    show_message_signal = pyqtSignal(str, str, str)  # (title, message, type)
    network_status_signal = pyqtSignal(str, bool)    # (api_name, is_connected)

    def __init__(self) -> None:
        """
        初始化主窗口

        设置UI界面、加载配置、初始化交易所连接等
        """
        super().__init__()
        # 加载环境变量
        load_dotenv()
        self.api_key: Optional[str] = os.getenv('BINANCE_API_KEY')
        self.secret_key: Optional[str] = os.getenv('BINANCE_SECRET_KEY')
        self.passphrase: Optional[str] = os.getenv('BINANCE_PASSPHRASE')

        # 初始化线程锁
        self.log_lock: threading.Lock = threading.Lock()
        self.update_lock: threading.Lock = threading.Lock()  # 添加更新锁
        self.trade_lock: threading.Lock = threading.Lock()   # 添加交易锁

        # 初始化交易所API连接
        self.exchange: ccxt.binance = ccxt.binance({
            'apiKey': self.api_key,
            'secret': self.secret_key,
            'password': self.passphrase,
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',  # 设置默认为期货/合约交易
                'adjustForTimeDifference': True,
                'warnOnFetchOpenOrdersWithoutSymbol': False,  # 禁用无Symbol获取订单的警告
                'recvWindow': 10000  # 增加接收窗口时间，减少时间同步问题
            }
        })

        # 初始化外部API密钥（存储实际的API密钥值）
        self.news_api_key_value: Optional[str] = os.getenv('NEWS_API_KEY')
        self.deepseek_api_key_value: Optional[str] = os.getenv('DEEPSEEK_API_KEY')

        # 加载技术指标设置
        self.load_indicator_settings()

        # 初始化ADX交易策略
        self.init_adx_strategy()

        # 设置窗口属性
        self.setWindowTitle("🚀 币安量化交易机器人 - 智能版 v2.0")
        self.setMinimumSize(1500, 950)
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(11, 14, 17, 1.0),
                    stop:0.25 rgba(15, 23, 42, 0.98),
                    stop:0.5 rgba(30, 41, 59, 0.95),
                    stop:0.75 rgba(15, 23, 42, 0.98),
                    stop:1 rgba(11, 14, 17, 1.0));
                border: 2px solid rgba(59, 130, 246, 0.3);
            }
            QLabel {
                color: #F8FAFC;
                font-size: 13px;
                font-weight: 500;
            }
            QWidget {
                background: transparent;
            }

            /* 现代化卡片样式 - 增强版 */
            .modern-card {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.95), stop:1 rgba(15, 23, 42, 0.9));
                border: 2px solid rgba(51, 65, 85, 0.8);
                border-radius: 16px;
            }
            .modern-card:hover {
                border: 2px solid rgba(59, 130, 246, 0.6);
            }

            /* 现代化输入控件 - 增强版 */
            QComboBox, QDoubleSpinBox, QSpinBox {
                padding: 10px 14px;
                border: 2px solid rgba(51, 65, 85, 0.8);
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.9), stop:1 rgba(15, 23, 42, 0.8));
                color: #F8FAFC;
                font-size: 13px;
                font-weight: 600;
                min-height: 28px;
                selection-background-color: #3B82F6;
            }
            QComboBox::drop-down {
                border: none;
                padding-right: 14px;
                width: 28px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:1 rgba(59, 130, 246, 0.1));
                border-radius: 8px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 7px solid #94A3B8;
            }
            QComboBox:hover, QDoubleSpinBox:hover, QSpinBox:hover {
                border: 2px solid rgba(59, 130, 246, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 1.0), stop:1 rgba(51, 65, 85, 0.9));
            }
            QComboBox:focus, QDoubleSpinBox:focus, QSpinBox:focus {
                border: 2px solid #60A5FA;
                outline: none;
            }
            QComboBox QAbstractItemView {
                border: 2px solid rgba(51, 65, 85, 0.8);
                selection-background-color: #3B82F6;
                background: rgba(30, 41, 59, 0.95);
                color: #F8FAFC;
                border-radius: 12px;
                padding: 4px;
            }

            /* 现代化按钮样式 - 增强版 */
            QPushButton {
                padding: 12px 18px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10B981, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 13px;
                font-weight: 700;
                min-height: 36px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34D399, stop:1 #10B981);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
            QPushButton[type="sell"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #EF4444, stop:1 #DC2626);
            }
            QPushButton[type="sell"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F87171, stop:1 #EF4444);
            }
            QPushButton[type="delete"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #EF4444, stop:1 #DC2626);
            }
            QPushButton[type="delete"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F87171, stop:1 #EF4444);
            }
            QPushButton[type="neutral"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #475569, stop:1 #334155);
                color: #F8FAFC;
            }
            QPushButton[type="neutral"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #64748B, stop:1 #475569);
            }
            QPushButton[type="primary"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F59E0B, stop:1 #D97706);
                color: #0F172A;
            }
            QPushButton[type="primary"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FBBF24, stop:1 #F59E0B);
            }
            /* 现代化表格样式 - 增强版 */
            QTableWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.95), stop:1 rgba(15, 23, 42, 0.9));
                color: #F8FAFC;
                gridline-color: rgba(51, 65, 85, 0.6);
                border: 2px solid rgba(51, 65, 85, 0.8);
                border-radius: 16px;
                outline: none;
                selection-background-color: rgba(59, 130, 246, 0.3);
                alternate-background-color: rgba(30, 41, 59, 0.5);
            }
            QTableWidget::item {
                padding: 14px 10px;
                border-bottom: 1px solid rgba(51, 65, 85, 0.4);
                border-right: 1px solid rgba(51, 65, 85, 0.4);
                font-weight: 500;
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.4), stop:1 rgba(29, 78, 216, 0.3));
                color: white;
                border: 1px solid rgba(59, 130, 246, 0.6);
            }
            QTableWidget::item:hover {
                background-color: rgba(59, 130, 246, 0.15);
                border: 1px solid rgba(59, 130, 246, 0.3);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(51, 65, 85, 0.9), stop:1 rgba(30, 41, 59, 0.8));
                color: #CBD5E1;
                padding: 16px 10px;
                border: none;
                border-right: 1px solid rgba(71, 85, 105, 0.6);
                border-bottom: 3px solid #3B82F6;
                font-weight: 700;
                font-size: 13px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2), stop:1 rgba(51, 65, 85, 0.9));
            }

            /* 现代化标签页样式 - 增强版 */
            QTabWidget::pane {
                border: 2px solid rgba(51, 65, 85, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.95), stop:1 rgba(15, 23, 42, 0.9));
                border-radius: 16px;
                margin-top: 12px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(51, 65, 85, 0.8), stop:1 rgba(30, 41, 59, 0.7));
                color: #94A3B8;
                padding: 14px 24px;
                border: none;
                min-width: 120px;
                margin-right: 6px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                font-weight: 600;
                font-size: 13px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1D4ED8);
                color: white;
                border-bottom: 4px solid #60A5FA;
            }
            QTabBar::tab:hover:!selected {
                color: #F8FAFC;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(71, 85, 105, 0.9), stop:1 rgba(51, 65, 85, 0.8));
            }
            /* 现代化分割器样式 */
            QSplitter::handle {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #334155, stop:0.5 #475569, stop:1 #334155);
                height: 3px;
                width: 3px;
                border-radius: 1px;
            }
            QSplitter::handle:horizontal {
                width: 3px;
            }
            QSplitter::handle:vertical {
                height: 3px;
            }
            QSplitter::handle:hover {
                background: #3B82F6;
            }

            /* 现代化分组框样式 */
            QGroupBox {
                border: 2px solid #334155;
                border-radius: 12px;
                margin-top: 1.2em;
                padding-top: 18px;
                color: #F8FAFC;
                font-weight: 600;
                font-size: 14px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.8), stop:1 rgba(15, 23, 42, 0.8));
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px;
                color: #60A5FA;
                background: transparent;
            }
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: rgba(51, 65, 85, 0.3);
                width: 12px;
                margin: 0px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3B82F6, stop:1 #1D4ED8);
                min-height: 30px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #60A5FA, stop:1 #3B82F6);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar:horizontal {
                background: rgba(51, 65, 85, 0.3);
                height: 12px;
                margin: 0px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1D4ED8);
                min-width: 30px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #60A5FA, stop:1 #3B82F6);
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }

            /* 现代化输入框样式 */
            QLineEdit {
                padding: 10px 14px;
                border: 2px solid #334155;
                border-radius: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                color: #F8FAFC;
                font-size: 13px;
                font-weight: 500;
                min-height: 20px;
                selection-background-color: #3B82F6;
            }
            QLineEdit:focus {
                border: 2px solid #60A5FA;
                outline: none;
            }
            QLineEdit:hover {
                border: 2px solid #3B82F6;
            }
            /* 现代化文本编辑器样式 */
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                color: #F8FAFC;
                border: 2px solid #334155;
                border-radius: 12px;
                padding: 16px;
                selection-background-color: #3B82F6;
                font-family: 'Monaco', 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.5;
            }
            QTextEdit:focus {
                border: 2px solid #60A5FA;
            }

            /* 现代化单选按钮样式 */
            QRadioButton {
                color: #F8FAFC;
                font-weight: 500;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 20px;
                height: 20px;
                border-radius: 10px;
                border: 2px solid #334155;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
            }
            QRadioButton::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1D4ED8);
                border: 2px solid #60A5FA;
            }
            QRadioButton::indicator:unchecked:hover {
                border: 2px solid #3B82F6;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #334155, stop:1 #1E293B);
            }

            /* 现代化数字输入框按钮样式 */
            QSpinBox::up-button, QSpinBox::down-button,
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 24px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #334155, stop:1 #1E293B);
                border: none;
                border-radius: 4px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover,
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1D4ED8);
            }
        """)
        
        # 添加状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 网络状态指示器
        self.exchange_status = QLabel("交易所连接: 未知")
        self.exchange_status.setStyleSheet("color: #848E9C;")
        self.news_api_status = QLabel("News API: 未知")
        self.news_api_status.setStyleSheet("color: #848E9C;")
        self.ai_api_status = QLabel("AI API: 未知")
        self.ai_api_status.setStyleSheet("color: #848E9C;")
        
        # 添加手动检查网络按钮
        check_network_btn = QPushButton("检查网络")
        check_network_btn.setProperty("type", "neutral")
        check_network_btn.setFixedSize(90, 26)
        check_network_btn.setStyleSheet("""
            QPushButton {
                padding: 3px 8px;
                font-size: 12px;
                min-height: 0px;
            }
        """)
        check_network_btn.clicked.connect(self.check_network_status)
        
        self.status_bar.addPermanentWidget(self.exchange_status)
        self.status_bar.addPermanentWidget(self.news_api_status)
        self.status_bar.addPermanentWidget(self.ai_api_status)
        self.status_bar.addPermanentWidget(check_network_btn)
        
        self.init_ui()
        self.log_trading("程序初始化完成")  # 测试日志

        # 启动定时器更新数据 - 优化更新频率
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_market_data)
        self.update_timer.start(3000)  # 每3秒更新一次，提高响应速度

        # 添加专门的账户信息快速更新定时器
        self.account_update_timer = QTimer()
        self.account_update_timer.timeout.connect(self.quick_refresh_account)
        self.account_update_timer.start(8000)  # 每8秒快速刷新账户信息

        # 添加线程锁
        self.trade_lock = threading.Lock()
        self.log_lock = threading.Lock()
        
        # 连接信号到日志更新方法
        self.update_log_signal.connect(self.log_trading_main_thread)
        self.show_message_signal.connect(self.show_message_box)

        # 初始化日志计数器
        self.log_count = 0

        # 加载配置文件
        self.load_config()

        # 添加缓存对象
        self._market_data_cache = {}
        self._account_data_cache = {}  # 账户数据专用缓存

        # 添加性能优化相关变量
        self.last_update_time = time.time()
        self.last_account_update_time = time.time()  # 账户数据更新时间
        self.update_interval = 3.0  # 优化更新间隔为3秒，提高响应速度
        self.account_update_interval = 2.0  # 账户数据更新间隔2秒
        self.is_updating = False  # 防止重复更新
        self.is_account_updating = False  # 防止重复更新账户数据
        self.data_cache = {}  # 数据缓存
        self.thread_pool = QThreadPool.globalInstance()  # 使用线程池
        self.thread_pool.setMaxThreadCount(4)  # 增加线程数以支持账户数据并行更新

        # 连接网络状态信号
        self.network_status_signal.connect(self.update_network_status)

        # 启动网络状态检查
        self.network_check_timer = QTimer()
        self.network_check_timer.timeout.connect(self.check_network_status)
        self.network_check_timer.start(60000)  # 每60秒检查一次网络状态

        # 初始网络状态检查
        QTimer.singleShot(2000, self.check_network_status)

        # 添加内存清理定时器
        self.memory_cleanup_timer = QTimer()
        self.memory_cleanup_timer.timeout.connect(self.cleanup_memory)
        self.memory_cleanup_timer.start(300000)  # 每5分钟清理一次内存

        # 初始化ADX相关变量
        self.current_adx = None
        self.current_plus_di = None
        self.current_minus_di = None
        self.adx_last_update = None
        self.adx_cache = {}

        # 初始化ADX参数
        self.adx_period = 14
        self.adx_threshold = 25
        self.adx_strong_threshold = 40

        # 启动ADX专用更新定时器（15分钟周期）
        self.adx_update_timer = QTimer()
        self.adx_update_timer.timeout.connect(self.update_adx_display)
        self.adx_update_timer.start(30000)  # 每30秒更新一次ADX显示

        # 添加动态背景呼吸效果
        self.background_animation_timer = QTimer()
        self.background_animation_timer.timeout.connect(self.animate_background)
        self.background_animation_timer.start(3000)  # 每3秒更新一次背景动画
        self.background_phase = 0  # 动画相位
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)

        # 现代化顶部面板 - 智能仪表板布局
        top_panel = QWidget()
        top_panel.setObjectName("modern_top_panel")
        top_panel.setStyleSheet("""
            #modern_top_panel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(30, 41, 59, 0.98), stop:0.5 rgba(15, 23, 42, 0.95), stop:1 rgba(30, 41, 59, 0.98));
                border: 2px solid rgba(59, 130, 246, 0.3);
                border-radius: 20px;
                margin: 6px;
            }
            #modern_top_panel:hover {
                border: 2px solid rgba(59, 130, 246, 0.5);
            }
        """)
        top_layout = QHBoxLayout(top_panel)
        top_layout.setContentsMargins(24, 20, 24, 20)
        top_layout.setSpacing(24)

        # 智能交易对选择卡片 - 增强版
        trading_card = QWidget()
        trading_card.setObjectName("trading_card")
        trading_card.setStyleSheet("""
            #trading_card {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.18), stop:0.5 rgba(29, 78, 216, 0.12), stop:1 rgba(59, 130, 246, 0.18));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 16px;
                padding: 10px;
            }
            #trading_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.28), stop:0.5 rgba(29, 78, 216, 0.20), stop:1 rgba(59, 130, 246, 0.28));
                border: 2px solid rgba(59, 130, 246, 0.7);
            }
        """)
        trading_layout = QVBoxLayout(trading_card)
        trading_layout.setContentsMargins(16, 12, 16, 12)
        trading_layout.setSpacing(10)

        # 智能交易对标题和状态
        symbol_header_layout = QHBoxLayout()
        symbol_header_layout.setSpacing(10)

        symbol_title = QLabel("🚀 智能交易对")
        symbol_title.setStyleSheet("color: #60A5FA; font-size: 14px; font-weight: 800;")

        # 交易对类型指示器 - 增强版
        self.symbol_type_label = QLabel("💎 主流币")
        self.symbol_type_label.setStyleSheet("""
            color: #3B82F6;
            font-size: 11px;
            font-weight: 700;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(59, 130, 246, 0.25), stop:1 rgba(29, 78, 216, 0.15));
            border: 2px solid rgba(59, 130, 246, 0.4);
            border-radius: 10px;
            padding: 4px 8px;
            min-width: 50px;
        """)

        symbol_header_layout.addWidget(symbol_title)
        symbol_header_layout.addStretch()
        symbol_header_layout.addWidget(self.symbol_type_label)
        trading_layout.addLayout(symbol_header_layout)

        # 智能交易对选择器和状态
        symbol_selection_layout = QHBoxLayout()
        symbol_selection_layout.setSpacing(12)
        symbol_selection_layout.setContentsMargins(4, 6, 4, 6)

        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            '₿ BTC/USDT', 'Ξ ETH/USDT', '🔶 BNB/USDT',  # 主流币
            '☀️ SOL/USDT', '💧 XRP/USDT', '🌟 ADA/USDT',  # 其他热门币
            '🐕 DOGE/USDT', '🐕 SHIB/USDT',            # 迷因币
            '⚫ DOT/USDT', '🔺 AVAX/USDT',             # 公链
            '🥈 LTC/USDT', '💰 BCH/USDT',              # 老牌币
            '🔷 MATIC/USDT', '🔗 LINK/USDT',           # 其他
            '⚛️ ATOM/USDT', '🦄 UNI/USDT',             # DeFi
            '🔴 TRX/USDT', '💚 ETC/USDT'               # 其他
        ])
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        self.symbol_combo.setMinimumWidth(160)
        self.symbol_combo.setMaximumWidth(200)
        self.symbol_combo.setFixedHeight(32)

        # 为智能交易对ComboBox添加专用样式以确保边框完整显示
        self.symbol_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 14px;
                border: 2px solid rgba(51, 65, 85, 0.8);
                border-radius: 10px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.9), stop:1 rgba(15, 23, 42, 0.8));
                color: #F8FAFC;
                font-size: 13px;
                font-weight: 600;
                min-height: 16px;
                selection-background-color: #3B82F6;
            }
            QComboBox::drop-down {
                border: none;
                padding-right: 14px;
                width: 28px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:1 rgba(59, 130, 246, 0.1));
                border-radius: 8px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 7px solid #94A3B8;
            }
            QComboBox:hover {
                border: 2px solid rgba(59, 130, 246, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 1.0), stop:1 rgba(51, 65, 85, 0.9));
            }
            QComboBox:focus {
                border: 2px solid #60A5FA;
                outline: none;
            }
            QComboBox QAbstractItemView {
                border: 2px solid rgba(51, 65, 85, 0.8);
                selection-background-color: #3B82F6;
                background: rgba(30, 41, 59, 0.95);
                color: #F8FAFC;
                border-radius: 10px;
                padding: 6px;
                margin: 2px;
                min-width: 160px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border-radius: 6px;
                margin: 1px;
            }
            QComboBox QAbstractItemView::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.4), stop:1 rgba(29, 78, 216, 0.3));
                color: white;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: rgba(59, 130, 246, 0.2);
            }
        """)

        # 交易对状态指示器 - 增强版
        self.symbol_status_label = QLabel("📈")
        self.symbol_status_label.setFixedHeight(32)  # 与ComboBox高度一致
        self.symbol_status_label.setStyleSheet("""
            color: #3B82F6;
            font-size: 16px;
            font-weight: 700;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(59, 130, 246, 0.15), stop:1 rgba(29, 78, 216, 0.1));
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 10px;
            padding: 6px 10px;
            min-width: 32px;
            max-width: 48px;
        """)

        symbol_selection_layout.addWidget(self.symbol_combo)
        symbol_selection_layout.addWidget(self.symbol_status_label)
        trading_layout.addLayout(symbol_selection_layout)

        # 交易对快速信息
        quick_info_layout = QHBoxLayout()
        quick_info_layout.setSpacing(12)

        # 市值排名
        rank_layout = QVBoxLayout()
        rank_layout.setSpacing(2)
        rank_label = QLabel("排名")
        rank_label.setStyleSheet("color: #94A3B8; font-size: 9px; font-weight: 500;")
        self.symbol_rank_label = QLabel("#1")
        self.symbol_rank_label.setStyleSheet("color: #F59E0B; font-size: 10px; font-weight: 600;")
        rank_layout.addWidget(rank_label)
        rank_layout.addWidget(self.symbol_rank_label)

        # 流动性指示
        liquidity_layout = QVBoxLayout()
        liquidity_layout.setSpacing(2)
        liquidity_label = QLabel("流动性")
        liquidity_label.setStyleSheet("color: #94A3B8; font-size: 9px; font-weight: 500;")
        self.liquidity_indicator = QLabel("🟢")
        self.liquidity_indicator.setStyleSheet("color: #10B981; font-size: 10px; font-weight: 600;")
        liquidity_layout.addWidget(liquidity_label)
        liquidity_layout.addWidget(self.liquidity_indicator)

        # 波动性指示
        volatility_layout = QVBoxLayout()
        volatility_layout.setSpacing(2)
        volatility_label = QLabel("波动性")
        volatility_label.setStyleSheet("color: #94A3B8; font-size: 9px; font-weight: 500;")
        self.volatility_indicator = QLabel("中等")
        self.volatility_indicator.setStyleSheet("color: #3B82F6; font-size: 10px; font-weight: 600;")
        volatility_layout.addWidget(volatility_label)
        volatility_layout.addWidget(self.volatility_indicator)

        quick_info_layout.addLayout(rank_layout)
        quick_info_layout.addLayout(liquidity_layout)
        quick_info_layout.addLayout(volatility_layout)
        trading_layout.addLayout(quick_info_layout)

        top_layout.addWidget(trading_card)

        # 智能实时价格卡片 - 增强版
        price_card = QWidget()
        price_card.setObjectName("price_card")
        price_card.setStyleSheet("""
            #price_card {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(245, 158, 11, 0.20), stop:0.5 rgba(217, 119, 6, 0.12), stop:1 rgba(245, 158, 11, 0.20));
                border: 2px solid rgba(245, 158, 11, 0.5);
                border-radius: 16px;
                padding: 10px;
            }
            #price_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(245, 158, 11, 0.30), stop:0.5 rgba(217, 119, 6, 0.20), stop:1 rgba(245, 158, 11, 0.30));
                border: 2px solid rgba(245, 158, 11, 0.7);
            }
        """)
        price_layout = QVBoxLayout(price_card)
        price_layout.setContentsMargins(16, 12, 16, 12)
        price_layout.setSpacing(8)

        # 智能价格标题和状态
        price_header_layout = QHBoxLayout()
        price_header_layout.setSpacing(10)

        price_title = QLabel("💎 实时价格")
        price_title.setStyleSheet("color: #FBBF24; font-size: 14px; font-weight: 800;")

        # 价格趋势指示器 - 增强版
        self.price_trend_label = QLabel("📈")
        self.price_trend_label.setStyleSheet("""
            color: #F59E0B;
            font-size: 14px;
            font-weight: 700;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(245, 158, 11, 0.25), stop:1 rgba(217, 119, 6, 0.15));
            border: 2px solid rgba(245, 158, 11, 0.4);
            border-radius: 10px;
            padding: 4px 8px;
            min-width: 24px;
        """)

        price_header_layout.addWidget(price_title)
        price_header_layout.addStretch()
        price_header_layout.addWidget(self.price_trend_label)
        price_layout.addLayout(price_header_layout)

        # 主要价格显示 - 增强版
        self.price_display = QLabel("$0.00")
        self.price_display.setStyleSheet("""
            font-size: 24px;
            font-weight: 800;
            color: #F59E0B;
            padding: 4px 0px;
        """)
        self.price_display.setTextFormat(Qt.TextFormat.RichText)
        price_layout.addWidget(self.price_display)

        # 涨幅和波动信息
        change_layout = QHBoxLayout()
        change_layout.setSpacing(8)

        # 24小时涨幅
        self.change_display = QLabel("0.00%")
        self.change_display.setStyleSheet("""
            font-size: 12px;
            color: #10B981;
            font-weight: 600;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 6px;
            padding: 2px 8px;
        """)

        # 价格变化指示器
        self.price_change_indicator = QLabel("●")
        self.price_change_indicator.setStyleSheet("""
            color: #94A3B8;
            font-size: 16px;
            font-weight: 700;
        """)

        change_layout.addWidget(self.change_display)
        change_layout.addWidget(self.price_change_indicator)
        change_layout.addStretch()
        price_layout.addLayout(change_layout)

        # 价格统计信息
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(12)

        # 24小时最高价
        high_layout = QVBoxLayout()
        high_layout.setSpacing(2)
        high_label = QLabel("24H最高")
        high_label.setStyleSheet("color: #94A3B8; font-size: 9px; font-weight: 500;")
        self.high_price_label = QLabel("$0.00")
        self.high_price_label.setStyleSheet("color: #EF4444; font-size: 10px; font-weight: 600;")
        high_layout.addWidget(high_label)
        high_layout.addWidget(self.high_price_label)

        # 24小时最低价
        low_layout = QVBoxLayout()
        low_layout.setSpacing(2)
        low_label = QLabel("24H最低")
        low_label.setStyleSheet("color: #94A3B8; font-size: 9px; font-weight: 500;")
        self.low_price_label = QLabel("$0.00")
        self.low_price_label.setStyleSheet("color: #10B981; font-size: 10px; font-weight: 600;")
        low_layout.addWidget(low_label)
        low_layout.addWidget(self.low_price_label)

        # 24小时成交量
        volume_layout = QVBoxLayout()
        volume_layout.setSpacing(2)
        volume_label = QLabel("24H成交量")
        volume_label.setStyleSheet("color: #94A3B8; font-size: 9px; font-weight: 500;")
        self.volume_label = QLabel("0")
        self.volume_label.setStyleSheet("color: #3B82F6; font-size: 10px; font-weight: 600;")
        volume_layout.addWidget(volume_label)
        volume_layout.addWidget(self.volume_label)

        stats_layout.addLayout(high_layout)
        stats_layout.addLayout(low_layout)
        stats_layout.addLayout(volume_layout)
        price_layout.addLayout(stats_layout)

        top_layout.addWidget(price_card)

        # 智能止盈止损设置卡片 - 增强版
        tp_sl_card = QWidget()
        tp_sl_card.setObjectName("tp_sl_card")
        tp_sl_card.setStyleSheet("""
            #tp_sl_card {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.20), stop:0.5 rgba(5, 150, 105, 0.12), stop:1 rgba(16, 185, 129, 0.20));
                border: 2px solid rgba(16, 185, 129, 0.5);
                border-radius: 16px;
                padding: 10px;
            }
            #tp_sl_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(16, 185, 129, 0.30), stop:0.5 rgba(5, 150, 105, 0.20), stop:1 rgba(16, 185, 129, 0.30));
                border: 2px solid rgba(16, 185, 129, 0.7);
            }
        """)
        tp_sl_layout = QVBoxLayout(tp_sl_card)
        tp_sl_layout.setContentsMargins(16, 12, 16, 12)
        tp_sl_layout.setSpacing(10)

        # 智能止盈止损标题和状态
        tp_sl_header_layout = QHBoxLayout()
        tp_sl_header_layout.setSpacing(10)

        tp_sl_title = QLabel("🛡️ 智能风控")
        tp_sl_title.setStyleSheet("color: #34D399; font-size: 14px; font-weight: 800;")

        # 风险等级指示器 - 增强版
        self.risk_level_label = QLabel("⚖️ 中等风险")
        self.risk_level_label.setStyleSheet("""
            color: #F59E0B;
            font-size: 11px;
            font-weight: 700;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(245, 158, 11, 0.25), stop:1 rgba(217, 119, 6, 0.15));
            border: 2px solid rgba(245, 158, 11, 0.4);
            border-radius: 10px;
            padding: 4px 8px;
            min-width: 60px;
        """)

        tp_sl_header_layout.addWidget(tp_sl_title)
        tp_sl_header_layout.addStretch()
        tp_sl_header_layout.addWidget(self.risk_level_label)
        tp_sl_layout.addLayout(tp_sl_header_layout)

        # 止盈止损控件网格
        tp_sl_grid = QGridLayout()
        tp_sl_grid.setVerticalSpacing(8)
        tp_sl_grid.setHorizontalSpacing(10)

        # 动态止盈设置
        tp_layout = QVBoxLayout()
        tp_layout.setSpacing(4)

        tp_header_layout = QHBoxLayout()
        tp_label = QLabel("📈 止盈:")
        tp_label.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        self.tp_status_label = QLabel("保守")
        self.tp_status_label.setStyleSheet("""
            color: #10B981;
            font-size: 9px;
            font-weight: 600;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 1px 4px;
        """)

        tp_header_layout.addWidget(tp_label)
        tp_header_layout.addStretch()
        tp_header_layout.addWidget(self.tp_status_label)
        tp_layout.addLayout(tp_header_layout)

        self.tp_spinbox = QDoubleSpinBox()
        self.tp_spinbox.setRange(0.1, 1000)
        self.tp_spinbox.setDecimals(2)
        self.tp_spinbox.setValue(1.5)
        self.tp_spinbox.setSuffix("%")
        self.tp_spinbox.setFixedWidth(100)
        self.tp_spinbox.setFixedHeight(28)
        self.tp_spinbox.valueChanged.connect(self.update_tp_sl_display)
        tp_layout.addWidget(self.tp_spinbox)

        tp_sl_grid.addLayout(tp_layout, 0, 0)

        # 动态止损设置
        sl_layout = QVBoxLayout()
        sl_layout.setSpacing(4)

        sl_header_layout = QHBoxLayout()
        sl_label = QLabel("📉 止损:")
        sl_label.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        self.sl_status_label = QLabel("保守")
        self.sl_status_label.setStyleSheet("""
            color: #10B981;
            font-size: 9px;
            font-weight: 600;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 1px 4px;
        """)

        sl_header_layout.addWidget(sl_label)
        sl_header_layout.addStretch()
        sl_header_layout.addWidget(self.sl_status_label)
        sl_layout.addLayout(sl_header_layout)

        self.sl_spinbox = QDoubleSpinBox()
        self.sl_spinbox.setRange(0.1, 1000)
        self.sl_spinbox.setDecimals(2)
        self.sl_spinbox.setValue(1.5)
        self.sl_spinbox.setSuffix("%")
        self.sl_spinbox.setFixedWidth(100)
        self.sl_spinbox.setFixedHeight(28)
        self.sl_spinbox.valueChanged.connect(self.update_tp_sl_display)
        sl_layout.addWidget(self.sl_spinbox)

        tp_sl_grid.addLayout(sl_layout, 0, 1)

        # 风险比率显示
        ratio_layout = QVBoxLayout()
        ratio_layout.setSpacing(4)

        ratio_header = QLabel("⚖️ 风险比率:")
        ratio_header.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        self.risk_ratio_label = QLabel("1:1")
        self.risk_ratio_label.setStyleSheet("""
            color: #F59E0B;
            font-size: 12px;
            font-weight: 700;
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 6px;
            padding: 4px 8px;
            min-width: 30px;
        """)

        ratio_layout.addWidget(ratio_header)
        ratio_layout.addWidget(self.risk_ratio_label)
        tp_sl_grid.addLayout(ratio_layout, 1, 0)

        # 应用按钮
        apply_tp_sl_button = QPushButton("应用设置")
        apply_tp_sl_button.setProperty("type", "neutral")
        apply_tp_sl_button.setFixedSize(80, 32)
        apply_tp_sl_button.setStyleSheet("""
            QPushButton {
                font-size: 11px;
                font-weight: 600;
                padding: 6px 10px;
                border-radius: 8px;
            }
        """)
        apply_tp_sl_button.clicked.connect(self.apply_tp_sl_settings)
        tp_sl_grid.addWidget(apply_tp_sl_button, 1, 1)

        tp_sl_layout.addLayout(tp_sl_grid)

        # 快速设置按钮
        quick_settings_layout = QHBoxLayout()
        quick_settings_layout.setSpacing(6)

        conservative_btn = QPushButton("保守")
        conservative_btn.setFixedSize(45, 24)
        conservative_btn.setStyleSheet("""
            QPushButton {
                font-size: 9px;
                font-weight: 600;
                background: rgba(16, 185, 129, 0.2);
                border: 1px solid rgba(16, 185, 129, 0.3);
                border-radius: 6px;
                color: #10B981;
                padding: 2px 4px;
            }
            QPushButton:hover {
                background: rgba(16, 185, 129, 0.3);
            }
        """)
        conservative_btn.clicked.connect(lambda: self.set_quick_tp_sl(1.0, 1.0))

        moderate_btn = QPushButton("适中")
        moderate_btn.setFixedSize(45, 24)
        moderate_btn.setStyleSheet("""
            QPushButton {
                font-size: 9px;
                font-weight: 600;
                background: rgba(245, 158, 11, 0.2);
                border: 1px solid rgba(245, 158, 11, 0.3);
                border-radius: 6px;
                color: #F59E0B;
                padding: 2px 4px;
            }
            QPushButton:hover {
                background: rgba(245, 158, 11, 0.3);
            }
        """)
        moderate_btn.clicked.connect(lambda: self.set_quick_tp_sl(2.0, 1.5))

        aggressive_btn = QPushButton("激进")
        aggressive_btn.setFixedSize(45, 24)
        aggressive_btn.setStyleSheet("""
            QPushButton {
                font-size: 9px;
                font-weight: 600;
                background: rgba(239, 68, 68, 0.2);
                border: 1px solid rgba(239, 68, 68, 0.3);
                border-radius: 6px;
                color: #EF4444;
                padding: 2px 4px;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 0.3);
            }
        """)
        aggressive_btn.clicked.connect(lambda: self.set_quick_tp_sl(3.0, 2.0))

        quick_settings_layout.addWidget(conservative_btn)
        quick_settings_layout.addWidget(moderate_btn)
        quick_settings_layout.addWidget(aggressive_btn)
        quick_settings_layout.addStretch()

        tp_sl_layout.addLayout(quick_settings_layout)
        top_layout.addWidget(tp_sl_card)

        # 初始化止盈止损显示
        self.update_tp_sl_display()

        # 智能ADX实时分析卡片 - 全新增强版
        adx_card = QWidget()
        adx_card.setObjectName("adx_card")
        adx_card.setStyleSheet("""
            #adx_card {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(139, 92, 246, 0.20), stop:0.5 rgba(109, 40, 217, 0.12), stop:1 rgba(139, 92, 246, 0.20));
                border: 2px solid rgba(139, 92, 246, 0.5);
                border-radius: 16px;
                padding: 10px;
            }
            #adx_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(139, 92, 246, 0.30), stop:0.5 rgba(109, 40, 217, 0.20), stop:1 rgba(139, 92, 246, 0.30));
                border: 2px solid rgba(139, 92, 246, 0.7);
            }
        """)
        adx_layout = QVBoxLayout(adx_card)
        adx_layout.setContentsMargins(16, 12, 16, 12)
        adx_layout.setSpacing(10)

        # 智能ADX分析标题和状态
        adx_header_layout = QHBoxLayout()
        adx_header_layout.setSpacing(10)

        adx_title = QLabel("📊 ADX实时分析")
        adx_title.setStyleSheet("color: #A78BFA; font-size: 14px; font-weight: 800;")

        # ADX趋势状态指示器 - 增强版
        self.adx_trend_status_label = QLabel("📈 趋势待定")
        self.adx_trend_status_label.setStyleSheet("""
            color: #94A3B8;
            font-size: 11px;
            font-weight: 700;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(148, 163, 184, 0.25), stop:1 rgba(100, 116, 139, 0.15));
            border: 2px solid rgba(148, 163, 184, 0.4);
            border-radius: 10px;
            padding: 4px 8px;
            min-width: 60px;
        """)

        adx_header_layout.addWidget(adx_title)
        adx_header_layout.addStretch()
        adx_header_layout.addWidget(self.adx_trend_status_label)
        adx_layout.addLayout(adx_header_layout)

        # ADX主要数值显示区域（紧凑版）
        adx_values_layout = QHBoxLayout()
        adx_values_layout.setSpacing(8)

        # ADX值显示
        adx_value_layout = QVBoxLayout()
        adx_value_layout.setSpacing(2)

        adx_value_label = QLabel("ADX:")
        adx_value_label.setStyleSheet("color: #F8FAFC; font-size: 10px; font-weight: 500;")

        self.adx_value_display = QLabel("--")
        self.adx_value_display.setStyleSheet("""
            color: #A78BFA;
            font-size: 16px;
            font-weight: 800;
            background: rgba(167, 139, 250, 0.1);
            border: 1px solid rgba(167, 139, 250, 0.3);
            border-radius: 6px;
            padding: 2px 6px;
            min-width: 35px;
        """)

        adx_value_layout.addWidget(adx_value_label)
        adx_value_layout.addWidget(self.adx_value_display)
        adx_values_layout.addLayout(adx_value_layout)

        # +DI值显示
        plus_di_layout = QVBoxLayout()
        plus_di_layout.setSpacing(2)

        plus_di_label = QLabel("+DI:")
        plus_di_label.setStyleSheet("color: #F8FAFC; font-size: 10px; font-weight: 500;")

        self.plus_di_display = QLabel("--")
        self.plus_di_display.setStyleSheet("""
            color: #10B981;
            font-size: 12px;
            font-weight: 700;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 4px;
            padding: 1px 4px;
            min-width: 25px;
        """)

        plus_di_layout.addWidget(plus_di_label)
        plus_di_layout.addWidget(self.plus_di_display)
        adx_values_layout.addLayout(plus_di_layout)

        # -DI值显示
        minus_di_layout = QVBoxLayout()
        minus_di_layout.setSpacing(2)

        minus_di_label = QLabel("-DI:")
        minus_di_label.setStyleSheet("color: #F8FAFC; font-size: 10px; font-weight: 500;")

        self.minus_di_display = QLabel("--")
        self.minus_di_display.setStyleSheet("""
            color: #EF4444;
            font-size: 12px;
            font-weight: 700;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 4px;
            padding: 1px 4px;
            min-width: 25px;
        """)

        minus_di_layout.addWidget(minus_di_label)
        minus_di_layout.addWidget(self.minus_di_display)
        adx_values_layout.addLayout(minus_di_layout)

        adx_layout.addLayout(adx_values_layout)

        # ADX趋势强度进度条
        adx_strength_layout = QVBoxLayout()
        adx_strength_layout.setSpacing(6)

        # 趋势强度标签
        strength_info_layout = QHBoxLayout()
        strength_info_layout.setSpacing(8)

        strength_label = QLabel("趋势强度:")
        strength_label.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        self.adx_strength_label = QLabel("弱趋势")
        self.adx_strength_label.setStyleSheet("""
            color: #94A3B8;
            font-size: 11px;
            font-weight: 600;
            background: rgba(148, 163, 184, 0.1);
            border-radius: 6px;
            padding: 2px 8px;
            min-width: 45px;
        """)

        strength_info_layout.addWidget(strength_label)
        strength_info_layout.addWidget(self.adx_strength_label)
        strength_info_layout.addStretch()
        adx_strength_layout.addLayout(strength_info_layout)

        # ADX进度条
        adx_progress_layout = QVBoxLayout()
        adx_progress_layout.setSpacing(2)

        adx_progress_info_layout = QHBoxLayout()
        adx_progress_start_label = QLabel("0")
        adx_progress_start_label.setStyleSheet("color: #64748B; font-size: 9px;")

        self.adx_threshold_label = QLabel("阈值: 25")
        self.adx_threshold_label.setStyleSheet("color: #A78BFA; font-size: 9px; font-weight: 600;")

        adx_progress_end_label = QLabel("100")
        adx_progress_end_label.setStyleSheet("color: #64748B; font-size: 9px;")

        adx_progress_info_layout.addWidget(adx_progress_start_label)
        adx_progress_info_layout.addWidget(self.adx_threshold_label)
        adx_progress_info_layout.addStretch()
        adx_progress_info_layout.addWidget(adx_progress_end_label)
        adx_progress_layout.addLayout(adx_progress_info_layout)

        # ADX进度条容器
        self.adx_progress_container = QWidget()
        self.adx_progress_container.setFixedHeight(8)
        self.adx_progress_container.setStyleSheet("""
            background: rgba(51, 65, 85, 0.3);
            border-radius: 4px;
        """)

        # ADX进度条填充
        self.adx_progress_fill = QWidget(self.adx_progress_container)
        self.adx_progress_fill.setFixedHeight(8)
        self.adx_progress_fill.setFixedWidth(0)
        self.adx_progress_fill.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #EF4444, stop:0.25 #F59E0B, stop:0.5 #10B981, stop:1 #3B82F6);
            border-radius: 4px;
        """)

        adx_progress_layout.addWidget(self.adx_progress_container)
        adx_strength_layout.addLayout(adx_progress_layout)
        adx_layout.addLayout(adx_strength_layout)

        # ADX时间周期和更新信息
        adx_time_layout = QHBoxLayout()
        adx_time_layout.setSpacing(8)

        time_period_label = QLabel("周期:")
        time_period_label.setStyleSheet("color: #F8FAFC; font-size: 10px; font-weight: 500;")

        self.adx_timeframe_label = QLabel("15分钟")
        self.adx_timeframe_label.setStyleSheet("""
            color: #3B82F6;
            font-size: 10px;
            font-weight: 600;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 4px;
            padding: 1px 4px;
        """)

        last_update_label = QLabel("更新:")
        last_update_label.setStyleSheet("color: #F8FAFC; font-size: 10px; font-weight: 500;")

        self.adx_last_update_label = QLabel("未更新")
        self.adx_last_update_label.setStyleSheet("""
            color: #64748B;
            font-size: 10px;
            font-weight: 500;
        """)

        adx_time_layout.addWidget(time_period_label)
        adx_time_layout.addWidget(self.adx_timeframe_label)
        adx_time_layout.addWidget(last_update_label)
        adx_time_layout.addWidget(self.adx_last_update_label)
        adx_time_layout.addStretch()
        adx_layout.addLayout(adx_time_layout)

        # 创建分析组件容器（ADX + 触发分析）
        analysis_container = QWidget()
        analysis_layout = QHBoxLayout(analysis_container)
        analysis_layout.setContentsMargins(0, 0, 0, 0)
        analysis_layout.setSpacing(12)

        analysis_layout.addWidget(adx_card)

        # 智能触发分析卡片 - 恢复版本
        trigger_card = QWidget()
        trigger_card.setObjectName("trigger_card")
        trigger_card.setStyleSheet("""
            #trigger_card {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.20), stop:0.5 rgba(220, 38, 38, 0.12), stop:1 rgba(239, 68, 68, 0.20));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 16px;
                padding: 10px;
            }
            #trigger_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.30), stop:0.5 rgba(220, 38, 38, 0.20), stop:1 rgba(239, 68, 68, 0.30));
                border: 2px solid rgba(239, 68, 68, 0.7);
            }
        """)
        trigger_layout = QVBoxLayout(trigger_card)
        trigger_layout.setContentsMargins(12, 10, 12, 10)
        trigger_layout.setSpacing(8)

        # 智能触发分析标题和状态
        trigger_header_layout = QHBoxLayout()
        trigger_header_layout.setSpacing(10)

        trigger_title = QLabel("🎯 AI触发分析")
        trigger_title.setStyleSheet("color: #F87171; font-size: 14px; font-weight: 800;")

        # 智能触发状态指示器
        self.trigger_status_label = QLabel("🤖 AI待机")
        self.trigger_status_label.setStyleSheet("""
            color: #94A3B8;
            font-size: 11px;
            font-weight: 700;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(148, 163, 184, 0.25), stop:1 rgba(100, 116, 139, 0.15));
            border: 2px solid rgba(148, 163, 184, 0.4);
            border-radius: 10px;
            padding: 4px 8px;
            min-width: 60px;
        """)

        trigger_header_layout.addWidget(trigger_title)
        trigger_header_layout.addStretch()
        trigger_header_layout.addWidget(self.trigger_status_label)
        trigger_layout.addLayout(trigger_header_layout)

        # 阈值设置行
        threshold_layout = QHBoxLayout()
        threshold_layout.setSpacing(8)

        threshold_label = QLabel("波动阈值:")
        threshold_label.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        self.trigger_threshold_spinbox = QDoubleSpinBox()
        self.trigger_threshold_spinbox.setRange(0.1, 10.0)
        self.trigger_threshold_spinbox.setDecimals(2)
        self.trigger_threshold_spinbox.setValue(0.25)
        self.trigger_threshold_spinbox.setSuffix("%")
        self.trigger_threshold_spinbox.setFixedWidth(100)
        self.trigger_threshold_spinbox.setFixedHeight(28)

        threshold_layout.addWidget(threshold_label)
        threshold_layout.addWidget(self.trigger_threshold_spinbox)
        threshold_layout.addStretch()
        trigger_layout.addLayout(threshold_layout)

        # 动态波动显示和进度条
        volatility_layout = QVBoxLayout()
        volatility_layout.setSpacing(6)

        # 波动数值显示
        volatility_value_layout = QHBoxLayout()
        volatility_value_layout.setSpacing(8)

        volatility_label = QLabel("当前波动:")
        volatility_label.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        self.current_volatility_label = QLabel("0.00%")
        self.current_volatility_label.setStyleSheet("""
            color: #94A3B8;
            font-size: 11px;
            font-weight: 600;
            background: rgba(148, 163, 184, 0.1);
            border-radius: 6px;
            padding: 2px 8px;
            min-width: 45px;
        """)

        volatility_value_layout.addWidget(volatility_label)
        volatility_value_layout.addWidget(self.current_volatility_label)
        volatility_value_layout.addStretch()
        volatility_layout.addLayout(volatility_value_layout)

        # 动态进度条
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(2)

        progress_info_layout = QHBoxLayout()
        progress_start_label = QLabel("0%")
        progress_start_label.setStyleSheet("color: #64748B; font-size: 9px;")

        self.progress_threshold_label = QLabel("阈值: 0.25%")
        self.progress_threshold_label.setStyleSheet("color: #F87171; font-size: 9px; font-weight: 600;")

        progress_info_layout.addWidget(progress_start_label)
        progress_info_layout.addStretch()
        progress_info_layout.addWidget(self.progress_threshold_label)
        progress_layout.addLayout(progress_info_layout)

        # 进度条容器
        self.progress_container = QWidget()
        self.progress_container.setFixedHeight(8)
        self.progress_container.setStyleSheet("""
            background: rgba(51, 65, 85, 0.3);
            border-radius: 4px;
        """)

        # 进度条填充
        self.progress_fill = QWidget(self.progress_container)
        self.progress_fill.setFixedHeight(8)
        self.progress_fill.setFixedWidth(0)
        self.progress_fill.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #10B981, stop:0.7 #F59E0B, stop:1 #EF4444);
            border-radius: 4px;
        """)

        progress_layout.addWidget(self.progress_container)
        volatility_layout.addLayout(progress_layout)
        trigger_layout.addLayout(volatility_layout)

        # 最后触发时间
        last_trigger_layout = QHBoxLayout()
        last_trigger_layout.setSpacing(8)

        last_trigger_label = QLabel("最后触发:")
        last_trigger_label.setStyleSheet("color: #F8FAFC; font-size: 10px; font-weight: 500;")

        self.last_trigger_time_label = QLabel("未触发")
        self.last_trigger_time_label.setStyleSheet("""
            color: #64748B;
            font-size: 10px;
            font-weight: 500;
        """)

        last_trigger_layout.addWidget(last_trigger_label)
        last_trigger_layout.addWidget(self.last_trigger_time_label)
        last_trigger_layout.addStretch()
        trigger_layout.addLayout(last_trigger_layout)

        # 将触发分析卡片添加到分析容器
        analysis_layout.addWidget(trigger_card)

        # 将分析容器添加到顶部布局
        top_layout.addWidget(analysis_container)

        # 添加弹性空间
        top_layout.addStretch(1)

        # 智能技术指标设置按钮 - 增强版
        indicator_settings_button = QPushButton("📊 技术指标")
        indicator_settings_button.setProperty("type", "primary")
        indicator_settings_button.setFixedSize(120, 48)
        indicator_settings_button.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: 800;
                border-radius: 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #F59E0B, stop:0.5 #D97706, stop:1 #F59E0B);
                color: #0F172A;
                border: 2px solid rgba(245, 158, 11, 0.6);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #FBBF24, stop:0.5 #F59E0B, stop:1 #FBBF24);
            }
        """)
        indicator_settings_button.clicked.connect(self.show_indicator_settings)
        top_layout.addWidget(indicator_settings_button)

        # 完成顶部面板
        layout.addWidget(top_panel)

        # 智能主面板 - 增强版分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStyleSheet("""
            QSplitter {
                background: transparent;
            }
            QSplitter::handle {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(51, 65, 85, 0.8), stop:0.5 rgba(71, 85, 105, 0.9), stop:1 rgba(51, 65, 85, 0.8));
                width: 6px;
                border-radius: 3px;
                margin: 4px 0;
                border: 1px solid rgba(59, 130, 246, 0.3);
            }
            QSplitter::handle:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3B82F6, stop:0.5 #60A5FA, stop:1 #3B82F6);
            }
        """)

        # 左侧面板 - 智能K线图卡片
        left_panel = QWidget()
        left_panel.setObjectName("chart_panel")
        left_panel.setStyleSheet("""
            #chart_panel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(30, 41, 59, 0.98), stop:0.5 rgba(15, 23, 42, 0.95), stop:1 rgba(30, 41, 59, 0.98));
                border: 2px solid rgba(59, 130, 246, 0.3);
                border-radius: 20px;
                margin: 6px;
            }
            #chart_panel:hover {
                border: 2px solid rgba(59, 130, 246, 0.5);
            }
        """)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(20, 20, 20, 20)

        # K线图
        self.chart_widget = ChartWidget()
        left_layout.addWidget(self.chart_widget)

        # 连接图表的时间周期变化信号以刷新数据
        self.chart_widget.timeframe_changed.connect(self.refresh_chart_data)

        # 右侧面板 - 智能标签页系统
        right_panel = QTabWidget()
        right_panel.setObjectName("right_tabs")
        right_panel.setStyleSheet("""
            #right_tabs {
                background: transparent;
            }
            #right_tabs QTabBar::tab {
                padding: 16px 28px;
                margin-right: 6px;
                font-weight: 700;
                font-size: 14px;
            }
            #right_tabs::pane {
                margin-top: 6px;
                border: 2px solid rgba(59, 130, 246, 0.3);
                border-radius: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(30, 41, 59, 0.98), stop:0.5 rgba(15, 23, 42, 0.95), stop:1 rgba(30, 41, 59, 0.98));
            }
        """)
        
        # 现代化账户信息标签页
        account_tab = QWidget()
        account_tab.setObjectName("account_tab")
        account_tab.setStyleSheet("""
            #account_tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                border-radius: 12px;
            }
        """)

        # 创建主布局
        account_main_layout = QVBoxLayout(account_tab)
        account_main_layout.setContentsMargins(0, 0, 0, 0)
        account_main_layout.setSpacing(0)

        # 创建滚动区域
        account_scroll_area = QScrollArea()
        account_scroll_area.setWidgetResizable(True)
        account_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        account_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        account_scroll_area.setObjectName("account_scroll")
        account_scroll_area.setStyleSheet("""
            #account_scroll {
                border: none;
                background: transparent;
            }
            #account_scroll > QWidget > QWidget {
                background: transparent;
            }
        """)

        # 创建滚动内容容器
        account_scroll_content = QWidget()
        account_scroll_content.setObjectName("account_scroll_content")
        account_scroll_content.setStyleSheet("""
            #account_scroll_content {
                background: transparent;
            }
        """)
        account_layout = QVBoxLayout(account_scroll_content)
        account_layout.setContentsMargins(16, 16, 16, 16)
        account_layout.setSpacing(12)

        # 现代化账户资产概览卡片
        account_summary_layout = QHBoxLayout()
        account_summary_layout.setSpacing(12)

        # USDT总资产卡片
        usdt_card = QWidget()
        usdt_card.setObjectName("modern_summary_card")
        usdt_card.setStyleSheet("""
            #modern_summary_card {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(245, 158, 11, 0.15), stop:1 rgba(217, 119, 6, 0.05));
                border: 2px solid rgba(245, 158, 11, 0.3);
                border-radius: 12px;
                padding: 8px;
            }
        """)
        usdt_layout = QVBoxLayout(usdt_card)
        usdt_layout.setContentsMargins(16, 12, 16, 12)
        usdt_layout.setSpacing(4)

        usdt_title = QLabel("USDT 总资产")
        usdt_title.setStyleSheet("color: #FBBF24; font-size: 12px; font-weight: 600;")
        self.usdt_value = QLabel("$0.00")
        self.usdt_value.setStyleSheet("color: #F59E0B; font-size: 20px; font-weight: 700;")

        usdt_layout.addWidget(usdt_title)
        usdt_layout.addWidget(self.usdt_value)

        # BTC总资产卡片
        btc_card = QWidget()
        btc_card.setObjectName("modern_summary_card_btc")
        btc_card.setStyleSheet("""
            #modern_summary_card_btc {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(59, 130, 246, 0.15), stop:1 rgba(29, 78, 216, 0.05));
                border: 2px solid rgba(59, 130, 246, 0.3);
                border-radius: 12px;
                padding: 8px;
            }
        """)
        btc_layout = QVBoxLayout(btc_card)
        btc_layout.setContentsMargins(16, 12, 16, 12)
        btc_layout.setSpacing(4)

        btc_title = QLabel("BTC 总资产")
        btc_title.setStyleSheet("color: #60A5FA; font-size: 12px; font-weight: 600;")
        self.btc_value = QLabel("₿0.00000000")
        self.btc_value.setStyleSheet("color: #3B82F6; font-size: 20px; font-weight: 700;")

        btc_layout.addWidget(btc_title)
        btc_layout.addWidget(self.btc_value)

        # 未实现盈亏卡片
        pnl_card = QWidget()
        pnl_card.setObjectName("modern_summary_card_pnl")
        pnl_card.setStyleSheet("""
            #modern_summary_card_pnl {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(16, 185, 129, 0.15), stop:1 rgba(5, 150, 105, 0.05));
                border: 2px solid rgba(16, 185, 129, 0.3);
                border-radius: 12px;
                padding: 8px;
            }
        """)
        pnl_layout = QVBoxLayout(pnl_card)
        pnl_layout.setContentsMargins(16, 12, 16, 12)
        pnl_layout.setSpacing(4)

        pnl_title = QLabel("未实现盈亏")
        pnl_title.setStyleSheet("color: #34D399; font-size: 12px; font-weight: 600;")
        self.pnl_value = QLabel("$0.00")
        self.pnl_value.setStyleSheet("color: #10B981; font-size: 20px; font-weight: 700;")

        pnl_layout.addWidget(pnl_title)
        pnl_layout.addWidget(self.pnl_value)

        # 将卡片添加到摘要布局
        account_summary_layout.addWidget(usdt_card, 1)
        account_summary_layout.addWidget(btc_card, 1)
        account_summary_layout.addWidget(pnl_card, 1)

        # 添加摘要布局到账户布局
        account_layout.addLayout(account_summary_layout)

        # 现代化资产标题栏
        asset_header = QWidget()
        asset_header.setObjectName("asset_header")
        asset_header.setStyleSheet("""
            #asset_header {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(51, 65, 85, 0.3), stop:1 rgba(30, 41, 59, 0.1));
                border-radius: 8px;
                padding: 4px;
            }
        """)
        asset_header_layout = QHBoxLayout(asset_header)
        asset_header_layout.setContentsMargins(12, 8, 12, 8)

        asset_title = QLabel("账户资产")
        asset_title.setStyleSheet("font-size: 16px; font-weight: 700; color: #60A5FA;")

        # 添加状态指示器
        self.account_status_indicator = QLabel("●")
        self.account_status_indicator.setStyleSheet("""
            color: #10B981;
            font-size: 14px;
            font-weight: bold;
        """)
        self.account_status_indicator.setToolTip("账户数据状态：绿色=正常，黄色=更新中，红色=错误")

        refresh_account_btn = QPushButton("快速刷新")
        refresh_account_btn.setProperty("type", "neutral")
        refresh_account_btn.setFixedSize(80, 32)
        refresh_account_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: 600;
                border-radius: 6px;
            }
        """)
        refresh_account_btn.clicked.connect(self.quick_refresh_account)

        asset_header_layout.addWidget(asset_title)
        asset_header_layout.addWidget(self.account_status_indicator)
        asset_header_layout.addStretch()
        asset_header_layout.addWidget(refresh_account_btn)

        # 现代化账户资产表格
        account_group = QGroupBox()
        account_group.setStyleSheet("QGroupBox { border: none; background: transparent; }")
        account_group_layout = QVBoxLayout()
        account_group_layout.setContentsMargins(0, 8, 0, 0)
        account_group_layout.setSpacing(8)
        account_group_layout.addWidget(asset_header)

        self.account_table = QTableWidget()
        self.account_table.setColumnCount(4)
        self.account_table.setHorizontalHeaderLabels(['资产', '可用', '已用', '总计'])
        self.account_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.account_table.setAlternatingRowColors(True)
        self.account_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.account_table.setFixedHeight(250)  # 设置与交易日志相同的高度
        self.account_table.setStyleSheet("""
            QTableWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                border: 2px solid #334155;
                border-radius: 12px;
                gridline-color: rgba(51, 65, 85, 0.5);
                selection-background-color: rgba(59, 130, 246, 0.2);
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid rgba(51, 65, 85, 0.3);
                color: #F8FAFC;
                font-weight: 500;
            }
            QTableWidget::item:alternate {
                background-color: rgba(30, 41, 59, 0.3);
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.3), stop:1 rgba(29, 78, 216, 0.2));
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #334155, stop:1 #1E293B);
                color: #CBD5E1;
                padding: 12px 8px;
                border: none;
                border-right: 1px solid #475569;
                border-bottom: 2px solid #3B82F6;
                font-weight: 700;
                font-size: 12px;
            }
        """)
        account_group_layout.addWidget(self.account_table)
        account_group.setLayout(account_group_layout)
        account_layout.addWidget(account_group)
        
        # 现代化分割器来分隔持仓和委托订单
        positions_splitter = QSplitter(Qt.Orientation.Vertical)
        positions_splitter.setStyleSheet("""
            QSplitter::handle {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #334155, stop:0.5 #475569, stop:1 #334155);
                height: 4px;
                border-radius: 2px;
                margin: 4px 0;
            }
            QSplitter::handle:hover {
                background: #3B82F6;
            }
        """)

        # 现代化持仓信息面板
        positions_group = QGroupBox()
        positions_group.setStyleSheet("QGroupBox { border: none; background: transparent; }")
        positions_group_layout = QVBoxLayout()
        positions_group_layout.setContentsMargins(0, 0, 0, 0)
        positions_group_layout.setSpacing(8)

        # 现代化持仓标题栏
        positions_header = QWidget()
        positions_header.setObjectName("positions_header")
        positions_header.setStyleSheet("""
            #positions_header {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(16, 185, 129, 0.2), stop:1 rgba(5, 150, 105, 0.1));
                border-radius: 8px;
                padding: 4px;
            }
        """)
        positions_header_layout = QHBoxLayout(positions_header)
        positions_header_layout.setContentsMargins(12, 8, 12, 8)

        positions_title = QLabel("当前持仓")
        positions_title.setStyleSheet("font-size: 16px; font-weight: 700; color: #34D399;")

        positions_header_layout.addWidget(positions_title)
        positions_header_layout.addStretch()

        positions_group_layout.addWidget(positions_header)

        # 现代化持仓表格
        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(5)
        self.positions_table.setHorizontalHeaderLabels(['交易对', '持仓量', '开仓价格', '标记价格', '未实现盈亏'])
        self.positions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.positions_table.setAlternatingRowColors(True)
        self.positions_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.positions_table.setFixedHeight(250)  # 设置与交易日志相同的高度
        self.positions_table.setStyleSheet("""
            QTableWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                border: 2px solid #334155;
                border-radius: 12px;
                gridline-color: rgba(51, 65, 85, 0.5);
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid rgba(51, 65, 85, 0.3);
                color: #F8FAFC;
                font-weight: 500;
            }
            QTableWidget::item:alternate {
                background-color: rgba(30, 41, 59, 0.3);
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(16, 185, 129, 0.3), stop:1 rgba(5, 150, 105, 0.2));
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #334155, stop:1 #1E293B);
                color: #CBD5E1;
                padding: 12px 8px;
                border: none;
                border-right: 1px solid #475569;
                border-bottom: 2px solid #10B981;
                font-weight: 700;
                font-size: 12px;
            }
        """)
        positions_group_layout.addWidget(self.positions_table)
        positions_group.setLayout(positions_group_layout)

        # 现代化委托订单面板
        orders_group = QGroupBox()
        orders_group.setStyleSheet("QGroupBox { border: none; background: transparent; }")
        orders_group_layout = QVBoxLayout()
        orders_group_layout.setContentsMargins(0, 0, 0, 0)
        orders_group_layout.setSpacing(8)

        # 现代化订单标题栏
        orders_header = QWidget()
        orders_header.setObjectName("orders_header")
        orders_header.setStyleSheet("""
            #orders_header {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2), stop:1 rgba(220, 38, 38, 0.1));
                border-radius: 8px;
                padding: 4px;
            }
        """)
        orders_header_layout = QHBoxLayout(orders_header)
        orders_header_layout.setContentsMargins(12, 8, 12, 8)

        orders_title = QLabel("委托订单")
        orders_title.setStyleSheet("font-size: 16px; font-weight: 700; color: #F87171;")

        orders_header_layout.addWidget(orders_title)
        orders_header_layout.addStretch()

        orders_group_layout.addWidget(orders_header)

        # 现代化订单表格
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels([
            '订单ID', '交易对', '方向', '类型', '价格', '数量', '状态'
        ])
        self.orders_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.orders_table.setFixedHeight(250)  # 设置与交易日志相同的高度
        self.orders_table.setStyleSheet("""
            QTableWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                border: 2px solid #334155;
                border-radius: 12px;
                gridline-color: rgba(51, 65, 85, 0.5);
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid rgba(51, 65, 85, 0.3);
                color: #F8FAFC;
                font-weight: 500;
            }
            QTableWidget::item:alternate {
                background-color: rgba(30, 41, 59, 0.3);
            }
            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(239, 68, 68, 0.3), stop:1 rgba(220, 38, 38, 0.2));
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #334155, stop:1 #1E293B);
                color: #CBD5E1;
                padding: 12px 8px;
                border: none;
                border-right: 1px solid #475569;
                border-bottom: 2px solid #EF4444;
                font-weight: 700;
                font-size: 12px;
            }
        """)
        orders_group_layout.addWidget(self.orders_table)

        # 现代化按钮布局
        orders_button_layout = QHBoxLayout()
        orders_button_layout.setContentsMargins(0, 12, 0, 0)
        orders_button_layout.setSpacing(12)

        # 现代化取消订单按钮
        cancel_button = QPushButton("取消选中订单")
        cancel_button.setProperty("type", "delete")
        cancel_button.setFixedHeight(36)
        cancel_button.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: 600;
                border-radius: 8px;
                padding: 8px 16px;
            }
        """)
        cancel_button.clicked.connect(self.cancel_selected_orders)
        orders_button_layout.addWidget(cancel_button)

        # 现代化刷新订单按钮
        refresh_button = QPushButton("刷新订单数据")
        refresh_button.setProperty("type", "primary")
        refresh_button.setFixedHeight(36)
        refresh_button.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: 600;
                border-radius: 8px;
                padding: 8px 16px;
            }
        """)
        refresh_button.clicked.connect(self.refresh_order_data)
        orders_button_layout.addWidget(refresh_button)

        orders_button_layout.addStretch()

        orders_group_layout.addLayout(orders_button_layout)
        orders_group.setLayout(orders_group_layout)

        # 将面板添加到分割器
        positions_splitter.addWidget(positions_group)
        positions_splitter.addWidget(orders_group)
        positions_splitter.setStretchFactor(0, 1)  # 持仓表格
        positions_splitter.setStretchFactor(1, 1)  # 订单表格
        account_layout.addWidget(positions_splitter)

        # 设置滚动内容并添加到滚动区域
        account_scroll_area.setWidget(account_scroll_content)
        account_main_layout.addWidget(account_scroll_area)

        right_panel.addTab(account_tab, "💰 账户资产")

        # 现代化AI交易标签页
        ai_trading_tab = QWidget()
        ai_trading_tab.setObjectName("ai_trading_tab")
        ai_trading_tab.setStyleSheet("""
            #ai_trading_tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                border-radius: 12px;
            }
        """)
        ai_trading_layout = QVBoxLayout(ai_trading_tab)
        ai_trading_layout.setContentsMargins(16, 16, 16, 16)
        ai_trading_layout.setSpacing(12)

        # 现代化滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setObjectName("modern_scroll")
        scroll_area.setStyleSheet("""
            #modern_scroll {
                border: none;
                background: transparent;
            }
            #modern_scroll > QWidget > QWidget {
                background: transparent;
            }
        """)

        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_content_layout = QVBoxLayout(scroll_content)
        scroll_content_layout.setContentsMargins(10, 10, 10, 10)
        scroll_content_layout.setSpacing(12)

        # 添加动态AI交易状态卡片
        ai_status_card = QWidget()
        ai_status_card.setObjectName("ai_status_card")
        ai_status_card.setStyleSheet("""
            #ai_status_card {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(139, 92, 246, 0.15), stop:1 rgba(109, 40, 217, 0.08));
                border: 2px solid rgba(139, 92, 246, 0.4);
                border-radius: 12px;
                padding: 8px;
            }
            #ai_status_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(139, 92, 246, 0.25), stop:1 rgba(109, 40, 217, 0.15));
                border: 2px solid rgba(139, 92, 246, 0.6);
            }
        """)
        ai_status_layout = QVBoxLayout(ai_status_card)
        ai_status_layout.setContentsMargins(16, 12, 16, 12)
        ai_status_layout.setSpacing(8)

        # AI状态标题和指示器
        ai_status_header_layout = QHBoxLayout()
        ai_status_header_layout.setSpacing(8)

        ai_status_title = QLabel("🤖 AI交易状态")
        ai_status_title.setStyleSheet("color: #A78BFA; font-size: 14px; font-weight: 700;")

        # AI状态指示器
        self.ai_status_indicator = QLabel("⏳ 待机中")
        self.ai_status_indicator.setStyleSheet("""
            color: #94A3B8;
            font-size: 11px;
            font-weight: 600;
            background: rgba(148, 163, 184, 0.2);
            border: 1px solid rgba(148, 163, 184, 0.3);
            border-radius: 8px;
            padding: 3px 8px;
            min-width: 60px;
        """)

        ai_status_header_layout.addWidget(ai_status_title)
        ai_status_header_layout.addStretch()
        ai_status_header_layout.addWidget(self.ai_status_indicator)
        ai_status_layout.addLayout(ai_status_header_layout)

        # AI分析进度条
        ai_progress_layout = QVBoxLayout()
        ai_progress_layout.setSpacing(4)

        ai_progress_label = QLabel("分析进度:")
        ai_progress_label.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        # 进度条容器
        self.ai_progress_container = QWidget()
        self.ai_progress_container.setFixedHeight(8)
        self.ai_progress_container.setStyleSheet("""
            background: rgba(51, 65, 85, 0.3);
            border-radius: 4px;
        """)

        # 进度条填充
        self.ai_progress_fill = QWidget(self.ai_progress_container)
        self.ai_progress_fill.setFixedHeight(8)
        self.ai_progress_fill.setFixedWidth(0)
        self.ai_progress_fill.setStyleSheet("""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #A78BFA, stop:0.5 #8B5CF6, stop:1 #7C3AED);
            border-radius: 4px;
        """)

        ai_progress_layout.addWidget(ai_progress_label)
        ai_progress_layout.addWidget(self.ai_progress_container)
        ai_status_layout.addLayout(ai_progress_layout)

        # AI信心度显示
        ai_confidence_layout = QHBoxLayout()
        ai_confidence_layout.setSpacing(8)

        confidence_label = QLabel("AI信心度:")
        confidence_label.setStyleSheet("color: #F8FAFC; font-size: 11px; font-weight: 500;")

        self.ai_confidence_label = QLabel("0%")
        self.ai_confidence_label.setStyleSheet("""
            color: #94A3B8;
            font-size: 11px;
            font-weight: 600;
            background: rgba(148, 163, 184, 0.1);
            border-radius: 6px;
            padding: 2px 8px;
            min-width: 35px;
        """)

        ai_confidence_layout.addWidget(confidence_label)
        ai_confidence_layout.addWidget(self.ai_confidence_label)
        ai_confidence_layout.addStretch()
        ai_status_layout.addLayout(ai_confidence_layout)

        scroll_content_layout.addWidget(ai_status_card)

        # 添加动态价格监控卡片
        price_monitor_card = QWidget()
        price_monitor_card.setObjectName("price_monitor_card")
        price_monitor_card.setStyleSheet("""
            #price_monitor_card {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(245, 158, 11, 0.15), stop:1 rgba(217, 119, 6, 0.08));
                border: 2px solid rgba(245, 158, 11, 0.4);
                border-radius: 12px;
                padding: 8px;
            }
            #price_monitor_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(245, 158, 11, 0.25), stop:1 rgba(217, 119, 6, 0.15));
                border: 2px solid rgba(245, 158, 11, 0.6);
            }
        """)
        price_monitor_layout = QVBoxLayout(price_monitor_card)
        price_monitor_layout.setContentsMargins(16, 12, 16, 12)
        price_monitor_layout.setSpacing(8)

        # 价格监控标题
        price_monitor_header_layout = QHBoxLayout()
        price_monitor_header_layout.setSpacing(8)

        price_monitor_title = QLabel("📊 实时价格监控")
        price_monitor_title.setStyleSheet("color: #FBBF24; font-size: 14px; font-weight: 700;")

        # 价格更新频率指示器
        self.price_update_indicator = QLabel("🔄 2s")
        self.price_update_indicator.setStyleSheet("""
            color: #F59E0B;
            font-size: 10px;
            font-weight: 600;
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 8px;
            padding: 2px 6px;
            min-width: 30px;
        """)

        price_monitor_header_layout.addWidget(price_monitor_title)
        price_monitor_header_layout.addStretch()
        price_monitor_header_layout.addWidget(self.price_update_indicator)
        price_monitor_layout.addLayout(price_monitor_header_layout)

        # AI交易价格显示
        ai_price_layout = QHBoxLayout()
        ai_price_layout.setSpacing(12)

        # 当前价格
        current_price_layout = QVBoxLayout()
        current_price_layout.setSpacing(2)

        current_price_label = QLabel("当前价格")
        current_price_label.setStyleSheet("color: #94A3B8; font-size: 10px; font-weight: 500;")

        self.ai_current_price_label = QLabel("$0.00")
        self.ai_current_price_label.setStyleSheet("color: #F59E0B; font-size: 16px; font-weight: 700;")

        current_price_layout.addWidget(current_price_label)
        current_price_layout.addWidget(self.ai_current_price_label)

        # 价格变化
        price_change_layout = QVBoxLayout()
        price_change_layout.setSpacing(2)

        price_change_label = QLabel("24H变化")
        price_change_label.setStyleSheet("color: #94A3B8; font-size: 10px; font-weight: 500;")

        self.ai_price_change_label = QLabel("0.00%")
        self.ai_price_change_label.setStyleSheet("""
            color: #94A3B8;
            font-size: 12px;
            font-weight: 600;
            background: rgba(148, 163, 184, 0.1);
            border-radius: 6px;
            padding: 2px 8px;
        """)

        price_change_layout.addWidget(price_change_label)
        price_change_layout.addWidget(self.ai_price_change_label)

        # 价格趋势指示器
        trend_layout = QVBoxLayout()
        trend_layout.setSpacing(2)

        trend_label = QLabel("趋势")
        trend_label.setStyleSheet("color: #94A3B8; font-size: 10px; font-weight: 500;")

        self.ai_trend_indicator = QLabel("●")
        self.ai_trend_indicator.setStyleSheet("color: #94A3B8; font-size: 20px; font-weight: 700;")

        trend_layout.addWidget(trend_label)
        trend_layout.addWidget(self.ai_trend_indicator)

        ai_price_layout.addLayout(current_price_layout)
        ai_price_layout.addLayout(price_change_layout)
        ai_price_layout.addLayout(trend_layout)
        ai_price_layout.addStretch()
        price_monitor_layout.addLayout(ai_price_layout)

        scroll_content_layout.addWidget(price_monitor_card)
        
        # AI交易控制面板 - 现代化卡片设计
        control_card = QWidget()
        control_card.setObjectName("control_card")
        control_card.setStyleSheet("""
            #control_card {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(16, 185, 129, 0.15), stop:1 rgba(5, 150, 105, 0.08));
                border: 2px solid rgba(16, 185, 129, 0.4);
                border-radius: 12px;
                padding: 8px;
            }
            #control_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(16, 185, 129, 0.25), stop:1 rgba(5, 150, 105, 0.15));
                border: 2px solid rgba(16, 185, 129, 0.6);
            }
        """)
        control_layout = QVBoxLayout(control_card)
        control_layout.setContentsMargins(16, 12, 16, 12)
        control_layout.setSpacing(12)

        # 控制面板标题
        control_header_layout = QHBoxLayout()
        control_header_layout.setSpacing(8)

        control_title = QLabel("⚙️ AI交易控制")
        control_title.setStyleSheet("color: #34D399; font-size: 14px; font-weight: 700;")

        # 交易模式指示器
        self.trading_mode_indicator = QLabel("手动模式")
        self.trading_mode_indicator.setStyleSheet("""
            color: #94A3B8;
            font-size: 10px;
            font-weight: 600;
            background: rgba(148, 163, 184, 0.2);
            border: 1px solid rgba(148, 163, 184, 0.3);
            border-radius: 8px;
            padding: 2px 6px;
            min-width: 50px;
        """)

        control_header_layout.addWidget(control_title)
        control_header_layout.addStretch()
        control_header_layout.addWidget(self.trading_mode_indicator)
        control_layout.addLayout(control_header_layout)

        # 创建控制网格
        control_grid = QGridLayout()
        control_grid.setVerticalSpacing(12)
        control_grid.setHorizontalSpacing(12)
        
        # 动态交易对选择
        symbol_layout = QVBoxLayout()
        symbol_layout.setSpacing(6)

        symbol_header_layout = QHBoxLayout()
        symbol_header_layout.setSpacing(8)

        symbol_label = QLabel("🔄 交易对:")
        symbol_label.setStyleSheet("color: #F8FAFC; font-size: 12px; font-weight: 600;")

        # 交易对状态指示器
        self.ai_symbol_status = QLabel("主流币")
        self.ai_symbol_status.setStyleSheet("""
            color: #3B82F6;
            font-size: 9px;
            font-weight: 600;
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 6px;
            padding: 1px 4px;
        """)

        symbol_header_layout.addWidget(symbol_label)
        symbol_header_layout.addStretch()
        symbol_header_layout.addWidget(self.ai_symbol_status)
        symbol_layout.addLayout(symbol_header_layout)

        self.ai_trading_symbol_combo = QComboBox()
        self.ai_trading_symbol_combo.addItems([
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT',  # 主流币
            'SOL/USDT', 'XRP/USDT', 'ADA/USDT',  # 其他热门币
            'DOGE/USDT', 'SHIB/USDT',            # 迷因币
            'DOT/USDT', 'AVAX/USDT',             # 公链
            'LTC/USDT', 'BCH/USDT',              # 老牌币
            'MATIC/USDT', 'LINK/USDT',           # 其他
            'ATOM/USDT', 'UNI/USDT',             # DeFi
            'TRX/USDT', 'ETC/USDT'               # 其他
        ])
        self.ai_trading_symbol_combo.currentTextChanged.connect(self.on_ai_symbol_changed)
        self.ai_trading_symbol_combo.setFixedHeight(32)
        symbol_layout.addWidget(self.ai_trading_symbol_combo)

        control_grid.addLayout(symbol_layout, 0, 0)

        # 动态交易数量设置
        amount_layout = QVBoxLayout()
        amount_layout.setSpacing(6)

        amount_header_layout = QHBoxLayout()
        amount_header_layout.setSpacing(8)

        amount_label = QLabel("💰 合约数量:")
        amount_label.setStyleSheet("color: #F8FAFC; font-size: 12px; font-weight: 600;")

        # 风险等级指示器
        self.amount_risk_indicator = QLabel("低风险")
        self.amount_risk_indicator.setStyleSheet("""
            color: #10B981;
            font-size: 9px;
            font-weight: 600;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 1px 4px;
        """)

        amount_header_layout.addWidget(amount_label)
        amount_header_layout.addStretch()
        amount_header_layout.addWidget(self.amount_risk_indicator)
        amount_layout.addLayout(amount_header_layout)

        self.trade_amount_input = QDoubleSpinBox()
        self.trade_amount_input.setRange(0.01, 1000)
        self.trade_amount_input.setDecimals(2)
        self.trade_amount_input.setValue(0.01)
        self.trade_amount_input.setSingleStep(0.01)
        self.trade_amount_input.setSuffix(" 张")
        self.trade_amount_input.setFixedHeight(32)
        self.trade_amount_input.valueChanged.connect(self.update_amount_risk_indicator)
        amount_layout.addWidget(self.trade_amount_input)

        control_grid.addLayout(amount_layout, 0, 1)
        
        # 动态订单类型选择
        order_type_layout = QVBoxLayout()
        order_type_layout.setSpacing(6)

        order_type_header_layout = QHBoxLayout()
        order_type_header_layout.setSpacing(8)

        order_type_label = QLabel("📋 订单类型:")
        order_type_label.setStyleSheet("color: #F8FAFC; font-size: 12px; font-weight: 600;")

        # 执行速度指示器
        self.execution_speed_indicator = QLabel("快速执行")
        self.execution_speed_indicator.setStyleSheet("""
            color: #10B981;
            font-size: 9px;
            font-weight: 600;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 1px 4px;
        """)

        order_type_header_layout.addWidget(order_type_label)
        order_type_header_layout.addStretch()
        order_type_header_layout.addWidget(self.execution_speed_indicator)
        order_type_layout.addLayout(order_type_header_layout)

        self.order_type_layout = QHBoxLayout()
        self.order_type_layout.setSpacing(12)

        self.market_order_radio = QRadioButton("市价单")
        self.market_order_radio.setChecked(True)
        self.limit_order_radio = QRadioButton("限价单")

        # 现代化单选按钮样式
        order_type_style = """
            QRadioButton {
                color: #F8FAFC;
                font-size: 12px;
                font-weight: 500;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #334155;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
            }
            QRadioButton::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10B981, stop:1 #059669);
                border: 2px solid #34D399;
            }
            QRadioButton::indicator:unchecked:hover {
                border: 2px solid #10B981;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #334155, stop:1 #1E293B);
            }
        """
        self.market_order_radio.setStyleSheet(order_type_style)
        self.limit_order_radio.setStyleSheet(order_type_style)
        self.market_order_radio.toggled.connect(self.update_execution_speed_indicator)

        self.order_type_layout.addWidget(self.market_order_radio)
        self.order_type_layout.addWidget(self.limit_order_radio)
        self.order_type_layout.addStretch()
        order_type_layout.addLayout(self.order_type_layout)

        control_grid.addLayout(order_type_layout, 1, 0)
        
        # 动态入场价格选项
        price_option_layout = QVBoxLayout()
        price_option_layout.setSpacing(6)

        price_option_header_layout = QHBoxLayout()
        price_option_header_layout.setSpacing(8)

        price_option_label = QLabel("💡 入场价格:")
        price_option_label.setStyleSheet("color: #F8FAFC; font-size: 12px; font-weight: 600;")

        # 价格策略指示器
        self.price_strategy_indicator = QLabel("AI智能")
        self.price_strategy_indicator.setStyleSheet("""
            color: #A78BFA;
            font-size: 9px;
            font-weight: 600;
            background: rgba(139, 92, 246, 0.2);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 6px;
            padding: 1px 4px;
        """)

        price_option_header_layout.addWidget(price_option_label)
        price_option_header_layout.addStretch()
        price_option_header_layout.addWidget(self.price_strategy_indicator)
        price_option_layout.addLayout(price_option_header_layout)

        self.price_option_layout = QHBoxLayout()
        self.price_option_layout.setSpacing(12)

        self.use_ai_price_radio = QRadioButton("AI分析价格")
        self.use_ai_price_radio.setChecked(True)
        self.use_current_price_radio = QRadioButton("当前市价")

        self.use_ai_price_radio.setStyleSheet(order_type_style)
        self.use_current_price_radio.setStyleSheet(order_type_style)
        self.use_ai_price_radio.toggled.connect(self.update_price_strategy_indicator)

        self.price_option_layout.addWidget(self.use_ai_price_radio)
        self.price_option_layout.addWidget(self.use_current_price_radio)
        self.price_option_layout.addStretch()
        price_option_layout.addLayout(self.price_option_layout)

        control_grid.addLayout(price_option_layout, 1, 1)

        # 动态杠杆设置
        leverage_layout = QVBoxLayout()
        leverage_layout.setSpacing(6)

        leverage_header_layout = QHBoxLayout()
        leverage_header_layout.setSpacing(8)

        leverage_label = QLabel("⚡ 杠杆倍数:")
        leverage_label.setStyleSheet("color: #F8FAFC; font-size: 12px; font-weight: 600;")

        # 杠杆风险指示器
        self.leverage_risk_indicator = QLabel("安全")
        self.leverage_risk_indicator.setStyleSheet("""
            color: #10B981;
            font-size: 9px;
            font-weight: 600;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 6px;
            padding: 1px 4px;
        """)

        leverage_header_layout.addWidget(leverage_label)
        leverage_header_layout.addStretch()
        leverage_header_layout.addWidget(self.leverage_risk_indicator)
        leverage_layout.addLayout(leverage_header_layout)

        self.ai_leverage_spinbox = QSpinBox()
        self.ai_leverage_spinbox.setRange(1, 125)
        self.ai_leverage_spinbox.setValue(1)
        self.ai_leverage_spinbox.setSuffix("×")
        self.ai_leverage_spinbox.setFixedHeight(32)
        self.ai_leverage_spinbox.valueChanged.connect(self.on_leverage_changed)
        leverage_layout.addWidget(self.ai_leverage_spinbox)

        control_grid.addLayout(leverage_layout, 2, 0)
        
        # 完成控制网格布局
        control_layout.addLayout(control_grid)

        # 动态自动交易开关
        auto_trading_layout = QVBoxLayout()
        auto_trading_layout.setSpacing(8)

        # 自动交易状态显示
        auto_trading_status_layout = QHBoxLayout()
        auto_trading_status_layout.setSpacing(8)

        auto_trading_status_label = QLabel("🤖 自动交易状态:")
        auto_trading_status_label.setStyleSheet("color: #F8FAFC; font-size: 12px; font-weight: 600;")

        self.auto_trading_status_indicator = QLabel("已停止")
        self.auto_trading_status_indicator.setStyleSheet("""
            color: #EF4444;
            font-size: 11px;
            font-weight: 600;
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 6px;
            padding: 2px 8px;
            min-width: 50px;
        """)

        auto_trading_status_layout.addWidget(auto_trading_status_label)
        auto_trading_status_layout.addWidget(self.auto_trading_status_indicator)
        auto_trading_status_layout.addStretch()
        auto_trading_layout.addLayout(auto_trading_status_layout)

        # 自动交易按钮
        self.auto_trading_enabled = False
        self.auto_trading_button = QPushButton("🚀 启动自动交易")
        self.auto_trading_button.setProperty("type", "primary")
        self.auto_trading_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10B981, stop:1 #059669);
                color: white;
                font-weight: 700;
                padding: 14px 20px;
                border: none;
                border-radius: 10px;
                font-size: 14px;
                min-height: 48px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34D399, stop:1 #10B981);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
            QPushButton:disabled {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #374151, stop:1 #1F2937);
                color: #6B7280;
            }
        """)
        self.auto_trading_button.clicked.connect(self.toggle_auto_trading)
        auto_trading_layout.addWidget(self.auto_trading_button)

        control_layout.addLayout(auto_trading_layout)

        # ADX策略控制面板
        adx_strategy_layout = QVBoxLayout()
        adx_strategy_layout.setSpacing(8)

        # ADX策略标题
        adx_title_layout = QHBoxLayout()
        adx_title_label = QLabel("📊 ADX交易策略")
        adx_title_label.setStyleSheet("color: #F8FAFC; font-size: 12px; font-weight: 600;")

        # ADX策略状态指示器
        self.adx_strategy_status = QLabel("已禁用")
        self.adx_strategy_status.setStyleSheet("""
            color: #6B7280;
            font-size: 11px;
            font-weight: 600;
            background: rgba(107, 114, 128, 0.2);
            border: 1px solid rgba(107, 114, 128, 0.3);
            border-radius: 6px;
            padding: 2px 8px;
            min-width: 50px;
        """)

        adx_title_layout.addWidget(adx_title_label)
        adx_title_layout.addWidget(self.adx_strategy_status)
        adx_title_layout.addStretch()
        adx_strategy_layout.addLayout(adx_title_layout)

        # ADX策略开关
        self.adx_strategy_checkbox = QCheckBox("启用ADX策略")
        self.adx_strategy_checkbox.setStyleSheet("""
            QCheckBox {
                color: #F8FAFC;
                font-size: 11px;
                font-weight: 500;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid #4B5563;
                background: transparent;
            }
            QCheckBox::indicator:checked {
                background: #10B981;
                border-color: #10B981;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:hover {
                border-color: #10B981;
            }
        """)
        self.adx_strategy_checkbox.stateChanged.connect(self.toggle_adx_strategy)
        adx_strategy_layout.addWidget(self.adx_strategy_checkbox)

        # ADX参数设置
        adx_params_layout = QHBoxLayout()
        adx_params_layout.setSpacing(10)

        # ADX周期设置
        adx_period_label = QLabel("ADX周期:")
        adx_period_label.setStyleSheet("color: #D1D5DB; font-size: 10px;")
        self.adx_period_spinbox = QSpinBox()
        self.adx_period_spinbox.setRange(5, 50)
        self.adx_period_spinbox.setValue(14)
        self.adx_period_spinbox.setStyleSheet("""
            QSpinBox {
                background: rgba(55, 65, 81, 0.8);
                border: 1px solid #4B5563;
                border-radius: 4px;
                color: #F8FAFC;
                font-size: 10px;
                padding: 2px 4px;
                min-width: 40px;
            }
        """)

        # ADX阈值设置
        adx_threshold_label = QLabel("ADX阈值:")
        adx_threshold_label.setStyleSheet("color: #D1D5DB; font-size: 10px;")
        self.adx_threshold_spinbox = QDoubleSpinBox()
        self.adx_threshold_spinbox.setRange(15.0, 50.0)
        self.adx_threshold_spinbox.setValue(25.0)
        self.adx_threshold_spinbox.setSingleStep(1.0)
        self.adx_threshold_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                background: rgba(55, 65, 81, 0.8);
                border: 1px solid #4B5563;
                border-radius: 4px;
                color: #F8FAFC;
                font-size: 10px;
                padding: 2px 4px;
                min-width: 50px;
            }
        """)

        adx_params_layout.addWidget(adx_period_label)
        adx_params_layout.addWidget(self.adx_period_spinbox)
        adx_params_layout.addWidget(adx_threshold_label)
        adx_params_layout.addWidget(self.adx_threshold_spinbox)
        adx_params_layout.addStretch()

        adx_strategy_layout.addLayout(adx_params_layout)
        control_layout.addLayout(adx_strategy_layout)

        scroll_content_layout.addWidget(control_card)
        
        # 动态交易日志卡片
        log_card = QWidget()
        log_card.setObjectName("log_card")
        log_card.setStyleSheet("""
            #log_card {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(99, 102, 241, 0.15), stop:1 rgba(67, 56, 202, 0.08));
                border: 2px solid rgba(99, 102, 241, 0.4);
                border-radius: 12px;
                padding: 8px;
            }
            #log_card:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(99, 102, 241, 0.25), stop:1 rgba(67, 56, 202, 0.15));
                border: 2px solid rgba(99, 102, 241, 0.6);
            }
        """)
        log_layout = QVBoxLayout(log_card)
        log_layout.setContentsMargins(16, 12, 16, 12)
        log_layout.setSpacing(12)

        # 日志标题和状态
        log_header_layout = QHBoxLayout()
        log_header_layout.setSpacing(8)

        log_title = QLabel("📋 交易日志")
        log_title.setStyleSheet("color: #818CF8; font-size: 14px; font-weight: 700;")

        # 日志状态指示器
        self.log_status_indicator = QLabel("📝 记录中")
        self.log_status_indicator.setStyleSheet("""
            color: #10B981;
            font-size: 10px;
            font-weight: 600;
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 8px;
            padding: 2px 6px;
            min-width: 50px;
        """)

        # 日志计数器
        self.log_counter_label = QLabel("0 条")
        self.log_counter_label.setStyleSheet("""
            color: #6366F1;
            font-size: 10px;
            font-weight: 600;
            background: rgba(99, 102, 241, 0.2);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 8px;
            padding: 2px 6px;
            min-width: 30px;
        """)

        log_header_layout.addWidget(log_title)
        log_header_layout.addStretch()
        log_header_layout.addWidget(self.log_counter_label)
        log_header_layout.addWidget(self.log_status_indicator)
        log_layout.addLayout(log_header_layout)

        # 日志级别过滤器
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(4, 8, 4, 8)

        filter_label = QLabel("过滤级别:")
        filter_label.setStyleSheet("""
            color: #F8FAFC;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            margin-right: 4px;
        """)
        filter_label.setFixedHeight(32)  # 与ComboBox高度一致

        self.log_filter_combo = QComboBox()
        self.log_filter_combo.addItems(["全部", "信息", "警告", "错误"])
        self.log_filter_combo.setFixedHeight(32)
        self.log_filter_combo.setMinimumWidth(100)
        self.log_filter_combo.setMaximumWidth(120)
        self.log_filter_combo.currentTextChanged.connect(self.filter_logs)

        # 为过滤器ComboBox添加特定样式以确保边框完整显示
        self.log_filter_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 2px solid rgba(51, 65, 85, 0.8);
                border-radius: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 0.9), stop:1 rgba(15, 23, 42, 0.8));
                color: #F8FAFC;
                font-size: 12px;
                font-weight: 600;
                min-height: 16px;
                selection-background-color: #3B82F6;
            }
            QComboBox::drop-down {
                border: none;
                padding-right: 12px;
                width: 24px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:1 rgba(59, 130, 246, 0.1));
                border-radius: 6px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #94A3B8;
            }
            QComboBox:hover {
                border: 2px solid rgba(59, 130, 246, 0.8);
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(30, 41, 59, 1.0), stop:1 rgba(51, 65, 85, 0.9));
            }
            QComboBox:focus {
                border: 2px solid #60A5FA;
                outline: none;
            }
            QComboBox QAbstractItemView {
                border: 2px solid rgba(51, 65, 85, 0.8);
                selection-background-color: #3B82F6;
                background: rgba(30, 41, 59, 0.95);
                color: #F8FAFC;
                border-radius: 8px;
                padding: 4px;
                margin: 2px;
            }
        """)

        # 自动滚动开关
        self.auto_scroll_enabled = True
        self.auto_scroll_button = QPushButton("📜 自动滚动")
        self.auto_scroll_button.setCheckable(True)
        self.auto_scroll_button.setChecked(True)
        self.auto_scroll_button.setFixedHeight(28)
        self.auto_scroll_button.setFixedWidth(90)
        self.auto_scroll_button.setStyleSheet("""
            QPushButton {
                font-size: 10px;
                font-weight: 600;
                background: rgba(16, 185, 129, 0.2);
                border: 1px solid rgba(16, 185, 129, 0.3);
                border-radius: 6px;
                color: #10B981;
                padding: 2px 6px;
            }
            QPushButton:checked {
                background: rgba(16, 185, 129, 0.3);
                border: 1px solid rgba(16, 185, 129, 0.5);
            }
            QPushButton:hover {
                background: rgba(16, 185, 129, 0.4);
            }
        """)
        self.auto_scroll_button.toggled.connect(self.toggle_auto_scroll)

        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.log_filter_combo)
        filter_layout.addStretch()
        filter_layout.addWidget(self.auto_scroll_button)
        log_layout.addLayout(filter_layout)

        # 增强的日志文本区域
        self.trading_log = EnhancedTradingLog()
        self.trading_log.setReadOnly(True)
        self.trading_log.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                color: #F8FAFC;
                border: 2px solid #334155;
                border-radius: 12px;
                padding: 16px;
                font-family: "Monaco", "Courier New", monospace;
                font-size: 12px;
                line-height: 1.6;
                selection-background-color: #3B82F6;
            }
            QTextEdit:focus {
                border: 2px solid #60A5FA;
            }
        """)
        self.trading_log.setMinimumHeight(250)
        log_layout.addWidget(self.trading_log)
        
        # 动态按钮布局
        log_button_layout = QHBoxLayout()
        log_button_layout.setSpacing(12)

        # 清空日志按钮
        self.clear_log_button = QPushButton("🗑️ 清空")
        self.clear_log_button.setFixedHeight(32)
        self.clear_log_button.setFixedWidth(80)
        self.clear_log_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #EF4444, stop:1 #DC2626);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 11px;
                font-weight: 600;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #F87171, stop:1 #EF4444);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #DC2626, stop:1 #B91C1C);
            }
        """)
        self.clear_log_button.clicked.connect(self.clear_trading_log)

        # 保存日志按钮
        self.save_log_button = QPushButton("💾 保存")
        self.save_log_button.setFixedHeight(32)
        self.save_log_button.setFixedWidth(80)
        self.save_log_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3B82F6, stop:1 #1D4ED8);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 11px;
                font-weight: 600;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #60A5FA, stop:1 #3B82F6);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1D4ED8, stop:1 #1E40AF);
            }
        """)
        self.save_log_button.clicked.connect(self.save_trading_log)

        # 导出日志按钮
        self.export_log_button = QPushButton("📤 导出")
        self.export_log_button.setFixedHeight(32)
        self.export_log_button.setFixedWidth(80)
        self.export_log_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10B981, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 11px;
                font-weight: 600;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34D399, stop:1 #10B981);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
        """)
        self.export_log_button.clicked.connect(self.export_trading_log)

        # 日志统计信息
        self.log_stats_label = QLabel("总计: 0 | 信息: 0 | 警告: 0 | 错误: 0")
        self.log_stats_label.setStyleSheet("""
            color: #94A3B8;
            font-size: 10px;
            font-weight: 500;
            background: rgba(148, 163, 184, 0.1);
            border-radius: 6px;
            padding: 4px 8px;
        """)

        log_button_layout.addWidget(self.log_stats_label)
        log_button_layout.addStretch()
        log_button_layout.addWidget(self.clear_log_button)
        log_button_layout.addWidget(self.save_log_button)
        log_button_layout.addWidget(self.export_log_button)

        log_layout.addLayout(log_button_layout)
        scroll_content_layout.addWidget(log_card)

        # 连接日志相关信号
        self.trading_log.log_stats_updated.connect(self.update_log_stats)
        self.trading_log.log_added.connect(self.on_log_added)
        
        # 设置滚动内容
        scroll_area.setWidget(scroll_content)
        
        # 将滚动区域添加到主布局
        ai_trading_layout.addWidget(scroll_area)
        
        right_panel.addTab(ai_trading_tab, "🤖 AI交易")

        # 现代化API配置标签页
        api_tab = QWidget()
        api_tab.setObjectName("api_tab")
        api_tab.setStyleSheet("""
            #api_tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                border-radius: 12px;
            }
        """)
        api_layout = QVBoxLayout(api_tab)
        api_layout.setContentsMargins(20, 20, 20, 20)
        api_layout.setSpacing(20)
        
        # API配置面板
        api_group = QGroupBox("API密钥配置")
        api_layout_grid = QGridLayout()
        api_layout_grid.setVerticalSpacing(20)
        api_layout_grid.setContentsMargins(20, 30, 20, 20)
        
        # News API 配置
        api_layout_grid.addWidget(QLabel("News API密钥:"), 0, 0)
        self.news_api_key_input = QLineEdit()
        self.news_api_key_input.setPlaceholderText("请输入News API密钥")
        self.news_api_key_input.setText(os.getenv('NEWS_API_KEY', ''))
        api_layout_grid.addWidget(self.news_api_key_input, 0, 1)
        
        news_button_layout = QHBoxLayout()
        news_button_layout.setSpacing(10)
        
        save_news_button = QPushButton("保存")
        save_news_button.setProperty("type", "neutral")
        save_news_button.clicked.connect(self.save_news_api)
        
        test_news_button = QPushButton("测试")
        test_news_button.setProperty("type", "neutral")
        test_news_button.clicked.connect(self.test_news_api)
        
        delete_news_button = QPushButton("删除")
        delete_news_button.setProperty("type", "delete")
        delete_news_button.clicked.connect(self.delete_news_api)
        
        news_button_layout.addWidget(save_news_button)
        news_button_layout.addWidget(test_news_button)
        news_button_layout.addWidget(delete_news_button)
        api_layout_grid.addLayout(news_button_layout, 0, 2)
        
        # DeepSeek AI API 配置
        api_layout_grid.addWidget(QLabel("DeepSeek AI API密钥:"), 1, 0)
        self.ai_api_key_input = QLineEdit()
        self.ai_api_key_input.setPlaceholderText("请输入DeepSeek AI API密钥")
        self.ai_api_key_input.setText(os.getenv('DEEPSEEK_API_KEY', ''))
        api_layout_grid.addWidget(self.ai_api_key_input, 1, 1)
        
        ai_button_layout = QHBoxLayout()
        ai_button_layout.setSpacing(10)
        
        save_ai_button = QPushButton("保存")
        save_ai_button.setProperty("type", "neutral")
        save_ai_button.clicked.connect(self.save_ai_api)
        
        test_ai_button = QPushButton("测试")
        test_ai_button.setProperty("type", "neutral")
        test_ai_button.clicked.connect(self.test_ai_api)
        
        delete_ai_button = QPushButton("删除")
        delete_ai_button.setProperty("type", "delete")
        delete_ai_button.clicked.connect(self.delete_ai_api)
        
        ai_button_layout.addWidget(save_ai_button)
        ai_button_layout.addWidget(test_ai_button)
        ai_button_layout.addWidget(delete_ai_button)
        api_layout_grid.addLayout(ai_button_layout, 1, 2)
        
        # API说明文本
        api_info = QLabel("配置API密钥用于获取市场新闻和AI分析。这些API密钥将存储在本地环境变量中，不会上传到任何服务器。")
        api_info.setWordWrap(True)
        api_info.setStyleSheet("color: #848E9C; font-size: 13px; margin-top: 20px;")
        api_layout_grid.addWidget(api_info, 2, 0, 1, 3)
        
        api_group.setLayout(api_layout_grid)
        api_layout.addWidget(api_group)
        
        # 添加说明
        instruction_group = QGroupBox("使用说明")
        instruction_layout = QVBoxLayout()
        instruction_layout.setContentsMargins(20, 20, 20, 20)
        
        instruction_text = QTextEdit()
        instruction_text.setReadOnly(True)
        instruction_text.setStyleSheet("""
            QTextEdit {
                background-color: #14151a;
                color: #E6E8EA;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                padding: 15px;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        
        instruction_text.setHtml("""
            <h3 style="color: #F0B90B;">API配置说明</h3>
            <p>为了使用量化交易机器人的全部功能，需要配置以下API：</p>
            <ul>
                <li><b>News API</b> - 用于获取加密货币市场相关新闻</li>
                <li><b>DeepSeek AI API</b> - 用于分析市场数据和新闻，生成交易信号</li>
            </ul>
            <h3 style="color: #F0B90B;">如何获取API密钥</h3>
            <p><b>News API</b>: 访问 <a href="https://newsapi.org" style="color:#E6E8EA;">newsapi.org</a> 注册账号并获取API密钥</p>
            <p><b>DeepSeek AI API</b>: 访问 <a href="https://platform.deepseek.com" style="color:#E6E8EA;">platform.deepseek.com</a> 注册账号并创建API密钥</p>
        """)
        
        instruction_layout.addWidget(instruction_text)
        instruction_group.setLayout(instruction_layout)
        api_layout.addWidget(instruction_group)
        
        # 添加弹性空间
        api_layout.addStretch(1)
        
        right_panel.addTab(api_tab, "⚙️ API配置")

        # 将面板添加到智能分割器
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(right_panel)
        main_splitter.setStretchFactor(0, 3)  # 左侧占3/4，给K线图更多空间
        main_splitter.setStretchFactor(1, 1)  # 右侧占1/4
        main_splitter.setSizes([1100, 400])  # 设置初始大小，适应更大的窗口

        layout.addWidget(main_splitter)

        # 添加动态效果定时器
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_dynamic_effects)
        self.animation_timer.start(2000)  # 每2秒更新一次动态效果
        
    def update_market_data(self) -> None:
        """
        更新市场数据

        定时从交易所获取最新市场数据，并在UI中更新显示。
        使用线程池异步执行，避免阻塞UI线程。
        包含防重复更新和更新频率控制机制。
        """
        try:
            # 使用锁确保线程安全
            with self.update_lock:
                # 防止重复更新
                if self.is_updating:
                    return

                # 检查更新时间间隔
                current_time = time.time()
                if current_time - self.last_update_time < self.update_interval:
                    return

                # 检查线程池是否过载
                if self.thread_pool.activeThreadCount() >= 2:
                    self.log_trading("线程池繁忙，跳过本次更新", level='debug')
                    return

                self.is_updating = True
                self.last_update_time = current_time

            # 使用线程池执行数据获取任务
            worker = Worker(self._fetch_market_data)
            worker.signals.result.connect(self._process_market_data)
            worker.signals.finished.connect(self._update_finished)
            worker.signals.error.connect(self._handle_update_error)
            self.thread_pool.start(worker)

        except Exception as e:
            self.log_trading(f"更新市场数据失败: {str(e)}")
            with self.update_lock:
                self.is_updating = False

    def _fetch_market_data(self) -> Dict[str, Any]:
        """
        在后台线程中获取市场数据

        获取当前选中交易对的K线数据、订单薄、24小时行情等信息

        Returns:
            Dict[str, Any]: 包含K线数据、订单薄、行情等信息的字典
        """
        try:
            symbol = self.symbol_combo.currentText()
            # 清理符号前缀（移除emoji）
            if ' ' in symbol:
                symbol = symbol.split(' ', 1)[1]  # 移除emoji前缀

            # 确保使用一致的格式获取永续合约数据
            base_quote = symbol.split('/')
            base, quote = base_quote[0], base_quote[1]
            binance_symbol = f"{base}{quote}"  # 币安合约格式，如BTCUSDT
            
            # 获取当前选择的时间周期
            timeframe = '15m'  # 默认15分钟
            if hasattr(self, 'chart_widget') and hasattr(self.chart_widget, 'timeframe_combo'):
                selected_tf = self.chart_widget.timeframe_combo.currentText()
                if selected_tf == '5分钟':
                    timeframe = '5m'
                elif selected_tf == '15分钟':
                    timeframe = '15m'
                elif selected_tf == '1小时':
                    timeframe = '1h'
                elif selected_tf == '4小时':
                    timeframe = '4h'
                elif selected_tf == '1天':
                    timeframe = '1d'
            
            # 使用缓存减少API调用 - 优化缓存策略
            cache_key = f"{binance_symbol}_{timeframe}"
            if cache_key in self.data_cache and self.data_cache[cache_key].is_valid(180):  # 延长缓存时间到3分钟
                return self.data_cache[cache_key].data
                
            # 根据时间周期调整获取的K线数量
            limit = 100
            if timeframe == '1h':
                limit = 120
            elif timeframe == '4h':
                limit = 150
            elif timeframe == '1d':
                limit = 180
                
            # 优化API调用策略 - 分批获取数据，减少同时调用
            data = {}

            # 核心数据优先获取，添加超时控制
            try:
                # 设置API调用超时
                self.exchange.timeout = 10000  # 10秒超时
                data['ohlcv'] = self.exchange.fetch_ohlcv(binance_symbol, timeframe, limit=limit)
                data['ticker'] = self.exchange.fetch_ticker(binance_symbol)
            except Exception as e:
                self.log_trading(f"获取核心市场数据失败: {str(e)}", level='warning')
                # 重置超时设置
                self.exchange.timeout = 30000  # 恢复默认30秒超时

            # 账户数据使用独立的更新频率控制，提高响应速度
            update_count = getattr(self, '_update_count', 0) + 1
            self._update_count = update_count

            # 优化：账户数据每2次更新获取一次，提高响应速度
            if update_count % 2 == 0:
                try:
                    # 并行获取账户数据，减少等待时间
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                        # 提交并行任务
                        balance_future = executor.submit(self.exchange.fetch_balance)
                        positions_future = executor.submit(self.exchange.fetch_positions, [binance_symbol])
                        orders_future = executor.submit(self.exchange.fetch_open_orders, symbol=binance_symbol)

                        # 获取结果，设置超时避免长时间等待
                        try:
                            data['balance'] = balance_future.result(timeout=8)  # 增加超时时间
                        except concurrent.futures.TimeoutError:
                            self.log_trading("获取余额超时", level='debug')
                        except Exception as e:
                            self.log_trading(f"获取余额失败: {str(e)}", level='debug')

                        try:
                            data['positions'] = positions_future.result(timeout=8)
                        except concurrent.futures.TimeoutError:
                            self.log_trading("获取持仓超时", level='debug')
                        except Exception as e:
                            self.log_trading(f"获取持仓失败: {str(e)}", level='debug')

                        try:
                            data['orders'] = orders_future.result(timeout=8)
                        except concurrent.futures.TimeoutError:
                            self.log_trading("获取订单超时", level='debug')
                        except Exception as e:
                            self.log_trading(f"获取订单失败: {str(e)}", level='debug')

                except Exception as e:
                    self.log_trading(f"获取账户数据失败: {str(e)}", level='warning')
            
            # 更新缓存
            self.data_cache[cache_key] = CachedData(data)
            return data
            
        except Exception as e:
            raise Exception(f"获取市场数据失败: {str(e)}")

    def _process_market_data(self, data: Dict[str, Any]) -> None:
        """
        处理获取到的市场数据

        将后台线程获取的数据更新到用户界面的各个部分，包括：
        K线图、价格显示、账户信息、持仓信息和订单信息

        Args:
            data: 包含各类市场数据的字典，可能包含ohlcv、ticker、balance、positions、orders等键
        """
        try:
            if not data:
                return

            # 批量UI更新 - 暂停重绘以提高性能
            self.setUpdatesEnabled(False)

            try:
                # 更新K线图
                if data.get('ohlcv'):
                    self.chart_widget.update_data(data['ohlcv'])

                # 更新价格显示（在K线图更新后执行，确保能获取到最新的ADX值）
                if data.get('ticker'):
                    self.update_ai_trading_price()

                # 更新ADX显示（使用K线数据计算）
                if data.get('ohlcv'):
                    self.update_adx_from_ohlcv(data['ohlcv'])

                # 更新账户信息（仅在有数据时更新）
                if data.get('balance'):
                    self.update_account_table(data['balance'])

                # 更新持仓信息（仅在有数据时更新）
                if data.get('positions'):
                    self.update_positions_table(data['positions'])

                # 更新订单信息（仅在有数据时更新）
                if data.get('orders'):
                    self.update_orders_table(data['orders'])

            finally:
                # 恢复UI更新并强制重绘
                self.setUpdatesEnabled(True)
                self.update()

        except Exception as e:
            self.log_trading(f"处理市场数据失败: {str(e)}")
            # 确保在异常情况下也恢复UI更新
            self.setUpdatesEnabled(True)

    def _update_finished(self) -> None:
        """
        更新完成后的处理

        重置更新状态标志，允许下一次数据更新操作
        """
        with self.update_lock:
            self.is_updating = False

    def _handle_update_error(self, error: Exception) -> None:
        """
        处理更新过程中的错误

        Args:
            error: 发生的异常
        """
        with self.update_lock:
            self.is_updating = False
        self.log_trading(f"数据更新错误: {str(error)}", level='warning')

    def animate_background(self) -> None:
        """
        创建动态背景呼吸效果
        """
        try:
            import math

            # 更新动画相位
            self.background_phase = (self.background_phase + 1) % 360

            # 计算呼吸效果的透明度变化
            breath_factor = (math.sin(math.radians(self.background_phase * 2)) + 1) / 2

            # 计算颜色渐变的偏移
            gradient_offset = (math.sin(math.radians(self.background_phase)) + 1) / 2

            # 动态调整背景渐变
            base_opacity = 0.03 + breath_factor * 0.08  # 0.03-0.11之间变化
            border_opacity = 0.2 + breath_factor * 0.3   # 0.2-0.5之间变化

            # 根据市场状态调整颜色主题
            current_symbol = self.symbol_combo.currentText()
            if "BTC" in current_symbol:
                # 比特币主题 - 金色调
                theme_color = f"rgba(245, 158, 11, {base_opacity})"
                border_color = f"rgba(245, 158, 11, {border_opacity})"
            elif "ETH" in current_symbol:
                # 以太坊主题 - 蓝色调
                theme_color = f"rgba(59, 130, 246, {base_opacity})"
                border_color = f"rgba(59, 130, 246, {border_opacity})"
            else:
                # 默认主题 - 紫色调
                theme_color = f"rgba(139, 92, 246, {base_opacity})"
                border_color = f"rgba(139, 92, 246, {border_opacity})"

            # 创建动态背景样式
            dynamic_style = f"""
                QMainWindow {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(11, 14, 17, 1.0),
                        stop:{0.2 + gradient_offset * 0.1} rgba(15, 23, 42, 0.98),
                        stop:{0.4 + gradient_offset * 0.2} {theme_color},
                        stop:{0.6 + gradient_offset * 0.2} rgba(30, 41, 59, 0.95),
                        stop:{0.8 + gradient_offset * 0.1} rgba(15, 23, 42, 0.98),
                        stop:1 rgba(11, 14, 17, 1.0));
                    border: 2px solid {border_color};
                }}
            """

            # 保持其他样式不变，只更新主窗口背景
            current_style = self.styleSheet()

            # 找到并替换QMainWindow部分
            import re
            pattern = r'QMainWindow\s*\{[^}]*\}'
            new_style = re.sub(pattern, dynamic_style.strip(), current_style)

            self.setStyleSheet(new_style)

        except Exception as e:
            # 如果动画出错，不影响主程序运行
            pass

    def cleanup_memory(self) -> None:
        """
        定期清理内存，减少内存占用
        """
        try:
            # 清理过期的缓存数据
            current_time = datetime.now()
            expired_keys = []

            for key, cached_data in self.data_cache.items():
                if not cached_data.is_valid(300):  # 5分钟过期
                    expired_keys.append(key)

            for key in expired_keys:
                del self.data_cache[key]

            if expired_keys:
                self.log_trading(f"清理了 {len(expired_keys)} 个过期缓存项", level='debug')

            # 强制垃圾回收
            gc.collect()

        except Exception as e:
            self.log_trading(f"内存清理失败: {str(e)}", level='warning')

    def update_dynamic_effects(self) -> None:
        """
        更新动态效果

        定期更新UI中的动态元素，如状态指示器、进度条等
        """
        try:
            import random

            # 更新价格趋势指示器
            trend_indicators = ["📈", "📊", "📉", "💹", "⚡"]
            current_trend = random.choice(trend_indicators)
            if hasattr(self, 'price_trend_label'):
                self.price_trend_label.setText(current_trend)

            # 更新交易对状态指示器
            status_indicators = ["📈", "📊", "💎", "🚀", "⭐"]
            current_status = random.choice(status_indicators)
            if hasattr(self, 'symbol_status_label'):
                self.symbol_status_label.setText(current_status)

            # 更新AI状态指示器
            if hasattr(self, 'ai_status_indicator'):
                ai_statuses = ["🤖 AI分析中", "🧠 深度学习", "⚡ 快速计算", "🎯 精准分析", "🔮 预测中"]
                if self.auto_trading_enabled:
                    self.ai_status_indicator.setText(random.choice(ai_statuses))
                    self.ai_status_indicator.setStyleSheet("""
                        color: #10B981;
                        font-size: 11px;
                        font-weight: 600;
                        background: rgba(16, 185, 129, 0.2);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        padding: 3px 8px;
                        min-width: 60px;
                    """)
                else:
                    self.ai_status_indicator.setText("🤖 AI待机")
                    self.ai_status_indicator.setStyleSheet("""
                        color: #94A3B8;
                        font-size: 11px;
                        font-weight: 600;
                        background: rgba(148, 163, 184, 0.2);
                        border: 1px solid rgba(148, 163, 184, 0.3);
                        border-radius: 8px;
                        padding: 3px 8px;
                        min-width: 60px;
                    """)

            # 更新触发状态指示器
            if hasattr(self, 'trigger_status_label'):
                trigger_statuses = ["🎯 监控中", "🔍 扫描市场", "📡 信号检测", "⚡ 实时分析"]
                self.trigger_status_label.setText(random.choice(trigger_statuses))

        except Exception as e:
            self.log_trading(f"更新动态效果失败: {str(e)}", level='debug')

    def on_symbol_changed(self, symbol: str) -> None:
        """
        处理交易对变化事件

        Args:
            symbol: 新选择的交易对
        """
        try:
            # 清理符号前缀
            clean_symbol = symbol
            if ' ' in symbol:
                clean_symbol = symbol.split(' ', 1)[1]  # 移除emoji前缀

            # 更新交易对类型标签
            if clean_symbol.startswith(('BTC', 'ETH', 'BNB')):
                self.symbol_type_label.setText("💎 主流币")
                self.symbol_type_label.setStyleSheet("""
                    color: #3B82F6;
                    font-size: 11px;
                    font-weight: 700;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.25), stop:1 rgba(29, 78, 216, 0.15));
                    border: 2px solid rgba(59, 130, 246, 0.4);
                    border-radius: 10px;
                    padding: 4px 8px;
                    min-width: 50px;
                """)
            elif clean_symbol.startswith(('DOGE', 'SHIB')):
                self.symbol_type_label.setText("🐕 迷因币")
                self.symbol_type_label.setStyleSheet("""
                    color: #F59E0B;
                    font-size: 11px;
                    font-weight: 700;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(245, 158, 11, 0.25), stop:1 rgba(217, 119, 6, 0.15));
                    border: 2px solid rgba(245, 158, 11, 0.4);
                    border-radius: 10px;
                    padding: 4px 8px;
                    min-width: 50px;
                """)
            elif clean_symbol.startswith(('DOT', 'AVAX', 'SOL')):
                self.symbol_type_label.setText("🌐 公链币")
                self.symbol_type_label.setStyleSheet("""
                    color: #10B981;
                    font-size: 11px;
                    font-weight: 700;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(16, 185, 129, 0.25), stop:1 rgba(5, 150, 105, 0.15));
                    border: 2px solid rgba(16, 185, 129, 0.4);
                    border-radius: 10px;
                    padding: 4px 8px;
                    min-width: 50px;
                """)
            else:
                self.symbol_type_label.setText("⭐ 其他币")
                self.symbol_type_label.setStyleSheet("""
                    color: #8B5CF6;
                    font-size: 11px;
                    font-weight: 700;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(139, 92, 246, 0.25), stop:1 rgba(109, 40, 217, 0.15));
                    border: 2px solid rgba(139, 92, 246, 0.4);
                    border-radius: 10px;
                    padding: 4px 8px;
                    min-width: 50px;
                """)

            # 更新K线图
            try:
                # 使用CCXT的默认设置获取期货市场数据
                ohlcv = self.exchange.fetch_ohlcv(clean_symbol, '1m', limit=100)
                self.chart_widget.update_data(ohlcv)

                # 更新动态交易对显示
                if hasattr(self, 'update_dynamic_symbol_display'):
                    self.update_dynamic_symbol_display(clean_symbol)

            except Exception as e:
                self.log_trading(f"更新K线图失败: {str(e)}", level='warning')

            # 同步更新AI交易标签页中的交易对选择
            if hasattr(self, 'ai_trading_symbol_combo') and clean_symbol != self.ai_trading_symbol_combo.currentText():
                self.ai_trading_symbol_combo.blockSignals(True)  # 阻止触发信号
                self.ai_trading_symbol_combo.setCurrentText(clean_symbol)  # 设置相同的交易对
                self.ai_trading_symbol_combo.blockSignals(False)  # 恢复信号处理

                # 更新AI交易界面的实时价格
                if hasattr(self, 'update_ai_trading_price'):
                    self.update_ai_trading_price()

            # 同步更新新闻分析标签页中的交易对选择
            if hasattr(self, 'news_symbol_combo') and clean_symbol != self.news_symbol_combo.currentText():
                self.news_symbol_combo.blockSignals(True)  # 阻止触发信号
                self.news_symbol_combo.setCurrentText(clean_symbol)  # 设置相同的交易对
                self.news_symbol_combo.blockSignals(False)  # 恢复信号处理

            # 立即更新市场数据
            self.update_market_data()
            self.log_trading(f"交易对已切换至: {clean_symbol}")

        except Exception as e:
            self.log_trading(f"切换交易对失败: {str(e)}", level='warning')

    def update_tp_sl_display(self) -> None:
        """
        更新止盈止损显示
        """
        try:
            tp_value = self.tp_spinbox.value()
            sl_value = self.sl_spinbox.value()

            # 更新止盈状态
            if tp_value <= 1.0:
                self.tp_status_label.setText("🛡️ 保守")
                self.tp_status_label.setStyleSheet("""
                    color: #10B981;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(16, 185, 129, 0.2);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            elif tp_value <= 2.0:
                self.tp_status_label.setText("⚖️ 适中")
                self.tp_status_label.setStyleSheet("""
                    color: #F59E0B;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(245, 158, 11, 0.2);
                    border: 1px solid rgba(245, 158, 11, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            else:
                self.tp_status_label.setText("⚡ 激进")
                self.tp_status_label.setStyleSheet("""
                    color: #EF4444;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(239, 68, 68, 0.2);
                    border: 1px solid rgba(239, 68, 68, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)

            # 更新止损状态
            if sl_value <= 1.0:
                self.sl_status_label.setText("🛡️ 保守")
                self.sl_status_label.setStyleSheet("""
                    color: #10B981;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(16, 185, 129, 0.2);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            elif sl_value <= 2.0:
                self.sl_status_label.setText("⚖️ 适中")
                self.sl_status_label.setStyleSheet("""
                    color: #F59E0B;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(245, 158, 11, 0.2);
                    border: 1px solid rgba(245, 158, 11, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            else:
                self.sl_status_label.setText("⚡ 激进")
                self.sl_status_label.setStyleSheet("""
                    color: #EF4444;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(239, 68, 68, 0.2);
                    border: 1px solid rgba(239, 68, 68, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)

            # 计算风险比率
            if sl_value > 0:
                ratio = tp_value / sl_value
                self.risk_ratio_label.setText(f"1:{ratio:.1f}")

                # 更新风险等级
                if ratio >= 2.0:
                    self.risk_level_label.setText("🟢 低风险")
                    self.risk_level_label.setStyleSheet("""
                        color: #10B981;
                        font-size: 11px;
                        font-weight: 700;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(16, 185, 129, 0.25), stop:1 rgba(5, 150, 105, 0.15));
                        border: 2px solid rgba(16, 185, 129, 0.4);
                        border-radius: 10px;
                        padding: 4px 8px;
                        min-width: 60px;
                    """)
                elif ratio >= 1.5:
                    self.risk_level_label.setText("🟡 中等风险")
                    self.risk_level_label.setStyleSheet("""
                        color: #F59E0B;
                        font-size: 11px;
                        font-weight: 700;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(245, 158, 11, 0.25), stop:1 rgba(217, 119, 6, 0.15));
                        border: 2px solid rgba(245, 158, 11, 0.4);
                        border-radius: 10px;
                        padding: 4px 8px;
                        min-width: 60px;
                    """)
                else:
                    self.risk_level_label.setText("🔴 高风险")
                    self.risk_level_label.setStyleSheet("""
                        color: #EF4444;
                        font-size: 11px;
                        font-weight: 700;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 rgba(239, 68, 68, 0.25), stop:1 rgba(220, 38, 38, 0.15));
                        border: 2px solid rgba(239, 68, 68, 0.4);
                        border-radius: 10px;
                        padding: 4px 8px;
                        min-width: 60px;
                    """)

        except Exception as e:
            self.log_trading(f"更新止盈止损显示失败: {str(e)}", level='warning')

    def set_quick_tp_sl(self, tp_value: float, sl_value: float) -> None:
        """
        快速设置止盈止损

        Args:
            tp_value: 止盈百分比
            sl_value: 止损百分比
        """
        try:
            self.tp_spinbox.setValue(tp_value)
            self.sl_spinbox.setValue(sl_value)
            self.update_tp_sl_display()
            self.log_trading(f"快速设置止盈止损: TP={tp_value}%, SL={sl_value}%")
        except Exception as e:
            self.log_trading(f"快速设置止盈止损失败: {str(e)}", level='warning')

    def apply_tp_sl_settings(self) -> None:
        """
        应用止盈止损设置
        """
        try:
            tp_value = self.tp_spinbox.value()
            sl_value = self.sl_spinbox.value()

            # 更新内存变量
            self.tp_percent = tp_value
            self.sl_percent = sl_value

            # 保存到配置文件
            config = {
                'tp_percent': tp_value,
                'sl_percent': sl_value
            }

            with open('config.json', 'w') as f:
                json.dump(config, f, indent=4)

            # 同时更新trading_settings.json
            try:
                if os.path.exists('trading_settings.json'):
                    with open('trading_settings.json', 'r', encoding='utf-8') as f:
                        trading_config = json.load(f)

                    trading_config['tp_percent'] = tp_value
                    trading_config['sl_percent'] = sl_value

                    with open('trading_settings.json', 'w', encoding='utf-8') as f:
                        json.dump(trading_config, f, indent=4, ensure_ascii=False)
            except Exception as e:
                self.log_trading(f"更新trading_settings.json失败: {str(e)}", level='warning')

            self.log_trading(f"止盈止损设置已应用并保存: 止盈={tp_value}%, 止损={sl_value}%")
            self.log_trading(f"当前生效的设置: self.tp_percent={self.tp_percent}%, self.sl_percent={self.sl_percent}%")

            # 显示确认消息
            self.show_message_signal.emit("设置成功", f"止盈止损已设置并保存: TP={tp_value}%, SL={sl_value}%", "info")

        except Exception as e:
            self.log_trading(f"应用止盈止损设置失败: {str(e)}", level='error')
            self.show_message_signal.emit("错误", f"保存止盈止损设置失败: {str(e)}", "error")

    def on_ai_symbol_changed(self, symbol: str) -> None:
        """
        处理AI交易交易对变化

        Args:
            symbol: 新选择的交易对
        """
        try:
            # 清理符号前缀
            clean_symbol = symbol
            if ' ' in symbol:
                clean_symbol = symbol.split(' ', 1)[1]  # 移除emoji前缀

            # 更新AI交易对状态
            if hasattr(self, 'ai_symbol_status'):
                if clean_symbol.startswith(('BTC', 'ETH', 'BNB')):
                    self.ai_symbol_status.setText("💎 主流币")
                elif clean_symbol.startswith(('DOGE', 'SHIB')):
                    self.ai_symbol_status.setText("🐕 迷因币")
                elif clean_symbol.startswith(('DOT', 'AVAX', 'SOL')):
                    self.ai_symbol_status.setText("🌐 公链币")
                else:
                    self.ai_symbol_status.setText("⭐ 其他币")

            # 更新AI交易价格
            if hasattr(self, 'update_ai_trading_price'):
                self.update_ai_trading_price()

            # 更新交易对状态指示器
            if hasattr(self, 'update_ai_symbol_status'):
                self.update_ai_symbol_status(clean_symbol)

            # 同步更新主界面的交易对选择
            if hasattr(self, 'symbol_combo') and clean_symbol != self.symbol_combo.currentText():
                self.symbol_combo.blockSignals(True)  # 阻止触发信号
                # 查找带emoji前缀的对应项
                for i in range(self.symbol_combo.count()):
                    item_text = self.symbol_combo.itemText(i)
                    if ' ' in item_text and item_text.split(' ', 1)[1] == clean_symbol:
                        self.symbol_combo.setCurrentIndex(i)
                        break
                else:
                    # 如果找不到带emoji的，直接设置
                    self.symbol_combo.setCurrentText(clean_symbol)
                self.symbol_combo.blockSignals(False)  # 恢复信号处理

                # 更新K线图
                try:
                    ohlcv = self.exchange.fetch_ohlcv(clean_symbol, '1m', limit=100)
                    self.chart_widget.update_data(ohlcv)
                except Exception as e:
                    self.log_trading(f"K线图更新失败: {str(e)}", level='warning')

            self.log_trading(f"AI交易对已切换至: {clean_symbol}")

        except Exception as e:
            self.log_trading(f"切换AI交易对失败: {str(e)}", level='warning')

    def update_amount_risk_indicator(self, amount: float) -> None:
        """
        更新交易数量风险指示器

        Args:
            amount: 交易数量
        """
        try:
            if amount <= 0.1:
                self.amount_risk_indicator.setText("🟢 低风险")
                self.amount_risk_indicator.setStyleSheet("""
                    color: #10B981;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(16, 185, 129, 0.2);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            elif amount <= 1.0:
                self.amount_risk_indicator.setText("🟡 中风险")
                self.amount_risk_indicator.setStyleSheet("""
                    color: #F59E0B;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(245, 158, 11, 0.2);
                    border: 1px solid rgba(245, 158, 11, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            else:
                self.amount_risk_indicator.setText("🔴 高风险")
                self.amount_risk_indicator.setStyleSheet("""
                    color: #EF4444;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(239, 68, 68, 0.2);
                    border: 1px solid rgba(239, 68, 68, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
        except Exception as e:
            self.log_trading(f"更新数量风险指示器失败: {str(e)}", level='warning')

    def update_execution_speed_indicator(self, is_market_order: bool) -> None:
        """
        更新执行速度指示器

        Args:
            is_market_order: 是否为市价单
        """
        try:
            if is_market_order:
                self.execution_speed_indicator.setText("⚡ 快速执行")
                self.execution_speed_indicator.setStyleSheet("""
                    color: #10B981;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(16, 185, 129, 0.2);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            else:
                self.execution_speed_indicator.setText("🎯 精确执行")
                self.execution_speed_indicator.setStyleSheet("""
                    color: #3B82F6;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(59, 130, 246, 0.2);
                    border: 1px solid rgba(59, 130, 246, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
        except Exception as e:
            self.log_trading(f"更新执行速度指示器失败: {str(e)}", level='warning')

    def update_price_strategy_indicator(self, use_ai_price: bool) -> None:
        """
        更新价格策略指示器

        Args:
            use_ai_price: 是否使用AI价格
        """
        try:
            if use_ai_price:
                self.price_strategy_indicator.setText("🤖 AI智能")
                self.price_strategy_indicator.setStyleSheet("""
                    color: #A78BFA;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(139, 92, 246, 0.2);
                    border: 1px solid rgba(139, 92, 246, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            else:
                self.price_strategy_indicator.setText("📊 市价")
                self.price_strategy_indicator.setStyleSheet("""
                    color: #F59E0B;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(245, 158, 11, 0.2);
                    border: 1px solid rgba(245, 158, 11, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
        except Exception as e:
            self.log_trading(f"更新价格策略指示器失败: {str(e)}", level='warning')

    def on_leverage_changed(self, leverage: int) -> None:
        """
        处理杠杆变化

        Args:
            leverage: 杠杆倍数
        """
        try:
            if leverage <= 5:
                self.leverage_risk_indicator.setText("🟢 安全")
                self.leverage_risk_indicator.setStyleSheet("""
                    color: #10B981;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(16, 185, 129, 0.2);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            elif leverage <= 20:
                self.leverage_risk_indicator.setText("🟡 中等")
                self.leverage_risk_indicator.setStyleSheet("""
                    color: #F59E0B;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(245, 158, 11, 0.2);
                    border: 1px solid rgba(245, 158, 11, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
            else:
                self.leverage_risk_indicator.setText("🔴 危险")
                self.leverage_risk_indicator.setStyleSheet("""
                    color: #EF4444;
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba(239, 68, 68, 0.2);
                    border: 1px solid rgba(239, 68, 68, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)

            self.log_trading(f"杠杆已调整至: {leverage}×")

        except Exception as e:
            self.log_trading(f"处理杠杆变化失败: {str(e)}", level='warning')

    def update_account_table(self, balance: Dict[str, Dict[str, float]]) -> None:
        """
        更新账户信息表格（优化版本）

        显示账户中各种资产的可用量、已用量和总量，
        并计算和显示总资产价值

        Args:
            balance: 包含账户余额信息的字典，格式为
                     {'free': {...}, 'used': {...}, 'total': {...}}
        """
        try:
            # 优化：只显示有余额的资产，减少表格行数
            significant_assets = []
            total_usdt = 0
            total_btc = 0

            # 预处理数据，只保留有意义的资产
            for currency in balance['total']:
                total_value = balance['total'][currency]
                if total_value > 0:
                    # 过滤掉极小的余额（小于0.001的非主要币种）
                    if currency in ['USDT', 'BTC', 'ETH', 'BNB'] or total_value >= 0.001:
                        significant_assets.append(currency)
                        if currency == 'USDT':
                            total_usdt += total_value
                        elif currency == 'BTC':
                            total_btc += total_value

            # 快速更新资产摘要（优先显示）
            self.usdt_value.setText(f"${total_usdt:.2f}")
            self.btc_value.setText(f"₿{total_btc:.8f}")

            # 优化表格更新 - 批量操作
            self.account_table.setSortingEnabled(False)
            self.account_table.setUpdatesEnabled(False)

            # 只在资产数量变化时才重建表格
            current_row_count = len(significant_assets)
            if self.account_table.rowCount() != current_row_count:
                self.account_table.setRowCount(current_row_count)

            # 批量更新表格数据
            for row, currency in enumerate(significant_assets):
                free_value = balance['free'][currency]
                used_value = balance['used'][currency]
                total_value = balance['total'][currency]

                # 设置精度
                if currency in ['BTC', 'ETH']:
                    precision = 8
                elif currency == 'USDT':
                    precision = 2
                else:
                    precision = 4

                # 只在需要时创建新的表格项
                if not self.account_table.item(row, 0):
                    currency_item = QTableWidgetItem(currency)
                    currency_item.setForeground(QColor('#F0B90B') if currency in ['USDT', 'BTC', 'ETH'] else QColor('#E6E8EA'))
                    self.account_table.setItem(row, 0, currency_item)
                else:
                    self.account_table.item(row, 0).setText(currency)

                # 更新数值
                items = [
                    (1, f"{free_value:.{precision}f}"),
                    (2, f"{used_value:.{precision}f}"),
                    (3, f"{total_value:.{precision}f}")
                ]

                for col, text in items:
                    if not self.account_table.item(row, col):
                        self.account_table.setItem(row, col, QTableWidgetItem(text))
                    else:
                        self.account_table.item(row, col).setText(text)

            # 异步更新未实现盈亏（避免阻塞）
            QTimer.singleShot(100, self.update_pnl_display)

        except Exception as e:
            self.log_trading(f"更新账户表格失败: {str(e)}", level='error')
        finally:
            # 恢复表格更新
            self.account_table.setUpdatesEnabled(True)
            self.account_table.setSortingEnabled(True)

    def update_pnl_display(self):
        """异步更新未实现盈亏显示"""
        try:
            total_pnl = 0
            for row in range(self.positions_table.rowCount()):
                pnl_item = self.positions_table.item(row, 4)
                if pnl_item:
                    try:
                        pnl_value = float(pnl_item.text())
                        total_pnl += pnl_value
                    except:
                        pass

            # 更新显示
            self.pnl_value.setText(f"${total_pnl:.2f}")
            if total_pnl >= 0:
                self.pnl_value.setStyleSheet("color: #10B981; font-size: 20px; font-weight: 700;")
            else:
                self.pnl_value.setStyleSheet("color: #EF4444; font-size: 20px; font-weight: 700;")
        except Exception as e:
            self.log_trading(f"更新未实现盈亏失败: {str(e)}", level='debug')

    def quick_refresh_account(self):
        """快速刷新账户信息（优化版本）"""
        try:
            # 防止重复更新
            if self.is_account_updating:
                return

            # 检查更新时间间隔
            current_time = time.time()
            if current_time - self.last_account_update_time < self.account_update_interval:
                return

            self.is_account_updating = True
            self.last_account_update_time = current_time

            # 更新状态指示器为更新中
            self.account_status_indicator.setStyleSheet("color: #F59E0B; font-size: 14px; font-weight: bold;")
            self.account_status_indicator.setToolTip("正在更新账户数据...")

            # 检查缓存
            cache_key = "account_data"
            if cache_key in self._account_data_cache:
                cached_data = self._account_data_cache[cache_key]
                if cached_data.is_valid(30):  # 30秒缓存
                    self._process_quick_account_data(cached_data.data)
                    self.is_account_updating = False
                    # 恢复正常状态
                    self.account_status_indicator.setStyleSheet("color: #10B981; font-size: 14px; font-weight: bold;")
                    self.account_status_indicator.setToolTip("账户数据正常")
                    return

            # 使用线程池快速获取账户数据
            worker = Worker(self._quick_fetch_account_data)
            worker.signals.result.connect(self._process_quick_account_data)
            worker.signals.error.connect(self._handle_quick_refresh_error)
            worker.signals.finished.connect(self._account_update_finished)
            self.thread_pool.start(worker)

        except Exception as e:
            self.log_trading(f"启动快速刷新失败: {str(e)}", level='error')
            self.is_account_updating = False

    def _quick_fetch_account_data(self):
        """快速获取账户数据"""
        try:
            import concurrent.futures

            # 并行获取账户相关数据
            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                balance_future = executor.submit(self.exchange.fetch_balance)
                positions_future = executor.submit(self.exchange.fetch_positions)

                # 快速获取结果
                balance = balance_future.result(timeout=3)
                positions = positions_future.result(timeout=3)

                return {
                    'balance': balance,
                    'positions': positions
                }

        except Exception as e:
            raise Exception(f"快速获取账户数据失败: {str(e)}")

    def _process_quick_account_data(self, data):
        """处理快速获取的账户数据"""
        try:
            # 缓存数据
            cache_key = "account_data"
            self._account_data_cache[cache_key] = CachedData(data)

            if data.get('balance'):
                self.update_account_table(data['balance'])

            if data.get('positions'):
                self.update_positions_table(data['positions'])

            # 更新状态指示器为正常
            self.account_status_indicator.setStyleSheet("color: #10B981; font-size: 14px; font-weight: bold;")
            self.account_status_indicator.setToolTip("账户数据正常")

        except Exception as e:
            self.log_trading(f"处理账户数据失败: {str(e)}", level='error')
            # 更新状态指示器为错误
            self.account_status_indicator.setStyleSheet("color: #EF4444; font-size: 14px; font-weight: bold;")
            self.account_status_indicator.setToolTip(f"账户数据错误: {str(e)}")

    def _handle_quick_refresh_error(self, error):
        """处理快速刷新错误"""
        self.log_trading(f"快速刷新失败: {str(error)}", level='warning')
        # 更新状态指示器为错误
        self.account_status_indicator.setStyleSheet("color: #EF4444; font-size: 14px; font-weight: bold;")
        self.account_status_indicator.setToolTip(f"更新失败: {str(error)}")

    def _account_update_finished(self):
        """账户更新完成"""
        self.is_account_updating = False

    def update_positions_table(self, positions):
        """更新持仓信息表格"""
        try:
            # 优化表格更新
            self.positions_table.setSortingEnabled(False)
            self.positions_table.setUpdatesEnabled(False)
            self.positions_table.setRowCount(0)
            
            # 确保positions是列表
            if not isinstance(positions, list):
                positions = [positions]
                
            for position in positions:
                # 检查是否有实际持仓
                size = float(position.get('contracts', 0))
                if size != 0:  # 修改判断条件，检查持仓量是否为0
                    row = self.positions_table.rowCount()
                    self.positions_table.insertRow(row)
                    
                    # 获取合约信息
                    symbol = position.get('symbol', '')
                    entry_price = float(position.get('entryPrice', 0))
                    mark_price = float(position.get('markPrice', 0))
                    unrealized_pnl = float(position.get('unrealizedPnl', 0))
                    side = "多" if position.get('side') == 'long' else "空"
                    
                    # 设置表格内容
                    self.positions_table.setItem(row, 0, QTableWidgetItem(f"{symbol} ({side})"))
                    self.positions_table.setItem(row, 1, QTableWidgetItem(f"{size:.4f}"))
                    self.positions_table.setItem(row, 2, QTableWidgetItem(f"{entry_price:.2f}"))
                    self.positions_table.setItem(row, 3, QTableWidgetItem(f"{mark_price:.2f}"))
                    
                    # 设置PnL颜色
                    pnl_item = QTableWidgetItem(f"{unrealized_pnl:.2f}")
                    pnl_item.setForeground(QColor('#2EBD85') if unrealized_pnl >= 0 else QColor('#F23645'))
                    self.positions_table.setItem(row, 4, pnl_item)

        except Exception as e:
            self.log_trading(f"更新持仓信息失败: {str(e)}")
        finally:
            # 恢复表格更新
            self.positions_table.setUpdatesEnabled(True)
            self.positions_table.setSortingEnabled(True)

    def update_orders_table(self, orders):
        """更新委托订单表格"""
        try:
            # 优化表格更新
            self.orders_table.setSortingEnabled(False)
            self.orders_table.setUpdatesEnabled(False)
            self.orders_table.setRowCount(0)
            
            # 调试日志
            # self.log_trading(f"更新委托订单表格: 收到 {len(orders)} 个订单")
            
            for order in orders:
                row = self.orders_table.rowCount()
                self.orders_table.insertRow(row)
                
                # 获取订单信息
                order_id = order.get('id', '')
                symbol = order.get('symbol', '')
                side = "多" if order.get('side') == 'buy' else "空"
                order_type = order.get('type', '').upper()
                
                # 安全地转换价格和数量，确保不会尝试将None转换为float
                price_value = order.get('price')
                price = float(price_value if price_value is not None else 0)
                
                amount_value = order.get('amount')
                amount = float(amount_value if amount_value is not None else 0)
                
                status = self.translate_order_status(order.get('status', ''))
                
                # 调试日志 - 只记录每个订单的基本信息
                # self.log_trading(f"添加订单: ID={order_id}, 交易对={symbol}, 状态={status}")
                
                # 设置表格内容
                self.orders_table.setItem(row, 0, QTableWidgetItem(order_id))
                self.orders_table.setItem(row, 1, QTableWidgetItem(symbol))
                
                # 设置方向单元格颜色
                side_item = QTableWidgetItem(side)
                side_item.setForeground(QColor('#2EBD85') if side == "多" else QColor('#F23645'))
                self.orders_table.setItem(row, 2, side_item)
                
                self.orders_table.setItem(row, 3, QTableWidgetItem(order_type))
                self.orders_table.setItem(row, 4, QTableWidgetItem(f"{price:.2f}"))
                self.orders_table.setItem(row, 5, QTableWidgetItem(f"{amount:.4f}"))
                self.orders_table.setItem(row, 6, QTableWidgetItem(status))

        except Exception as e:
            self.log_trading(f"更新委托订单表格失败: {str(e)}")
        finally:
            # 恢复表格更新
            self.orders_table.setUpdatesEnabled(True)
            self.orders_table.setSortingEnabled(True)

    def translate_order_status(self, status):
        """翻译订单状态"""
        status_map = {
            'open': '未成交',
            'closed': '已成交',
            'canceled': '已取消',
            'expired': '已过期',
            'rejected': '已拒绝',
            'pending': '等待中'
        }
        return status_map.get(status, status)

    def cancel_selected_orders(self):
        """取消选中的订单"""
        try:
            selected_rows = set(item.row() for item in self.orders_table.selectedItems())
            if not selected_rows:
                self.show_message_signal.emit("提示", "请先选择要取消的订单", "warning")
                return
                
            # 确认取消
            reply = QMessageBox.question(
                self, 
                "确认取消",
                f"确定要取消选中的 {len(selected_rows)} 个订单吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                for row in selected_rows:
                    order_id = self.orders_table.item(row, 0).text()
                    symbol = self.orders_table.item(row, 1).text()
                    
                    try:
                        self.exchange.cancel_order(order_id, symbol)
                        self.log_trading(f"已取消订单 {order_id}")
                    except Exception as e:
                        self.log_trading(f"取消订单 {order_id} 失败: {str(e)}")
                
                # 刷新订单列表 - 等待1秒给交易所一些处理时间
                QTimer.singleShot(1000, self.refresh_order_data)
                
        except Exception as e:
            self.log_trading(f"取消订单操作失败: {str(e)}")
            
    def refresh_order_data(self):
        """手动刷新订单数据"""
        try:
            self.log_trading("正在刷新订单数据...")
            # 获取所有订单信息，使用CCXT的默认设置
            orders = self.exchange.fetch_open_orders()
            self.update_orders_table(orders)
            self.log_trading("订单数据刷新完成")
        except Exception as e:
            self.log_trading(f"刷新订单数据失败: {str(e)}")



    def update_dynamic_symbol_display(self, symbol):
        """更新动态交易对显示"""
        try:
            # 定义交易对分类和属性
            symbol_info = self.get_symbol_info(symbol)

            # 更新交易对类型标签
            symbol_type = symbol_info['type']
            type_color = symbol_info['type_color']
            self.symbol_type_label.setText(symbol_type)
            self.symbol_type_label.setStyleSheet(f"""
                color: {type_color};
                font-size: 10px;
                font-weight: 600;
                background: rgba({symbol_info['type_rgba']}, 0.2);
                border: 1px solid rgba({symbol_info['type_rgba']}, 0.3);
                border-radius: 8px;
                padding: 2px 6px;
                min-width: 40px;
            """)

            # 更新交易对状态指示器
            status_icon = symbol_info['status_icon']
            status_color = symbol_info['status_color']
            self.symbol_status_label.setText(status_icon)
            self.symbol_status_label.setStyleSheet(f"""
                color: {status_color};
                font-size: 14px;
                font-weight: 600;
                background: rgba({symbol_info['status_rgba']}, 0.1);
                border: 1px solid rgba({symbol_info['status_rgba']}, 0.2);
                border-radius: 6px;
                padding: 2px 6px;
                min-width: 20px;
            """)

            # 更新市值排名
            rank = symbol_info['rank']
            rank_color = symbol_info['rank_color']
            self.symbol_rank_label.setText(f"#{rank}")
            self.symbol_rank_label.setStyleSheet(f"color: {rank_color}; font-size: 10px; font-weight: 600;")

            # 更新流动性指示
            liquidity = symbol_info['liquidity']
            liquidity_color = symbol_info['liquidity_color']
            self.liquidity_indicator.setText(liquidity)
            self.liquidity_indicator.setStyleSheet(f"color: {liquidity_color}; font-size: 10px; font-weight: 600;")

            # 更新波动性指示
            volatility = symbol_info['volatility']
            volatility_color = symbol_info['volatility_color']
            self.volatility_indicator.setText(volatility)
            self.volatility_indicator.setStyleSheet(f"color: {volatility_color}; font-size: 10px; font-weight: 600;")

        except Exception as e:
            self.log_trading(f"更新动态交易对显示失败: {str(e)}", level='error')

    def get_symbol_info(self, symbol):
        """获取交易对信息"""
        # 定义交易对分类和属性
        symbol_data = {
            # 主流币
            'BTC/USDT': {
                'type': '主流币', 'type_color': '#F59E0B', 'type_rgba': '245, 158, 11',
                'status_icon': '👑', 'status_color': '#F59E0B', 'status_rgba': '245, 158, 11',
                'rank': 1, 'rank_color': '#F59E0B',
                'liquidity': '🟢', 'liquidity_color': '#10B981',
                'volatility': '中等', 'volatility_color': '#3B82F6'
            },
            'ETH/USDT': {
                'type': '主流币', 'type_color': '#F59E0B', 'type_rgba': '245, 158, 11',
                'status_icon': '💎', 'status_color': '#3B82F6', 'status_rgba': '59, 130, 246',
                'rank': 2, 'rank_color': '#F59E0B',
                'liquidity': '🟢', 'liquidity_color': '#10B981',
                'volatility': '中等', 'volatility_color': '#3B82F6'
            },
            'BNB/USDT': {
                'type': '主流币', 'type_color': '#F59E0B', 'type_rgba': '245, 158, 11',
                'status_icon': '🔥', 'status_color': '#F59E0B', 'status_rgba': '245, 158, 11',
                'rank': 4, 'rank_color': '#F59E0B',
                'liquidity': '🟢', 'liquidity_color': '#10B981',
                'volatility': '中等', 'volatility_color': '#3B82F6'
            },

            # 热门币
            'SOL/USDT': {
                'type': '热门币', 'type_color': '#8B5CF6', 'type_rgba': '139, 92, 246',
                'status_icon': '⚡', 'status_color': '#8B5CF6', 'status_rgba': '139, 92, 246',
                'rank': 5, 'rank_color': '#10B981',
                'liquidity': '🟢', 'liquidity_color': '#10B981',
                'volatility': '较高', 'volatility_color': '#F59E0B'
            },
            'XRP/USDT': {
                'type': '热门币', 'type_color': '#8B5CF6', 'type_rgba': '139, 92, 246',
                'status_icon': '💧', 'status_color': '#3B82F6', 'status_rgba': '59, 130, 246',
                'rank': 6, 'rank_color': '#10B981',
                'liquidity': '🟢', 'liquidity_color': '#10B981',
                'volatility': '中等', 'volatility_color': '#3B82F6'
            },
            'ADA/USDT': {
                'type': '热门币', 'type_color': '#8B5CF6', 'type_rgba': '139, 92, 246',
                'status_icon': '🌿', 'status_color': '#10B981', 'status_rgba': '16, 185, 129',
                'rank': 8, 'rank_color': '#10B981',
                'liquidity': '🟡', 'liquidity_color': '#F59E0B',
                'volatility': '中等', 'volatility_color': '#3B82F6'
            },

            # 迷因币
            'DOGE/USDT': {
                'type': '迷因币', 'type_color': '#EF4444', 'type_rgba': '239, 68, 68',
                'status_icon': '🐕', 'status_color': '#F59E0B', 'status_rgba': '245, 158, 11',
                'rank': 10, 'rank_color': '#10B981',
                'liquidity': '🟡', 'liquidity_color': '#F59E0B',
                'volatility': '很高', 'volatility_color': '#EF4444'
            },
            'SHIB/USDT': {
                'type': '迷因币', 'type_color': '#EF4444', 'type_rgba': '239, 68, 68',
                'status_icon': '🔥', 'status_color': '#EF4444', 'status_rgba': '239, 68, 68',
                'rank': 15, 'rank_color': '#3B82F6',
                'liquidity': '🟡', 'liquidity_color': '#F59E0B',
                'volatility': '极高', 'volatility_color': '#EF4444'
            },

            # 公链币
            'DOT/USDT': {
                'type': '公链币', 'type_color': '#10B981', 'type_rgba': '16, 185, 129',
                'status_icon': '🔗', 'status_color': '#10B981', 'status_rgba': '16, 185, 129',
                'rank': 12, 'rank_color': '#3B82F6',
                'liquidity': '🟡', 'liquidity_color': '#F59E0B',
                'volatility': '较高', 'volatility_color': '#F59E0B'
            },
            'AVAX/USDT': {
                'type': '公链币', 'type_color': '#10B981', 'type_rgba': '16, 185, 129',
                'status_icon': '🏔️', 'status_color': '#3B82F6', 'status_rgba': '59, 130, 246',
                'rank': 18, 'rank_color': '#3B82F6',
                'liquidity': '🟡', 'liquidity_color': '#F59E0B',
                'volatility': '较高', 'volatility_color': '#F59E0B'
            }
        }

        # 默认值（用于未定义的交易对）
        default_info = {
            'type': '其他币', 'type_color': '#94A3B8', 'type_rgba': '148, 163, 184',
            'status_icon': '📊', 'status_color': '#94A3B8', 'status_rgba': '148, 163, 184',
            'rank': 50, 'rank_color': '#94A3B8',
            'liquidity': '🟡', 'liquidity_color': '#F59E0B',
            'volatility': '中等', 'volatility_color': '#3B82F6'
        }

        return symbol_data.get(symbol, default_info)



    def calculate_overall_risk(self, tp_value, sl_value, risk_ratio):
        """计算整体风险等级"""
        # 基于止盈、止损值和风险比率计算综合风险
        if tp_value <= 1.5 and sl_value <= 1.5 and risk_ratio >= 1.5:
            return "低风险"
        elif tp_value <= 2.5 and sl_value <= 2.0 and risk_ratio >= 1.2:
            return "中等风险"
        else:
            return "高风险"

    def update_risk_level_display(self, risk_level):
        """更新风险等级显示"""
        self.risk_level_label.setText(risk_level)

        if risk_level == "低风险":
            self.risk_level_label.setStyleSheet("""
                color: #10B981;
                font-size: 10px;
                font-weight: 600;
                background: rgba(16, 185, 129, 0.2);
                border: 1px solid rgba(16, 185, 129, 0.3);
                border-radius: 8px;
                padding: 2px 6px;
                min-width: 50px;
            """)
        elif risk_level == "中等风险":
            self.risk_level_label.setStyleSheet("""
                color: #F59E0B;
                font-size: 10px;
                font-weight: 600;
                background: rgba(245, 158, 11, 0.2);
                border: 1px solid rgba(245, 158, 11, 0.3);
                border-radius: 8px;
                padding: 2px 6px;
                min-width: 50px;
            """)
        else:  # 高风险
            self.risk_level_label.setStyleSheet("""
                color: #EF4444;
                font-size: 10px;
                font-weight: 600;
                background: rgba(239, 68, 68, 0.2);
                border: 1px solid rgba(239, 68, 68, 0.3);
                border-radius: 8px;
                padding: 2px 6px;
                min-width: 50px;
            """)

    def set_quick_tp_sl(self, tp_value, sl_value):
        """快速设置止盈止损"""
        self.tp_spinbox.setValue(tp_value)
        self.sl_spinbox.setValue(sl_value)
        self.update_tp_sl_display()

    def save_news_api(self):
        """保存News API配置"""
        try:
            api_key = self.news_api_key_input.text()

            if not api_key:
                self.show_message_signal.emit("警告", "请输入News API密钥！", "warning")
                return

            # 读取现有的环境变量
            env_vars = {}
            if os.path.exists('.env'):
                load_dotenv()
                env_vars = {
                    'BINANCE_API_KEY': os.getenv('BINANCE_API_KEY', ''),
                    'BINANCE_SECRET_KEY': os.getenv('BINANCE_SECRET_KEY', ''),
                    'BINANCE_PASSPHRASE': os.getenv('BINANCE_PASSPHRASE', ''),
                    'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY', '')
                }

            # 更新News API密钥
            env_vars['NEWS_API_KEY'] = api_key

            # 保存所有环境变量
            with open('.env', 'w') as f:
                for key, value in env_vars.items():
                    if value:
                        f.write(f"{key}={value}\n")

            # 更新内存中的值
            self.news_api_key_value = api_key
            os.environ['NEWS_API_KEY'] = api_key

            self.show_message_signal.emit("成功", "News API配置已保存！", "info")

        except Exception as e:
            self.show_message_signal.emit("错误", f"保存News API配置失败: {str(e)}", "error")
            
    def delete_news_api(self):
        """删除News API配置"""
        try:
            reply = QMessageBox.question(self, "确认", "确定要删除News API配置吗？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # 读取现有的环境变量
                env_vars = {}
                if os.path.exists('.env'):
                    load_dotenv()
                    env_vars = {
                        'BINANCE_API_KEY': os.getenv('BINANCE_API_KEY', ''),
                        'BINANCE_SECRET_KEY': os.getenv('BINANCE_SECRET_KEY', ''),
                        'BINANCE_PASSPHRASE': os.getenv('BINANCE_PASSPHRASE', ''),
                        'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY', '')
                    }
                    
                    # 删除News API密钥
                    env_vars.pop('NEWS_API_KEY', None)
                    
                    # 保存其他环境变量
                    with open('.env', 'w') as f:
                        for key, value in env_vars.items():
                            if value:
                                f.write(f"{key}={value}\n")
                
                self.news_api_key_input.clear()
                self.news_api_key_value = None
                if 'NEWS_API_KEY' in os.environ:
                    del os.environ['NEWS_API_KEY']
                self.show_message_signal.emit("成功", "News API配置已删除！", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"删除News API配置失败: {str(e)}", "error")
            
    def save_ai_api(self):
        """保存DeepSeek AI API配置"""
        try:
            api_key = self.ai_api_key_input.text()

            if not api_key:
                self.show_message_signal.emit("警告", "请输入DeepSeek AI API密钥！", "warning")
                return

            # 读取现有的环境变量
            env_vars = {}
            if os.path.exists('.env'):
                load_dotenv()
                env_vars = {
                    'BINANCE_API_KEY': os.getenv('BINANCE_API_KEY', ''),
                    'BINANCE_SECRET_KEY': os.getenv('BINANCE_SECRET_KEY', ''),
                    'BINANCE_PASSPHRASE': os.getenv('BINANCE_PASSPHRASE', ''),
                    'NEWS_API_KEY': os.getenv('NEWS_API_KEY', '')
                }

            # 更新DeepSeek AI API密钥
            env_vars['DEEPSEEK_API_KEY'] = api_key

            # 保存所有环境变量
            with open('.env', 'w') as f:
                for key, value in env_vars.items():
                    if value:
                        f.write(f"{key}={value}\n")

            # 更新内存中的值
            self.deepseek_api_key_value = api_key
            os.environ['DEEPSEEK_API_KEY'] = api_key

            self.show_message_signal.emit("成功", "DeepSeek AI API配置已保存！", "info")

        except Exception as e:
            self.show_message_signal.emit("错误", f"保存DeepSeek AI API配置失败: {str(e)}", "error")
            
    def delete_ai_api(self):
        """删除DeepSeek AI API配置"""
        try:
            reply = QMessageBox.question(self, "确认", "确定要删除DeepSeek AI API配置吗？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # 读取现有的环境变量
                env_vars = {}
                if os.path.exists('.env'):
                    load_dotenv()
                    env_vars = {
                        'BINANCE_API_KEY': os.getenv('BINANCE_API_KEY', ''),
                        'BINANCE_SECRET_KEY': os.getenv('BINANCE_SECRET_KEY', ''),
                        'BINANCE_PASSPHRASE': os.getenv('BINANCE_PASSPHRASE', ''),
                        'NEWS_API_KEY': os.getenv('NEWS_API_KEY', '')
                    }
                    
                    # 删除DeepSeek AI API密钥
                    env_vars.pop('DEEPSEEK_API_KEY', None)
                    
                    # 保存其他环境变量
                    with open('.env', 'w') as f:
                        for key, value in env_vars.items():
                            if value:
                                f.write(f"{key}={value}\n")
                
                self.ai_api_key_input.clear()
                self.deepseek_api_key_value = None
                if 'DEEPSEEK_API_KEY' in os.environ:
                    del os.environ['DEEPSEEK_API_KEY']
                self.show_message_signal.emit("成功", "DeepSeek AI API配置已删除！", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"删除DeepSeek AI API配置失败: {str(e)}", "error")


    def make_robust_request(self, url, params=None, headers=None, method='GET', json_data=None, max_retries=3, timeout=(15, 45)):
        """
        创建一个更强大的网络请求方法，具有更好的错误处理和重试机制
        """
        import time
        from urllib3.util.retry import Retry
        from requests.adapters import HTTPAdapter

        for attempt in range(max_retries):
            try:
                # 创建会话对象
                session = requests.Session()

                # 配置重试策略
                retry_strategy = Retry(
                    total=3,
                    backoff_factor=2,  # 指数退避
                    status_forcelist=[429, 500, 502, 503, 504, 520, 521, 522, 523, 524],
                    allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
                )

                # 配置适配器
                adapter = HTTPAdapter(
                    max_retries=retry_strategy,
                    pool_connections=10,
                    pool_maxsize=20
                )

                session.mount("http://", adapter)
                session.mount("https://", adapter)

                # 设置用户代理
                if headers is None:
                    headers = {}
                headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                })

                # 发送请求
                if method.upper() == 'GET':
                    response = session.get(url, params=params, headers=headers, timeout=timeout, verify=True)
                elif method.upper() == 'POST':
                    response = session.post(url, params=params, headers=headers, json=json_data, timeout=timeout, verify=True)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")

                # 检查响应状态
                response.raise_for_status()
                return response

            except requests.exceptions.Timeout as e:
                self.log_trading(f"请求超时 (尝试 {attempt + 1}/{max_retries}): {str(e)}", level='warning')
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1  # 指数退避
                    self.log_trading(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise

            except requests.exceptions.ConnectionError as e:
                self.log_trading(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}", level='warning')
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1
                    self.log_trading(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise

            except requests.exceptions.HTTPError as e:
                self.log_trading(f"HTTP错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}", level='warning')
                if e.response.status_code in [429, 500, 502, 503, 504] and attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1
                    self.log_trading(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise

            except Exception as e:
                self.log_trading(f"未知错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}", level='warning')
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1
                    self.log_trading(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise

        raise Exception(f"在 {max_retries} 次尝试后仍然失败")

    def test_news_api(self):
        """测试News API是否有效"""
        try:
            api_key = self.news_api_key_input.text()

            if not api_key:
                self.show_message_signal.emit("警告", "请先输入News API密钥！", "warning")
                return
            
            # 测试API
            news_url = 'https://newsapi.org/v2/everything'
            news_params = {
                'q': 'cryptocurrency',
                'pageSize': 1,  # 只获取一条新闻进行测试
                'language': 'en',
                'apiKey': api_key
            }
            
            # 使用增强的网络请求
            try:
                response = self.make_robust_request(
                    url=news_url,
                    params=news_params,
                    timeout=(15, 45)  # 更长的超时时间
                )
                
                data = response.json()
                
                if response.status_code == 200 and data.get('status') == 'ok':
                    self.show_message_signal.emit("成功", "News API连接测试成功！", "info")
                else:
                    error_message = data.get('message', '未知错误')
                    raise Exception(error_message)
                    
            except requests.exceptions.SSLError as ssl_err:
                self.log_trading(f"SSL连接错误: {str(ssl_err)}", level='error')
                raise Exception(f"SSL连接错误: {str(ssl_err)}")
                
            except requests.exceptions.ConnectionError as conn_err:
                self.log_trading(f"连接错误: {str(conn_err)}", level='error')
                raise Exception(f"连接错误: {str(conn_err)}")
                
            except requests.exceptions.Timeout as timeout_err:
                self.log_trading(f"请求超时: {str(timeout_err)}", level='error')
                raise Exception(f"请求超时: {str(timeout_err)}")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"News API测试失败: {str(e)}", "error")
            
    def test_ai_api(self):
        """测试DeepSeek AI API是否有效"""
        try:
            api_key = self.ai_api_key_input.text()

            if not api_key:
                self.show_message_signal.emit("警告", "请先输入DeepSeek AI API密钥！", "warning")
                return
            
            # 测试API
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            api_url = "https://api.deepseek.com/v1/chat/completions"
            api_data = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": "Hello"}],
                "temperature": 0.7,
                "max_tokens": 5  # 减少令牌数以加快响应
            }
            
            # 使用增强的网络请求
            try:
                # 使用更安全的会话对象并配置重试
                retries = Retry(
                    total=3,
                    backoff_factor=1,
                    status_forcelist=[429, 500, 502, 503, 504],
                    allowed_methods=["POST"]
                )
                session = requests.Session()
                session.mount('https://', HTTPAdapter(max_retries=retries))
                
                # 添加超时设置
                response = session.post(
                    api_url,
                    json=api_data,
                    headers=headers,
                    timeout=(5, 30)  # (连接超时, 读取超时)
                )
                
                if response.status_code == 200:
                    self.show_message_signal.emit("成功", "DeepSeek AI API连接测试成功！", "info")
                else:
                    error_data = response.json()
                    error_message = error_data.get('error', {}).get('message', '未知错误')
                    self.log_trading(f"AI API错误: {error_message}", level='error')
                    raise Exception(error_message)
                    
            except requests.exceptions.SSLError as ssl_err:
                self.log_trading(f"SSL连接错误: {str(ssl_err)}", level='error')
                raise Exception(f"SSL连接错误: {str(ssl_err)}")
                
            except requests.exceptions.ConnectionError as conn_err:
                self.log_trading(f"连接错误: {str(conn_err)}", level='error')
                raise Exception(f"连接错误: {str(conn_err)}")
                
            except requests.exceptions.Timeout as timeout_err:
                self.log_trading(f"请求超时: {str(timeout_err)}", level='error')
                raise Exception(f"请求超时: {str(timeout_err)}")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"DeepSeek AI API测试失败: {str(e)}", "error")

    def toggle_auto_trading(self):
        """切换自动交易状态"""
        try:
            self.auto_trading_enabled = not self.auto_trading_enabled

            if self.auto_trading_enabled:
                # 检查API配置
                if not self.news_api_key_input.text() or not self.ai_api_key_input.text():
                    self.show_message_signal.emit("警告", "请先配置News API和DeepSeek AI API！", "warning")
                    self.auto_trading_enabled = False
                    self.update_trading_mode_indicator(False)
                    return

                # 检查交易参数
                if self.trade_amount_input.value() <= 0:
                    self.show_message_signal.emit("警告", "请设置有效的交易金额！", "warning")
                    self.auto_trading_enabled = False
                    self.update_trading_mode_indicator(False)
                    return

                # 更新按钮状态
                self.auto_trading_button.setText("🛑 停止自动交易")
                self.auto_trading_button.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #EF4444, stop:1 #DC2626);
                        color: white;
                        font-weight: 700;
                        padding: 14px 20px;
                        border: none;
                        border-radius: 10px;
                        font-size: 14px;
                        min-height: 48px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #F87171, stop:1 #EF4444);
                    }
                """)

                # 更新动态指示器
                self.update_trading_mode_indicator(True)
                self.update_ai_status_display('ready', 100, 85)

                # 禁用设置选项
                self.ai_trading_symbol_combo.setEnabled(False)
                self.trade_amount_input.setEnabled(False)
                self.ai_leverage_spinbox.setEnabled(False)
                self.market_order_radio.setEnabled(False)
                self.limit_order_radio.setEnabled(False)
                self.use_ai_price_radio.setEnabled(False)
                self.use_current_price_radio.setEnabled(False)

                # 启动自动交易
                self.start_auto_trading()

            else:
                # 更新按钮状态
                self.auto_trading_button.setText("🚀 启动自动交易")
                self.auto_trading_button.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #10B981, stop:1 #059669);
                        color: white;
                        font-weight: 700;
                        padding: 14px 20px;
                        border: none;
                        border-radius: 10px;
                        font-size: 14px;
                        min-height: 48px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #34D399, stop:1 #10B981);
                    }
                """)

                # 更新动态指示器
                self.update_trading_mode_indicator(False)
                self.update_ai_status_display('waiting', 0, 0)

                # 启用设置选项
                self.ai_trading_symbol_combo.setEnabled(True)
                self.trade_amount_input.setEnabled(True)
                self.ai_leverage_spinbox.setEnabled(True)
                self.market_order_radio.setEnabled(True)
                self.limit_order_radio.setEnabled(True)
                self.use_ai_price_radio.setEnabled(True)
                self.use_current_price_radio.setEnabled(True)

        except Exception as e:
            self.show_message_signal.emit("错误", f"切换自动交易状态失败: {str(e)}", "error")
            self.auto_trading_enabled = False
            self.auto_trading_button.setText("🚀 启动自动交易")
            self.update_trading_mode_indicator(False)
            self.update_ai_status_display('error', 0, 0)
            
    def start_auto_trading(self):
        """启动自动交易"""
        if not hasattr(self, 'auto_trading_thread') or not self.auto_trading_thread.is_alive():
            self.auto_trading_enabled = True
            self.auto_trading_thread = threading.Thread(target=self.auto_trading_loop, daemon=True)
            self.auto_trading_thread.start()
        else:
            self.log_trading("自动交易已在运行中")

    def auto_trading_loop(self):
        """自动交易循环"""
        error_count = 0
        max_errors = 5
        consecutive_errors = 0
        self.last_trigger_price = None
        initial_analysis = True
        last_error_time = 0

        while self.auto_trading_enabled:
            try:
                # 检查是否需要暂停以防止API限制
                current_time = time.time()
                if consecutive_errors > 0 and current_time - last_error_time < (5 * consecutive_errors):
                    # 指数退避：连续错误越多，等待时间越长
                    wait_time = 5 * consecutive_errors
                    self.log_trading(f"由于连续错误，暂停交易 {wait_time} 秒", level='warning')
                    time.sleep(wait_time)
                    
                # 在每次迭代开始时检查网络连接
                if not hasattr(self, 'exchange_status') or self.exchange_status.text().endswith("未连接"):
                    self.log_trading("交易所连接断开，等待重新连接...", level='warning')
                    self.check_network_status()
                    time.sleep(10)  # 等待网络检查完成
                    continue
                with self.trade_lock:
                    symbol = self.ai_trading_symbol_combo.currentText()
                    base_symbol = symbol.split('/')[0]
                    
                    ticker = self.exchange.fetch_ticker(symbol)
                    current_price = round(ticker['last'], 2)
                    
                    # 获取触发分析阈值
                    trigger_threshold = self.trigger_threshold_spinbox.value()
                    
                    # 触发条件判断
                    trigger_analysis = False
                    if initial_analysis:
                        trigger_analysis = True
                        initial_analysis = False
                        self.log_trading("执行首次分析", level='info')
                    elif self.last_trigger_price is not None:
                        price_change = abs((current_price - self.last_trigger_price) / self.last_trigger_price * 100)
                        price_change = round(price_change, 2)
                        
                        if price_change >= trigger_threshold:
                            trigger_analysis = True
                            self.log_trading(f"价格波动达到{price_change:.2f}%，触发分析", level='info')

                    if trigger_analysis:
                        self.last_trigger_price = current_price
                        # 获取并分析新闻
                        self.log_trading(f"正在分析{base_symbol}相关新闻...")
                        analysis_result = self.get_trading_signal(base_symbol)
                        
                        if analysis_result:
                            trend = analysis_result.get('trend')
                            entry_price = float(analysis_result.get('entry_price', 0))
                            tp_price = float(analysis_result.get('tp_price', 0))
                            sl_price = float(analysis_result.get('sl_price', 0))
                            market_state = analysis_result.get('market_state', 'RANGING_WEAK_TREND')
                            trend_strength = analysis_result.get('trend_strength', 0)
                            
                            # 记录市场状态和趋势强度
                            self.log_trading(f"市场状态: {market_state}, 趋势强度: {trend_strength:.2f}")
                            
                            if trend and entry_price and tp_price and sl_price:
                                # 获取交易对的最小交易数量限制
                                markets = self.exchange.fetch_markets()
                                market = next((m for m in markets if m['symbol'] == symbol), None)
                                if not market:
                                    self.log_trading(f"未找到{symbol}的市场信息")
                                    continue
                                    
                                min_amount = float(market['limits']['amount']['min'])
                                self.log_trading(f"最小交易数量: {min_amount} {base_symbol}")
                                
                                # 直接使用输入的合约数量，但根据市场状态可能调整
                                base_quantity = self.trade_amount_input.value()
                                
                                # 根据市场状态调整交易策略
                                if market_state == "STRONG_UPTREND":
                                    # 在强上涨趋势中
                                    if trend == "看多":
                                        # 顺势交易，可以适当增加仓位
                                        quantity_multiplier = 1.2  # 增加20%仓位
                                        self.log_trading("强上涨趋势中的看多信号，增加20%仓位")
                                    else:  # trend == "看空"
                                        # 逆势交易，需要更谨慎，减少仓位
                                        quantity_multiplier = 0.7  # 减少30%仓位
                                        self.log_trading("强上涨趋势中的看空信号，减少30%仓位")
                                elif market_state == "STRONG_DOWNTREND":
                                    # 在强下跌趋势中
                                    if trend == "看空":
                                        # 顺势交易，可以适当增加仓位
                                        quantity_multiplier = 1.2  # 增加20%仓位
                                        self.log_trading("强下跌趋势中的看空信号，增加20%仓位")
                                    else:  # trend == "看多"
                                        # 逆势交易，需要更谨慎，减少仓位
                                        quantity_multiplier = 0.7  # 减少30%仓位
                                        self.log_trading("强下跌趋势中的看多信号，减少30%仓位")
                                else:  # "RANGING_WEAK_TREND"
                                    # 震荡市场，保持正常仓位
                                    quantity_multiplier = 1.0
                                    self.log_trading("震荡市场，使用标准仓位")
                                
                                # 应用乘数调整仓位
                                quantity = base_quantity * quantity_multiplier
                                
                                # 确保数量至少为最小交易量，并向上取整到0.01
                                quantity = max(min_amount, round(quantity / min_amount) * min_amount)
                                self.log_trading(f"调整后的交易数量: {quantity} {base_symbol}")
                                
                                # 当价格接近目标价格时，再次确认分析
                                price_diff = abs(current_price - entry_price)
                                if price_diff / entry_price < 0.02:  # 2%以内触发确认分析
                                    self.log_trading(f"价格接近目标价格，当前价:{current_price}，正在重新确认分析...")
                                    
                                    # 使用当前价格重新计算止盈止损价格
                                    if trend == '看多':
                                        new_entry_price = current_price
                                        new_tp_price = current_price * 1.007  # 0.7%止盈
                                        new_sl_price = current_price * 0.965  # 3.5%止损
                                    else:  # trend == '看空'
                                        new_entry_price = current_price
                                        new_tp_price = current_price * 0.993  # 0.7%止盈
                                        new_sl_price = current_price * 1.035  # 3.5%止损
                                    
                                    # 进行第二次分析确认趋势
                                    confirm_result = self.get_trading_signal(base_symbol)
                                    
                                    if confirm_result:
                                        confirm_trend = confirm_result.get('trend')
                                        
                                        # 添加趋势判断日志
                                        self.log_trading(
                                            f"分析比较:\n"
                                            f"初次分析 - 趋势: {trend}, 入场价: {entry_price}\n"
                                            f"确认分析 - 趋势: {confirm_trend}, 入场价: {new_entry_price}"
                                        )
                                        
                                        # 如果两次分析趋势一致，使用当前价格执行交易
                                        if confirm_trend == trend:
                                            try:
                                                # 确定交易方向
                                                side = 'buy' if trend == '看多' else 'sell'
                                                
                                                # 在强趋势市场中，即使确认分析的趋势不完全一致，也可能执行交易
                                                execute_trade = True
                                                
                                                # 根据市场状态调整交易确认逻辑
                                                self.log_trading(f"交易确认 - 市场状态: {market_state}, 初始趋势: {trend}, 确认趋势: {confirm_trend}")
                                                
                                                # 根据用户选择确定入场价格
                                                entry_price_to_use = None
                                                tp_price_to_use = None
                                                sl_price_to_use = None
                                                
                                                if self.use_ai_price_radio.isChecked():
                                                    # 使用AI分析的入场价格
                                                    entry_price_to_use = entry_price
                                                    tp_price_to_use = tp_price
                                                    sl_price_to_use = sl_price
                                                    
                                                    self.log_trading(f"使用AI分析入场价格: {entry_price_to_use} (原始AI分析价格)")
                                                else:
                                                    # 使用当前市价作为入场价格
                                                    ticker = self.exchange.fetch_ticker(symbol)
                                                    current_market_price = ticker['last']
                                                    entry_price_to_use = current_market_price
                                                    
                                                    # 根据市场状态调整止盈止损比例
                                                    tp_percent = self.tp_percent
                                                    sl_percent = self.sl_percent

                                                    # 记录当前使用的止盈止损设置
                                                    self.log_trading(f"当前止盈止损设置: TP={tp_percent}%, SL={sl_percent}% (来自用户配置)")
                                                    
                                                    # 在强趋势中，可以调整止盈止损设置
                                                    if market_state == "STRONG_UPTREND" and side == 'buy':
                                                        tp_percent = tp_percent * 1.5
                                                        sl_percent = sl_percent * 0.8
                                                    elif market_state == "STRONG_DOWNTREND" and side == 'sell':
                                                        tp_percent = tp_percent * 1.5
                                                        sl_percent = sl_percent * 0.8
                                                    elif market_state == "RANGING_WEAK_TREND":
                                                        tp_percent = tp_percent * 0.8
                                                        sl_percent = sl_percent * 0.8
                                                    
                                                    # 重新计算止盈止损价格
                                                    if side == 'buy':
                                                        tp_price_to_use = entry_price_to_use * (1 + tp_percent / 100)
                                                        sl_price_to_use = entry_price_to_use * (1 - sl_percent / 100)
                                                    else:  # side == 'sell'
                                                        tp_price_to_use = entry_price_to_use * (1 - tp_percent / 100)
                                                        sl_price_to_use = entry_price_to_use * (1 + sl_percent / 100)
                                                    
                                                    self.log_trading(
                                                        f"使用当前市场价格: {entry_price_to_use}\n"
                                                        f"调整后止盈价格: {tp_price_to_use} ({tp_percent:.2f}%)\n"
                                                        f"调整后止损价格: {sl_price_to_use} ({sl_percent:.2f}%)"
                                                    )
                                                
                                                # 下单前显示详细分析信息
                                                self.log_trading("🎯 交易决策详情:")
                                                self.log_trading(f"分析结果一致，准备{side}单")
                                                self.log_trading(f"入场价: {entry_price_to_use:.2f}")
                                                self.log_trading(f"止盈价: {tp_price_to_use:.2f}")
                                                self.log_trading(f"止损价: {sl_price_to_use:.2f}")
                                                self.log_trading(f"市场状态: {market_state}")
                                                self.log_trading(f"趋势强度: {trend_strength:.2f}")

                                                # 显示支持决策的关键信号
                                                if 'signals' in analysis_result:
                                                    key_signals = analysis_result['signals'][:5]  # 前5个关键信号
                                                    self.log_trading("支持决策的关键信号:")
                                                    for signal in key_signals:
                                                        self.log_trading(f"  • {signal}")

                                                # 显示新闻情感影响
                                                if 'sentiment_score' in analysis_result:
                                                    sentiment = analysis_result['sentiment_score']
                                                    sentiment_trend = "看多" if sentiment > 0.4 else "看空" if sentiment < -0.4 else "中性"
                                                    self.log_trading(f"新闻情感: {sentiment:.2f} ({sentiment_trend})")
                                                
                                                order = self.place_ai_order(
                                                    symbol=symbol,
                                                    side=side,
                                                    amount=quantity,
                                                    entry_price=entry_price_to_use,
                                                    tp_price=tp_price_to_use,
                                                    sl_price=sl_price_to_use
                                                )
                                                
                                                if order:
                                                    self.log_trading(f"下单成功: {order['id']}")
                                                    # 监控订单状态
                                                    self.monitor_order_status(order['id'], symbol)
                                                
                                            except Exception as e:
                                                self.log_trading(f"下单失败: {str(e)}")
                                        else:
                                            # 两次分析趋势不一致，但在强趋势市场中，可能仍然执行交易
                                            execute_trade = False
                                            
                                            # 在强趋势中，如果初始分析与趋势方向一致，可以考虑执行交易
                                            if (market_state == "STRONG_UPTREND" and trend == "看多") or \
                                               (market_state == "STRONG_DOWNTREND" and trend == "看空"):
                                                # 强趋势中的顺势交易，即使确认分析不一致，也可能执行
                                                trend_strength = analysis_result.get('trend_strength', 0)
                                                if abs(trend_strength) > 4:  # 趋势强度非常高
                                                    execute_trade = True
                                                    self.log_trading(
                                                        f"虽然两次分析趋势不一致，但在强{trend}趋势中且趋势强度高({trend_strength:.2f})，"
                                                        f"决定继续执行{trend}单"
                                                    )
                                            
                                            if not execute_trade:
                                                self.log_trading(
                                                    "两次分析趋势不一致，可能存在市场波动，"
                                                    f"初次分析为{trend}，确认分析为{confirm_trend}，"
                                                    "为避免风险取消本次交易"
                                                )
                                            else:
                                                # 执行交易逻辑（与上面相同）
                                                try:
                                                    # 确定交易方向
                                                    side = 'buy' if trend == '看多' else 'sell'
                                                    
                                                    # 根据用户选择确定入场价格
                                                    entry_price_to_use = None
                                                    tp_price_to_use = None
                                                    sl_price_to_use = None
                                                    
                                                    if self.use_ai_price_radio.isChecked():
                                                        # 使用AI分析的入场价格
                                                        entry_price_to_use = entry_price
                                                        tp_price_to_use = tp_price
                                                        sl_price_to_use = sl_price
                                                        
                                                        self.log_trading(f"使用AI分析入场价格: {entry_price_to_use} (原始AI分析价格)")
                                                    else:
                                                        # 使用当前市价作为入场价格
                                                        ticker = self.exchange.fetch_ticker(symbol)
                                                        current_market_price = ticker['last']
                                                        entry_price_to_use = current_market_price
                                                        
                                                        # 根据市场状态调整止盈止损比例
                                                        tp_percent = self.tp_percent
                                                        sl_percent = self.sl_percent

                                                        # 记录当前使用的止盈止损设置
                                                        self.log_trading(f"当前止盈止损设置: TP={tp_percent}%, SL={sl_percent}% (来自用户配置)")
                                                        
                                                        # 在强趋势中，可以调整止盈止损设置
                                                        if market_state == "STRONG_UPTREND" and side == 'buy':
                                                            tp_percent = tp_percent * 1.5
                                                            sl_percent = sl_percent * 0.8
                                                        elif market_state == "STRONG_DOWNTREND" and side == 'sell':
                                                            tp_percent = tp_percent * 1.5
                                                            sl_percent = sl_percent * 0.8
                                                        elif market_state == "RANGING_WEAK_TREND":
                                                            tp_percent = tp_percent * 0.8
                                                            sl_percent = sl_percent * 0.8
                                                        
                                                        # 重新计算止盈止损价格
                                                        if side == 'buy':
                                                            tp_price_to_use = entry_price_to_use * (1 + tp_percent / 100)
                                                            sl_price_to_use = entry_price_to_use * (1 - sl_percent / 100)
                                                        else:  # side == 'sell'
                                                            tp_price_to_use = entry_price_to_use * (1 - tp_percent / 100)
                                                            sl_price_to_use = entry_price_to_use * (1 + sl_percent / 100)
                                                    
                                                    # 下单
                                                    self.log_trading(
                                                        f"强趋势中执行{side}单:\n"
                                                        f"入场价: {entry_price_to_use:.2f}\n"
                                                        f"止盈价: {tp_price_to_use:.2f}\n"
                                                        f"止损价: {sl_price_to_use:.2f}"
                                                    )
                                                    
                                                    order = self.place_ai_order(
                                                        symbol=symbol,
                                                        side=side,
                                                        amount=quantity,
                                                        entry_price=entry_price_to_use,
                                                        tp_price=tp_price_to_use,
                                                        sl_price=sl_price_to_use
                                                    )
                                                    
                                                    if order:
                                                        self.log_trading(f"下单成功: {order['id']}")
                                                        # 监控订单状态
                                                        self.monitor_order_status(order['id'], symbol)
                                                    
                                                except Exception as e:
                                                    self.log_trading(f"下单失败: {str(e)}")
                        else:
                            self.log_trading("无法获取交易信号，取消本次交易")
                
                # 缩短检查间隔
                time.sleep(5)
                # 成功执行一次循环后重置连续错误计数
                consecutive_errors = 0
            
            except Exception as e:
                error_count += 1
                consecutive_errors += 1
                last_error_time = time.time()
                
                # 记录详细错误信息
                error_type = type(e).__name__
                error_message = str(e)
                self.log_trading(f"自动交易循环错误 ({error_count}/{max_errors}) [{error_type}]: {error_message}", level='error')
                
                # 根据错误类型采取不同的措施
                if "Rate limit" in error_message or "Too many requests" in error_message:
                    wait_time = 60  # API限制错误等待更长时间
                    self.log_trading(f"API限制错误，等待{wait_time}秒", level='warning')
                    time.sleep(wait_time)
                elif "Network" in error_message or "Timeout" in error_message or "Connection" in error_message:
                    self.log_trading("网络连接错误，检查网络状态", level='warning')
                    self.check_network_status()
                    time.sleep(15)
                
                if error_count >= max_errors:
                    self.log_trading("错误次数过多，自动停止交易", level='error')
                    self.auto_trading_enabled = False
                    self.show_message_signal.emit("错误", "自动交易因多次错误已停止，请检查网络和API状态。", "error")
                    break
                
                # 根据连续错误次数指数增加等待时间
                wait_time = min(10 * consecutive_errors, 60)  # 最多等待60秒
                self.log_trading(f"错误后等待{wait_time}秒后继续", level='info')
                time.sleep(wait_time)
            
            else:
                error_count = 0
            
            finally:
                gc.collect()
                # 添加内存监控与清理
                if gc.get_count()[0] > 1000:  # 如果待回收对象过多
                    gc.collect(2)  # 强制完全垃圾回收

    def get_trading_signal(self, symbol):
        """获取交易信号"""
        try:
            max_retries = 5
            retry_delay = 10
            timeout = 120  # 增加超时时间到120秒
            
            # 在函数开始就初始化可能在f-string中使用的变量，确保它们在所有代码路径中都有值
            market_state = "RANGING_WEAK_TREND"  # 默认为震荡或弱趋势
            adx_value = 0.0
            plus_di_value = 0.0
            minus_di_value = 0.0
            current_adx_threshold = 25
            trend_strength = 0.0
            trend_signals = []
            avg_news_sentiment = 0.0
            
            for attempt in range(max_retries):
                try:
                    # 修改获取市场数据部分，使用正确的交易对格式
                    swap_symbol = f"{symbol}USDT"  # 使用币安永续合约格式
                    ticker = self.exchange.fetch_ticker(swap_symbol)
                    current_price = ticker['last']
                    
                    # 获取K线数据也使用永续合约格式
                    ohlcv = self.exchange.fetch_ohlcv(swap_symbol, '15m', limit=100)
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    
                    # 添加价格精度处理
                    current_price = round(current_price, 2)
                    volatility = round(abs((current_price - self.last_price) / self.last_price * 100) if hasattr(self, 'last_price') else 0, 2)
                    self.last_price = current_price
                    
                    # 获取新闻数据，增加更多新闻源和关键词
                    news_sources = [
                        {
                            'url': 'https://newsapi.org/v2/everything',
                            'params': {
                                'q': f'(Trump OR Federal Reserve OR SEC OR regulation OR "crypto regulation" OR "CFTC" OR "Treasury Department" OR "central bank" OR "Yellen" OR "institutional adoption" OR "Blackrock" OR "JP Morgan" OR "Goldman Sachs" OR "digital currency") AND ({symbol} OR cryptocurrency OR "digital asset" OR blockchain)',
                                'sortBy': 'publishedAt',
                                'language': 'en',
                                'pageSize': 6,
                                'apiKey': self.news_api_key_input.text()
                            }
                        },
                        {
                            'url': 'https://newsapi.org/v2/everything',
                            'params': {
                                'q': f'(market analysis OR price prediction OR volatility OR "market sentiment" OR "trading volume" OR "bullish sentiment" OR "bearish outlook") AND ({symbol} OR cryptocurrency)',
                                'sortBy': 'publishedAt',
                                'language': 'en',
                                'pageSize': 6,
                                'apiKey': self.news_api_key_input.text()
                            }
                        }
                    ]
                    
                    all_articles = []
                    for source in news_sources:
                        try:
                            # 使用新的强大请求方法
                            news_response = self.make_robust_request(
                                url=source['url'],
                                params=source['params'],
                                timeout=(15, 45),  # 更长的超时时间
                                max_retries=3
                            )

                            if news_response.status_code == 200:
                                news_data = news_response.json()
                                articles = news_data.get('articles', [])
                                all_articles.extend(articles)
                                self.log_trading(f"自动交易模式: 成功获取 {len(articles)} 条新闻")
                            else:
                                error_msg = f"API返回错误: {news_response.status_code} - {news_response.text}"
                                self.log_trading(error_msg, level='warning')
                        except Exception as e:
                            self.log_trading(f"获取新闻源失败: {str(e)}", level='error')
                    
                    if not all_articles:
                        self.log_trading("未找到相关新闻")
                        return None
                    
                    # 新闻情感分析
                    news_sentiment = 0
                    news_summary = "最新加密货币相关新闻摘要：\n\n"
                    
                    for article in all_articles[:10]:  # 分析前10条新闻
                        title = article['title']
                        description = article.get('description', '')
                        content = f"{title} {description}"
                        
                        # 情感分析关键词
                        positive_keywords = ['bullish', 'surge', 'rise', 'gain', 'positive', 'growth', 'adoption', 'approval', 'institutional investment', 'breakthrough', 'rally', 'endorsement', 'partnership', 'launch', 'innovation']
                        negative_keywords = ['bearish', 'crash', 'drop', 'fall', 'negative', 'decline', 'ban', 'reject', 'regulation concerns', 'investigation', 'lawsuit', 'hack', 'security breach', 'restrictions', 'vulnerability', 'criticism']
                        
                        # 计算情感分数，引入关键词权重
                        sentiment_score = 0
                        
                        # 高权重关键词
                        high_impact_positive = ['institutional adoption', 'ETF approval', 'major partnership', 'regulatory clarity', 'mainstream adoption']
                        high_impact_negative = ['trading ban', 'major hack', 'SEC lawsuit', 'criminal charges', 'severe restriction']
                        
                        # 检查高权重关键词
                        for keyword in high_impact_positive:
                            if keyword.lower() in content.lower():
                                sentiment_score += 2  # 高权重积极关键词计2分
                        
                        for keyword in high_impact_negative:
                            if keyword.lower() in content.lower():
                                sentiment_score -= 2  # 高权重消极关键词计-2分
                        
                        # 检查普通关键词
                        for keyword in positive_keywords:
                            if keyword.lower() in content.lower():
                                sentiment_score += 1
                                
                        for keyword in negative_keywords:
                            if keyword.lower() in content.lower():
                                sentiment_score -= 1
                        
                        news_sentiment += sentiment_score
                        
                        # 添加到新闻摘要
                        news_summary += f"- {title}\n"
                        if description:
                            news_summary += f"  摘要: {description[:200]}...\n"
                        news_summary += f"  情感倾向: {'看多' if sentiment_score > 0 else '看空' if sentiment_score < 0 else '中性'}\n\n"
                    
                    # 计算整体新闻情感，增加权重影响
                    avg_news_sentiment = news_sentiment / len(all_articles[:10])
                    # 降低中性判断阈值，使系统更容易得出明确的多空信号
                    news_trend = "看多" if avg_news_sentiment > 0.4 else "看空" if avg_news_sentiment < -0.4 else "中性"
                    
                    # 初始化趋势强度和信号列表
                    trend_strength = 0
                    trend_signals = []
                    
                    # 将新闻情感纳入趋势强度计算，增加权重
                    if news_trend == "看多":
                        trend_strength += 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏多")
                    elif news_trend == "看空":
                        trend_strength -= 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏空")
                    
                    # 计算更多技术指标
                    close_prices = df['close'].values
                    high_prices = df['high'].values
                    low_prices = df['low'].values
                    volume = df['volume'].values
                    
                    # 基础指标 - 使用用户设置的参数
                    rsi = talib.RSI(close_prices, timeperiod=getattr(self, 'rsi_period', 14))
                    macd, signal, hist = talib.MACD(
                        close_prices, 
                        fastperiod=getattr(self, 'macd_fast', 12), 
                        slowperiod=getattr(self, 'macd_slow', 26), 
                        signalperiod=getattr(self, 'macd_signal', 9)
                    )
                    upper, middle, lower = talib.BBANDS(
                        close_prices, 
                        timeperiod=getattr(self, 'bb_period', 20),
                        nbdevup=getattr(self, 'bb_std', 2.0),
                        nbdevdn=getattr(self, 'bb_std', 2.0),
                        matype=0
                    )
                    
                    # 趋势指标 - 使用用户设置的参数
                    ema20 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_short', 20))
                    ema50 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_medium', 50))
                    ema200 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_long', 200))
                    adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14))
                    di_plus = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14))
                    di_minus = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14))
                    
                    # 动量指标
                    stoch_k, stoch_d = talib.STOCH(high_prices, low_prices, close_prices)
                    cci = talib.CCI(high_prices, low_prices, close_prices)
                    mfi = talib.MFI(high_prices, low_prices, close_prices, volume)
                    
                    # 波动指标
                    atr = talib.ATR(high_prices, low_prices, close_prices)
                    natr = talib.NATR(high_prices, low_prices, close_prices)
                    
                    # 成交量指标
                    obv = talib.OBV(close_prices, volume)
                    ad = talib.AD(high_prices, low_prices, close_prices, volume)
                    
                    # --- 市场状态判断 ---
                    market_state = "RANGING_WEAK_TREND" # 默认为震荡或弱趋势
                    adx_value = adx[-1]
                    plus_di_value = di_plus[-1]
                    minus_di_value = di_minus[-1]
                    current_adx_threshold = getattr(self, 'adx_threshold', 25)

                    if adx_value > current_adx_threshold:
                        if plus_di_value > minus_di_value:
                            market_state = "STRONG_UPTREND"
                        elif minus_di_value > plus_di_value:
                            market_state = "STRONG_DOWNTREND"
                        # else: ADX强但方向不明，仍可视为RANGING_WEAK_TREND或特定处理
                    
                    self.log_trading(f"市场状态判断: {market_state} (ADX: {adx_value:.2f}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f}, 阈值: {current_adx_threshold})")

                    # --- ADX策略信号生成 ---
                    adx_signal = None
                    if hasattr(self, 'adx_strategy') and self.adx_strategy and getattr(self, 'adx_strategy_enabled', False):
                        try:
                            # 更新ADX策略数据
                            self.adx_strategy.update_data(ohlcv_data)

                            # 获取ADX策略信号
                            adx_signals = self.adx_strategy.get_current_signals()
                            long_signal = adx_signals['long_signal']
                            short_signal = adx_signals['short_signal']

                            # 添加确认过滤器
                            filters = self.adx_strategy.add_confirmation_filter(ohlcv_data)

                            # 检查做多信号
                            if long_signal['signal'] == 'LONG' and validate_adx_signal(long_signal, filters):
                                # 检查信号冷却时间
                                current_time = datetime.now()
                                if (not self.adx_last_signal_time or
                                    (current_time - self.adx_last_signal_time).total_seconds() > self.adx_signal_cooldown):

                                    adx_signal = {
                                        'type': 'LONG',
                                        'strength': long_signal['strength'],
                                        'reason': long_signal['reason'],
                                        'adx': long_signal['adx'],
                                        'plus_di': long_signal['plus_di'],
                                        'minus_di': long_signal['minus_di']
                                    }
                                    self.adx_last_signal_time = current_time
                                    trend_strength += long_signal['strength'] * 3  # ADX信号权重较高
                                    trend_signals.append(f"ADX策略做多信号: {long_signal['reason']}")
                                    self.log_trading(f"ADX策略生成做多信号: {long_signal['reason']}", level='info')

                            # 检查做空信号
                            elif short_signal['signal'] == 'SHORT' and validate_adx_signal(short_signal, filters):
                                # 检查信号冷却时间
                                current_time = datetime.now()
                                if (not self.adx_last_signal_time or
                                    (current_time - self.adx_last_signal_time).total_seconds() > self.adx_signal_cooldown):

                                    adx_signal = {
                                        'type': 'SHORT',
                                        'strength': short_signal['strength'],
                                        'reason': short_signal['reason'],
                                        'adx': short_signal['adx'],
                                        'plus_di': short_signal['plus_di'],
                                        'minus_di': short_signal['minus_di']
                                    }
                                    self.adx_last_signal_time = current_time
                                    trend_strength -= short_signal['strength'] * 3  # ADX信号权重较高
                                    trend_signals.append(f"ADX策略做空信号: {short_signal['reason']}")
                                    self.log_trading(f"ADX策略生成做空信号: {short_signal['reason']}", level='info')

                        except Exception as e:
                            self.log_trading(f"ADX策略信号生成失败: {str(e)}", level='error')

                    # RSI信号 - 使用用户设置的阈值
                    rsi_value = rsi[-1]
                    rsi_overbought = getattr(self, 'rsi_overbought', 70)
                    rsi_oversold = getattr(self, 'rsi_oversold', 30)
                    if market_state == "STRONG_UPTREND":
                        if rsi_value > rsi_overbought: # 超买在强上涨趋势中可能意味着趋势持续
                            trend_strength += 0.5 # 轻微加分
                            trend_signals.append(f"RSI超买({rsi_value:.2f})，强上涨趋势中，可能持续")
                        elif rsi_value < rsi_oversold: # 超卖在强上涨趋势中是强烈的反向信号，需谨慎，暂时不减分或轻微减分
                            trend_strength -= 0.25
                            trend_signals.append(f"RSI超卖({rsi_value:.2f})，与强上涨趋势矛盾，警惕")
                    elif market_state == "STRONG_DOWNTREND":
                        if rsi_value < rsi_oversold: # 超卖在强下跌趋势中可能意味着趋势持续
                            trend_strength -= 0.5 # 轻微加分（负向）
                            trend_signals.append(f"RSI超卖({rsi_value:.2f})，强下跌趋势中，可能持续")
                        elif rsi_value > rsi_overbought: # 超买在强下跌趋势中是强烈的反向信号，需谨慎
                            trend_strength += 0.25
                            trend_signals.append(f"RSI超买({rsi_value:.2f})，与强下跌趋势矛盾，警惕")
                    else: # RANGING_WEAK_TREND
                        if rsi_value > rsi_overbought:
                            trend_strength -= 1
                            trend_signals.append(f"RSI超买 ({rsi_value:.2f} > {rsi_overbought})，震荡市看空信号")
                        elif rsi_value < rsi_oversold:
                            trend_strength += 1
                            trend_signals.append(f"RSI超卖 ({rsi_value:.2f} < {rsi_oversold})，震荡市看多信号")
                    
                    # MACD信号
                    macd_value = macd[-1]
                    signal_value = signal[-1]
                    prev_macd_value = macd[-2]
                    prev_signal_value = signal[-2]
                    if macd_value > signal_value and prev_macd_value <= prev_signal_value: # 金叉
                        if market_state == "STRONG_UPTREND":
                            trend_strength += 2 # 强趋势下，顺势信号权重增加
                            trend_signals.append("MACD金叉，强上涨趋势中，强烈看多")
                        elif market_state == "STRONG_DOWNTREND":
                            trend_strength += 0.25 # 逆势信号，小幅加分或忽略
                            trend_signals.append("MACD金叉，与强下跌趋势矛盾，谨慎")
                        else: # RANGING_WEAK_TREND
                            trend_strength += 1
                            trend_signals.append("MACD金叉，震荡市看多信号")
                    elif macd_value < signal_value and prev_macd_value >= prev_signal_value: # 死叉
                        if market_state == "STRONG_DOWNTREND":
                            trend_strength -= 2 # 强趋势下，顺势信号权重增加
                            trend_signals.append("MACD死叉，强下跌趋势中，强烈看空")
                        elif market_state == "STRONG_UPTREND":
                            trend_strength -= 0.25 # 逆势信号，小幅减分或忽略
                            trend_signals.append("MACD死叉，与强上涨趋势矛盾，谨慎")
                        else: # RANGING_WEAK_TREND
                            trend_strength -= 1
                            trend_signals.append("MACD死叉，震荡市看空信号")
                    
                    # 布林带信号
                    upper_band = upper[-1]
                    lower_band = lower[-1]
                    if market_state == "STRONG_UPTREND":
                        if current_price > upper_band: # 突破上轨，趋势持续
                            trend_strength += 1.5
                            trend_signals.append("价格突破布林上轨，强上涨趋势持续")
                        elif current_price < lower_band: # 跌破下轨，趋势反转信号，谨慎
                            trend_strength -= 0.5 
                            trend_signals.append("价格跌破布林下轨，与强上涨趋势矛盾，警惕")
                    elif market_state == "STRONG_DOWNTREND":
                        if current_price < lower_band: # 突破下轨，趋势持续
                            trend_strength -= 1.5
                            trend_signals.append("价格突破布林下轨，强下跌趋势持续")
                        elif current_price > upper_band: # 突破上轨，趋势反转信号，谨慎
                            trend_strength += 0.5
                            trend_signals.append("价格突破布林上轨，与强下跌趋势矛盾，警惕")
                    else: # RANGING_WEAK_TREND
                        if current_price > upper_band:
                            trend_strength -= 1
                            trend_signals.append("价格突破布林上轨，震荡市看空")
                        elif current_price < lower_band:
                            trend_strength += 1
                            trend_signals.append("价格突破布林下轨，震荡市看多")
                    
                    # EMA信号
                    ema20_value = ema20[-1]
                    ema50_value = ema50[-1]
                    prev_ema20_value = ema20[-2]
                    prev_ema50_value = ema50[-2]
                    if ema20_value > ema50_value and prev_ema20_value <= prev_ema50_value: # 短期上穿中期
                        if market_state == "STRONG_UPTREND":
                            trend_strength += 1.5
                            trend_signals.append("EMA短穿中(金叉)，强上涨趋势中，看多")
                        else:
                            trend_strength += 0.5 # 在其他市场状态下，权重较低
                            trend_signals.append("EMA短穿中(金叉)，看多信号")
                    elif ema20_value < ema50_value and prev_ema20_value >= prev_ema50_value: # 短期下穿中期
                        if market_state == "STRONG_DOWNTREND":
                            trend_strength -= 1.5
                            trend_signals.append("EMA短穿中(死叉)，强下跌趋势中，看空")
                        else:
                            trend_strength -= 0.5 # 在其他市场状态下，权重较低
                            trend_signals.append("EMA短穿中(死叉)，看空信号")
                    
                    # ADX趋势强度信号 (已在市场状态判断时使用，这里可以再次确认或细化)
                    # 在强趋势市场，ADX本身已经贡献了市场状态的判断，其对trend_strength的直接影响已在前面体现
                    # 此处可以根据ADX的绝对值大小，对强趋势的强度做进一步细化，例如ADX > 40 则更强
                    if market_state == "STRONG_UPTREND" and adx_value > getattr(self, 'adx_strong_threshold', 40):
                        trend_strength += 0.5 # 额外加分给极强趋势
                        trend_signals.append(f"ADX极强上涨 ({adx_value:.2f})")
                    elif market_state == "STRONG_DOWNTREND" and adx_value > getattr(self, 'adx_strong_threshold', 40):
                        trend_strength -= 0.5 # 额外减分给极强趋势
                        trend_signals.append(f"ADX极强下跌 ({adx_value:.2f})")
                    # 对于之前的ADX信号逻辑，现在已经整合到market_state的判断里了
                    # 原有的ADX对trend_strength的直接加减分逻辑可以移除或重构，避免重复计算影响
                    # 这里保留了原始的 `elif di_minus[-1] > di_plus[-1]` 等判断，因为它们在 `market_state` 中已经使用。
                    # 下面的代码块是基于旧的直接修改trend_strength，现在应该由market_state指导
                    # Ensure this part is reviewed and potentially removed or refactored:
                    # current_adx_threshold = getattr(self, 'adx_threshold', 25)
                    # if adx_value > current_adx_threshold:
                    #     if plus_di_value > minus_di_value:
                    #         trend_strength += 1.5 
                    #         trend_signals.append(f"ADX显示强势上涨趋势 (ADX: {adx_value:.2f} > {current_adx_threshold}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f})")
                    #     elif minus_di_value > plus_di_value:
                    #         trend_strength -= 1.5 
                    #         trend_signals.append(f"ADX显示强势下跌趋势 (ADX: {adx_value:.2f} > {current_adx_threshold}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f})")
                    #     else:
                    #         trend_signals.append(f"ADX趋势强但方向不明 (ADX: {adx_value:.2f} > {current_adx_threshold}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f})")
                    # else:
                    #     trend_signals.append(f"ADX显示无明显趋势 (ADX: {adx_value:.2f} <= {current_adx_threshold})")
                    
                    # 随机指标 (Stochastic) 信号 - 在震荡市中权重较高
                    stoch_k_value = stoch_k[-1]
                    stoch_d_value = stoch_d[-1]
                    stoch_overbought = getattr(self, 'stoch_overbought', 80)
                    stoch_oversold = getattr(self, 'stoch_oversold', 20)
                    if market_state == "RANGING_WEAK_TREND":
                        if stoch_k_value > stoch_overbought and stoch_k_value < stoch_k[-2]: # K线从超买区下穿D线或K线自身拐头向下
                            trend_strength -= 0.75
                            trend_signals.append(f"随机指标超买且下降 ({stoch_k_value:.2f})，震荡市看空")
                        elif stoch_k_value < stoch_oversold and stoch_k_value > stoch_k[-2]: # K线从超卖区上穿D线或K线自身拐头向上
                            trend_strength += 0.75
                            trend_signals.append(f"随机指标超卖且上升 ({stoch_k_value:.2f})，震荡市看多")
                    else: # 强趋势市场，随机指标信号降权或忽略
                        if stoch_k_value > stoch_overbought:
                            trend_signals.append(f"随机指标超买({stoch_k_value:.2f})，强趋势市忽略")
                        elif stoch_k_value < stoch_oversold:
                            trend_signals.append(f"随机指标超卖({stoch_k_value:.2f})，强趋势市忽略")
                    
                    # CCI信号 - 在震荡市中权重较高
                    cci_value = cci[-1]
                    cci_overbought = getattr(self, 'cci_overbought', 100)
                    cci_oversold = getattr(self, 'cci_oversold', -100)
                    if market_state == "RANGING_WEAK_TREND":
                        if cci_value > cci_overbought:
                            trend_strength -= 0.75
                            trend_signals.append(f"CCI超买 ({cci_value:.2f})，震荡市看空")
                        elif cci_value < cci_oversold:
                            trend_strength += 0.75
                            trend_signals.append(f"CCI超卖 ({cci_value:.2f})，震荡市看多")
                    else: # 强趋势市场，CCI信号降权或忽略
                         if cci_value > cci_overbought:
                            trend_signals.append(f"CCI超买({cci_value:.2f})，强趋势市忽略")
                         elif cci_value < cci_oversold:
                            trend_signals.append(f"CCI超卖({cci_value:.2f})，强趋势市忽略")

                    # MFI信号 - 在震荡市中权重较高
                    mfi_value = mfi[-1]
                    mfi_overbought = getattr(self, 'mfi_overbought', 80)
                    mfi_oversold = getattr(self, 'mfi_oversold', 20)
                    if market_state == "RANGING_WEAK_TREND":
                        if mfi_value > mfi_overbought:
                            trend_strength -= 0.75
                            trend_signals.append(f"MFI超买 ({mfi_value:.2f})，震荡市看空")
                        elif mfi_value < mfi_oversold:
                            trend_strength += 0.75
                            trend_signals.append(f"MFI超卖 ({mfi_value:.2f})，震荡市看多")
                    else: # 强趋势市场，MFI信号降权或忽略
                        if mfi_value > mfi_overbought:
                            trend_signals.append(f"MFI超买({mfi_value:.2f})，强趋势市忽略")
                        elif mfi_value < mfi_oversold:
                            trend_signals.append(f"MFI超卖({mfi_value:.2f})，强趋势市忽略")

                    # 成交量趋势 (OBV) - 可作为辅助，强趋势下顺势成交量放大是确认
                    if obv[-1] > obv[-2]: # 成交量放大
                        if market_state == "STRONG_UPTREND":
                            trend_strength += 0.5
                            trend_signals.append("OBV成交量放大，配合强上涨趋势")
                        elif market_state == "STRONG_DOWNTREND":
                            # 下跌时成交量放大，可能加速下跌，也可能恐慌抛售接近尾声，这里稍微复杂
                            # 暂定为中性或轻微负面，因为通常下跌放量被视为空头力量强
                            trend_strength -= 0.25 
                            trend_signals.append("OBV成交量放大，配合强下跌趋势")
                        else: # 震荡市成交量变化意义不大
                            trend_signals.append("OBV成交量变化，震荡市中性")
                    elif obv[-1] < obv[-2]: # 成交量萎缩
                        if market_state == "STRONG_UPTREND":
                            trend_strength -= 0.25 # 上涨缩量，趋势可能减弱
                            trend_signals.append("OBV成交量萎缩，强上涨趋势中警惕")
                        elif market_state == "STRONG_DOWNTREND":
                            trend_strength += 0.25 # 下跌缩量，趋势可能减弱
                            trend_signals.append("OBV成交量萎缩，强下跌趋势中警惕")
                        else:
                            trend_signals.append("OBV成交量变化，震荡市中性")
                    
                    # 记录情感分析结果
                    self.log_trading(f"新闻情感分析：整体得分 {avg_news_sentiment:.2f}，趋势判断 {news_trend}", level='info')
                    self.log_trading(f"分析了 {len(all_articles[:10])} 条新闻，包含监管动态和机构参与相关内容", level='info')
                    
                    # 将新闻情感纳入趋势强度计算
                    if news_trend == "看多":
                        trend_strength += 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏多")
                    elif news_trend == "看空":
                        trend_strength -= 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏空")
                    
                    # 计算最终趋势强度
                    final_trend_strength = float(trend_strength)  # 确保是浮点数
                    
                    # 准备AI分析提示
                    prompt = f"""作为一个专业的加密货币分析师，请基于以下信息进行详细分析，并严格按照指定格式输出结果。
请特别注意：
1. 趋势判断必须明确且及时，不要犹豫
2. 价格设置必须严格符合要求，不得有任何偏差
3. 重点关注特朗普和美联储新闻对加密货币的影响

当前市场信息：
- 货币对：{symbol}USDT
- 当前价格：{current_price}
- **判定市场状态**：{market_state} (基于ADX({getattr(self, 'adx_period', 14)})={adx_value:.2f}, +DI={plus_di_value:.2f}, -DI={minus_di_value:.2f}, 阈值={current_adx_threshold})
- RSI({getattr(self, 'rsi_period', 14)}): {rsi[-1]:.2f} {'(超买)' if rsi[-1] > getattr(self, 'rsi_overbought', 70) else '(超卖)' if rsi[-1] < getattr(self, 'rsi_oversold', 30) else ''}
- MACD({getattr(self, 'macd_fast', 12)},{getattr(self, 'macd_slow', 26)},{getattr(self, 'macd_signal', 9)}): {macd[-1]:.2f} {'(金叉)' if macd[-1] > signal[-1] else '(死叉)'}
- 布林带({getattr(self, 'bb_period', 20)},{getattr(self, 'bb_std', 2.0)}): 上轨 {upper[-1]:.2f}, 中轨 {middle[-1]:.2f}, 下轨 {lower[-1]:.2f}
  {'(突破上轨)' if current_price > upper[-1] else '(突破下轨)' if current_price < lower[-1] else '(区间内)'}
- EMA: {getattr(self, 'ema_short', 20)}日 {ema20[-1]:.2f}, {getattr(self, 'ema_medium', 50)}日 {ema50[-1]:.2f}
  {'(黄金交叉)' if ema20[-1] > ema50[-1] and ema20[-2] <= ema50[-2] else '(死亡交叉)' if ema20[-1] < ema50[-1] and ema20[-2] >= ema50[-2] else ''}
- 随机指标: K({stoch_k[-1]:.2f}) D({stoch_d[-1]:.2f}) {'(超买区)' if stoch_k[-1] > getattr(self, 'stoch_overbought', 80) else '(超卖区)' if stoch_k[-1] < getattr(self, 'stoch_oversold', 20) else ''}
- ADX({getattr(self, 'adx_period', 14)}): {adx[-1]:.2f} (+DI: {di_plus[-1]:.2f}, -DI: {di_minus[-1]:.2f}) {'(趋势强)' if adx[-1] > getattr(self, 'adx_threshold', 25) else '(趋势弱)'}
- CCI({getattr(self, 'cci_period', 20)}): {cci[-1]:.2f} {'(超买)' if cci[-1] > getattr(self, 'cci_overbought', 100) else '(超卖)' if cci[-1] < getattr(self, 'cci_oversold', -100) else ''}
- MFI({getattr(self, 'mfi_period', 14)}): {mfi[-1]:.2f} {'(超买)' if mfi[-1] > getattr(self, 'mfi_overbought', 80) else '(超卖区)' if mfi[-1] < getattr(self, 'mfi_oversold', 20) else ''}

趋势判断参考：
1. 技术面分析：
   **请结合当前市场状态 ({market_state}) 解读以下指标：**
   - **强趋势 ({str(market_state == "STRONG_UPTREND" or market_state == "STRONG_DOWNTREND")})**：
     - 优先关注与趋势同向的信号（如MACD顺势交叉，价格沿布林带轨道运行）。
     - 振荡指标（RSI, Stochastic, CCI, MFI）的超买/超卖可能指示趋势持续，而非立即反转，需谨慎对待其反转信号。
   - **震荡/弱趋势 ({str(market_state == "RANGING_WEAK_TREND")})**：
     - 振荡指标的超买/超卖信号权重增加，可用于寻找潜在的反转点。
     - MACD和布林带的信号需警惕假突破。

   - RSI > {getattr(self, 'rsi_overbought', 70)} 且价格突破布林上轨，倾向看空 (震荡市更可靠)
   - RSI < {getattr(self, 'rsi_oversold', 30)} 且价格突破布林下轨，倾向看多 (震荡市更可靠)
   - MACD金叉且RSI回升，倾向看多
   - MACD死叉且RSI下降，倾向看空
   - EMA{getattr(self, 'ema_short', 20)}上穿EMA{getattr(self, 'ema_medium', 50)}形成黄金交叉，倾向看多
   - EMA{getattr(self, 'ema_short', 20)}下穿EMA{getattr(self, 'ema_medium', 50)}形成死亡交叉，倾向看空
   - ADX > {getattr(self, 'adx_threshold', 25)}表示趋势强，结合其他指标判断方向
   - 随机指标K值和D值在超买区（>{getattr(self, 'stoch_overbought', 80)}）且开始下降，倾向看空
   - 随机指标K值和D值在超卖区（<{getattr(self, 'stoch_oversold', 20)}）且开始上升，倾向看多

2. 新闻面分析：
   - 特朗普相关新闻：
     * 关注其对加密货币的态度和言论
     * 其政策主张对加密货币市场的潜在影响
     * 是否涉及对加密货币的监管态度
   
   - 美联储相关新闻：
     * 利率政策变化及预期
     * 通胀数据及应对措施
     * 货币政策声明对市场的影响
     * 美元走势对加密货币的影响

   - 监管动态：
     * SEC、CFTC等监管机构的政策发布与声明
     * 全球主要国家加密货币监管法规变化
     * 对交易所的监管措施与合规要求
     * 反洗钱和KYC政策的实施情况
   
   - 机构参与度：
     * 大型机构（如BlackRock、摩根大通、高盛等）投资加密货币的动向
     * ETF批准或拒绝的最新进展
     * 传统金融机构进入加密领域的战略
     * 机构持仓量的变化趋势

{news_summary}

请严格按照以下规则设置价格：

1. 如果建议做多：
   - 入场价必须接近但略低于当前价格（差距不超过0.5%）
   - 止盈价必须设置在入场价的0.5-1%上方（包含边界值）
   - 止损价必须设置在入场价的3-5%下方（包含3%和5%的边界值）

2. 如果建议做空：
   - 入场价必须接近但略高于当前价格（差距不超过0.5%）
   - 止盈价必须设置在入场价的0.5-1%下方（包含边界值）
   - 止损价必须设置在入场价的3-5%上方（包含3%和5%的边界值）

请按照以下格式输出：

市场趋势：[看多/看空]
入场价位：[具体数字] USDT
止盈价位：[具体数字] USDT
止损价位：[具体数字] USDT

详细分析：
1. 技术面分析：
[详细分析内容，包括RSI、MACD、布林带、EMA、随机指标和ADX的具体信号]

2. 新闻影响分析：
[详细分析内容，重点关注特朗普和美联储政策的影响]

3. 价格设置依据：
[解释入场价、止盈价和止损价的设置原因，必须包含具体的百分比计算]

4. 潜在风险提示：
[详细说明可能影响交易的风险因素]

注意：
1. 趋势判断必须明确，不要模棱两可
2. 所有价格必须是具体的数字，且严格符合上述百分比范围要求
3. 价格必须基于当前市场价格进行合理设置
4. 分析要客观专业，并提供具体理由
5. 严格按照上述格式输出，不要改变格式
"""

                    # 调用DeepSeek AI进行分析
                    headers = {
                        'Authorization': f'Bearer {self.ai_api_key_input.text()}',
                        'Content-Type': 'application/json'
                    }
                    
                    api_url = "https://api.deepseek.com/v1/chat/completions"
                    api_data = {
                        "model": "deepseek-chat",
                        "messages": [{"role": "user", "content": prompt}],
                        "temperature": 0.7,
                        "timeout": timeout
                    }
                    
                    # 使用session进行请求，增加重试机制
                    session = requests.Session()
                    retries = Retry(
                        total=max_retries,
                        backoff_factor=1,
                        status_forcelist=[500, 502, 503, 504]
                    )
                    session.mount('https://', HTTPAdapter(max_retries=retries))
                    
                    response = session.post(
                        api_url, 
                        json=api_data, 
                        headers=headers, 
                        timeout=(30, timeout)  # (连接超时, 读取超时)
                    )
                    
                    if response.status_code != 200:
                        raise Exception(f"AI分析失败: {response.text}")
                    
                    analysis = response.json()['choices'][0]['message']['content']
                    
                    # 解析分析结果
                    trend_match = re.search(r"市场趋势：\s*([看多|看空]+)", analysis)
                    entry_match = re.search(r"入场价位：\s*(\d+\.?\d*)\s*USDT", analysis)
                    tp_match = re.search(r"止盈价位：\s*(\d+\.?\d*)\s*USDT", analysis)
                    sl_match = re.search(r"止损价位：\s*(\d+\.?\d*)\s*USDT", analysis)
                    
                    if all([trend_match, entry_match, tp_match, sl_match]):
                        result = {
                            'trend': trend_match.group(1),
                            'entry_price': entry_match.group(1),
                            'tp_price': tp_match.group(1),
                            'sl_price': sl_match.group(1),
                            'analysis': analysis,
                            'market_state': market_state,  # 添加市场状态
                            'trend_strength': trend_strength,  # 添加趋势强度
                            'signals': trend_signals,  # 添加趋势信号列表
                            'sentiment_score': avg_news_sentiment,  # 添加新闻情感分数
                            'ai_recommendation': trend_match.group(1) if trend_match else None,  # AI建议的多空方向
                            'adx_signal': adx_signal  # 添加ADX策略信号
                        }
                        
                        # 详细显示分析结果
                        self.log_trading(f"分析结果: {result['trend']}, 市场状态: {market_state}, 趋势强度: {trend_strength:.2f}")

                        # 显示新闻分析详情
                        self.log_trading("=" * 50)
                        self.log_trading("📰 新闻分析详情:")
                        self.log_trading(f"新闻情感得分: {avg_news_sentiment:.2f} ({news_trend})")

                        # 显示前5条重要新闻
                        if len(all_articles) > 0:
                            self.log_trading("重要新闻摘要:")
                            for i, article in enumerate(all_articles[:5], 1):
                                title = article['title']
                                description = article.get('description', '')
                                self.log_trading(f"{i}. {title}")
                                if description:
                                    self.log_trading(f"   {description[:150]}...")

                        # 显示技术指标详情
                        self.log_trading("📊 技术指标详情:")
                        self.log_trading(f"RSI({getattr(self, 'rsi_period', 14)}): {rsi[-1]:.2f} {'(超买)' if rsi[-1] > getattr(self, 'rsi_overbought', 70) else '(超卖)' if rsi[-1] < getattr(self, 'rsi_oversold', 30) else '(正常)'}")
                        self.log_trading(f"MACD: {macd[-1]:.2f} {'(金叉)' if macd[-1] > signal[-1] else '(死叉)'}")
                        self.log_trading(f"布林带: 上轨{upper[-1]:.2f}, 中轨{middle[-1]:.2f}, 下轨{lower[-1]:.2f}")
                        self.log_trading(f"EMA: 短期{ema20[-1]:.2f}, 中期{ema50[-1]:.2f}")
                        self.log_trading(f"ADX: {adx[-1]:.2f} (+DI:{di_plus[-1]:.2f}, -DI:{di_minus[-1]:.2f})")

                        # 显示关键信号
                        if trend_signals:
                            self.log_trading("🎯 关键技术信号:")
                            for signal in trend_signals[:8]:  # 显示前8个重要信号
                                self.log_trading(f"  • {signal}")

                        # 显示AI完整分析
                        self.log_trading("🤖 AI完整分析:")
                        self.log_trading(analysis)
                        self.log_trading("=" * 50)

                        return result
                    else:
                        self.log_trading("无法从AI分析结果中提取完整信息")
                        return None
                    
                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        backoff_time = retry_delay * (2 ** attempt)  # 指数退避
                        self.log_trading(f"API请求超时，{backoff_time}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(backoff_time)
                        continue
                    else:
                        self.log_trading("API请求超时，已达到最大重试次数")
                        return None
                    
                except requests.exceptions.RequestException as e:
                    if attempt < max_retries - 1:
                        self.log_trading(f"网络请求异常: {str(e)}，将在{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.log_trading(f"网络请求失败，已达到最大重试次数: {str(e)}")
                        return None
                    
                except Exception as e:
                    self.log_trading(f"获取交易信号失败: {str(e)}")
                    return None
                
            return None

        except Exception as e:
            self.log_trading(f"获取交易信号失败: {str(e)}")
            return None

    def place_ai_order(self, symbol, side, amount, entry_price, tp_price, sl_price):
        """下AI永续合约订单 - 优化止盈止损逻辑"""
        try:
            # 确保所有价格都是有效的数字
            if not all(isinstance(x, (int, float)) for x in [entry_price, tp_price, sl_price]):
                raise ValueError("价格必须是有效的数字")
            
            # 验证价格的合理性
            if entry_price <= 0 or tp_price <= 0 or sl_price <= 0:
                raise ValueError("价格必须大于0")

            # 决定使用的入场价格
            if self.use_ai_price_radio.isChecked():
                # 使用AI分析的入场价格
                entry_price_to_use = entry_price
                self.log_trading(f"使用AI分析入场价格: {entry_price_to_use}")
            else:
                # 使用当前市价作为入场价格
                ticker = self.exchange.fetch_ticker(symbol)
                entry_price_to_use = ticker['last']
                self.log_trading(f"使用当前市价: {entry_price_to_use}")
            
            # 新增选项：是否使用AI建议的止盈止损价格
            use_ai_tp_sl = False  # 这应该是UI中的一个选项，例如复选框
            
            # 统一止盈止损价格计算逻辑
            if use_ai_tp_sl and self.use_ai_price_radio.isChecked():
                # 使用AI建议的止盈止损价格
                tp_price_to_use = tp_price
                sl_price_to_use = sl_price
                self.log_trading("使用AI建议的止盈止损价格")
            else:
                # 检查是否使用ADX策略的风险管理
                if (hasattr(self, 'adx_strategy') and self.adx_strategy and
                    getattr(self, 'adx_strategy_enabled', False)):
                    try:
                        # 使用ADX策略计算止盈止损
                        signal_type = 'LONG' if side == 'buy' else 'SHORT'

                        # 计算止损价格
                        sl_price_to_use = self.adx_strategy.calculate_stop_loss(
                            entry_price_to_use, signal_type
                        )

                        # 计算止盈价格
                        tp_price_to_use = self.adx_strategy.calculate_take_profit(
                            entry_price_to_use, sl_price_to_use, signal_type
                        )

                        self.log_trading(
                            f"使用ADX策略风险管理:\n"
                            f"止盈价格: {tp_price_to_use:.2f}\n"
                            f"止损价格: {sl_price_to_use:.2f}"
                        )

                    except Exception as e:
                        self.log_trading(f"ADX策略风险管理失败，使用默认设置: {str(e)}", level='warning')
                        # 回退到用户设置的止盈止损百分比
                        if side == 'buy':
                            tp_price_to_use = entry_price_to_use * (1 + self.tp_percent / 100)
                            sl_price_to_use = entry_price_to_use * (1 - self.sl_percent / 100)
                        else:  # side == 'sell'
                            tp_price_to_use = entry_price_to_use * (1 - self.tp_percent / 100)
                            sl_price_to_use = entry_price_to_use * (1 + self.sl_percent / 100)
                else:
                    # 使用用户设置的止盈止损百分比
                    if side == 'buy':
                        tp_price_to_use = entry_price_to_use * (1 + self.tp_percent / 100)
                        sl_price_to_use = entry_price_to_use * (1 - self.sl_percent / 100)
                    else:  # side == 'sell'
                        tp_price_to_use = entry_price_to_use * (1 - self.tp_percent / 100)
                        sl_price_to_use = entry_price_to_use * (1 + self.sl_percent / 100)

                    self.log_trading(
                        f"使用自定义止盈止损设置:\n"
                        f"止盈比例: {self.tp_percent}% (价格: {tp_price_to_use:.2f})\n"
                        f"止损比例: {self.sl_percent}% (价格: {sl_price_to_use:.2f})"
                    )
            
            # 构造永续合约交易对
            base_quote = symbol.split('/')
            base, quote = base_quote[0], base_quote[1]
            swap_symbol = f"{base}{quote}"

            # 获取当前杠杆倍数和设置杠杆
            leverage = self.ai_leverage_spinbox.value()
            pos_side = 'long' if side == 'buy' else 'short'
            
            try:
                # 币安设置杠杆需要指定市场类型为合约，并添加额外参数
                self.exchange.set_leverage(leverage, swap_symbol, params={
                    'marginMode': 'isolated', 
                    'positionSide': 'BOTH',
                    'type': 'future',         # 指定为期货/合约交易
                    'future': True            # 明确指定为合约交易
                })
                self.log_trading(f"已设置杠杆倍数: {leverage}x")
            except Exception as e:
                self.log_trading(f"设置杠杆倍数失败: {str(e)}")
                # 尝试继续下单，不因杠杆设置失败而终止整个流程
                self.log_trading("将尝试使用默认杠杆继续下单")

            # 获取市场信息
            market = self.exchange.market(swap_symbol)
            min_amount = 0.001  # 币安永续合约最小交易量

            # 使用ADX策略计算仓位大小（如果启用）
            if (hasattr(self, 'adx_strategy') and self.adx_strategy and
                getattr(self, 'adx_strategy_enabled', False)):
                try:
                    # 获取账户余额
                    balance_info = self.exchange.fetch_balance()
                    account_balance = balance_info['USDT']['free']

                    # 使用ADX策略计算建议仓位大小
                    suggested_amount = self.adx_strategy.calculate_position_size(
                        account_balance, entry_price_to_use, sl_price_to_use
                    )

                    # 应用杠杆
                    suggested_amount = suggested_amount * leverage

                    # 使用建议仓位大小（但不超过原始amount）
                    amount = min(amount, suggested_amount) if suggested_amount > 0 else amount

                    self.log_trading(
                        f"ADX策略仓位管理:\n"
                        f"账户余额: {account_balance:.2f} USDT\n"
                        f"建议仓位: {suggested_amount:.6f}\n"
                        f"最终仓位: {amount:.6f}"
                    )

                except Exception as e:
                    self.log_trading(f"ADX策略仓位计算失败，使用默认仓位: {str(e)}", level='warning')

            # 格式化数量
            quantity = max(min_amount, round(amount / min_amount) * min_amount)

            # 生成订单ID
            order_id = f"AI{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # 根据用户选择决定订单类型
            order_type = 'market' if self.market_order_radio.isChecked() else 'limit'
            
            # 设置基本订单参数
            params = {
                "symbol": swap_symbol,
                "marginMode": "isolated",  # 逐仓模式
                "positionSide": "LONG" if side == "buy" else "SHORT",  # 明确指定持仓方向
                "leverage": leverage,
                "newClientOrderId": order_id
            }
            
            # 根据订单类型设置不同参数
            if order_type == 'market':
                params["type"] = "MARKET"
            else:  # 限价单
                params["type"] = "LIMIT"
                params["timeInForce"] = "GTC"
                
            # 设置止盈止损参数，处理价格精度问题
            # 获取交易对价格精度
            price_precision = 1  # 默认精度
            if 'precision' in market and 'price' in market['precision']:
                price_precision = market['precision']['price']
            
            # 格式化价格，确保符合币安API精度要求
            tp_price_str = self.exchange.price_to_precision(swap_symbol, tp_price_to_use)
            sl_price_str = self.exchange.price_to_precision(swap_symbol, sl_price_to_use)
            
            # 在下单时不设置止盈止损，避免参数问题
            self.log_trading("将不在下单时设置止盈止损，避免参数问题")
            
            # 确保不包含reduceOnly参数
            if "reduceOnly" in params:
                del params["reduceOnly"]

            # 记录下单信息
            self.log_trading(f"下单详情:\n"
                            f"合约: {params['symbol']}\n"
                            f"方向: {side.upper()}\n"
                            f"数量: {quantity}\n"
                            f"类型: {('市价单' if order_type == 'market' else '限价单')}\n"
                            f"{'入场价: ' + str(entry_price_to_use) if order_type == 'limit' else ''}\n"
                            f"止盈价: {tp_price_to_use:.1f}\n"
                            f"止损价: {sl_price_to_use:.1f}\n"
                            f"杠杆倍数: {params['leverage']}x\n"
                            f"保证金模式: {params['marginMode']}")

            # 下单
            order = self.exchange.create_order(
                symbol=swap_symbol,
                type=order_type,
                side=side,
                amount=float(quantity),
                price=entry_price if order_type == 'limit' else None,
                params=params
            )

            # 记录订单详情
            log_msg = (
                f"永续合约{('市价单' if order_type == 'market' else '限价单')}已提交:\n"
                f"合约: {params['symbol']}\n"
                f"方向: {side.upper()}\n"
                f"数量: {quantity}\n"
                f"杠杆: {params['leverage']}x\n"
                f"入场价: {('市价' if order_type == 'market' else entry_price_to_use)}\n"
                f"止盈价: {tp_price_to_use:.2f} ({(abs(tp_price_to_use - entry_price_to_use) / entry_price_to_use * 100):.2f}%)\n"
                f"止损价: {sl_price_to_use:.2f} ({(abs(sl_price_to_use - entry_price_to_use) / entry_price_to_use * 100):.2f}%)\n"
                f"{'使用AI分析价格' if self.use_ai_price_radio.isChecked() else '使用当前市价'}\n"
                f"{'使用AI建议止盈止损' if (use_ai_tp_sl and self.use_ai_price_radio.isChecked()) else '使用设置的止盈止损百分比'}"
            )
            self.log_trading(log_msg)
            
            # 下单成功后，单独设置止盈止损
            try:
                # 等待一下确保订单已经处理
                time.sleep(1)
                
                # 检查订单是否成功
                if order and 'id' in order:
                    self.log_trading("订单已提交，正在设置止盈止损...")
                    
                    # 这里应该使用Binance的止盈止损API
                    # 注意：这里是一个简化的例子，实际API调用可能需要调整
                    try:
                        # 先查询当前持仓，传入数组作为参数
                        positions = self.exchange.fetch_positions_risk([swap_symbol])
                        
                        self.log_trading(f"获取到 {len(positions)} 个持仓信息")
                        
                        # 记录持仓信息以便调试
                        for idx, pos in enumerate(positions):
                            pos_side = pos.get('side', '未知')
                            pos_size = pos.get('contracts', 0)
                            pos_notional = pos.get('notional', 0)
                            self.log_trading(f"持仓 {idx+1}: 方向={pos_side}, 大小={pos_size}, 名义价值={pos_notional}")
                        
                        if positions and len(positions) > 0:
                            position = None
                            # 查找对应方向的持仓，增强匹配逻辑
                            for pos in positions:
                                # 检查多种可能的字段
                                pos_side = pos.get('side', '')
                                pos_type = pos.get('type', '')
                                pos_direction = pos.get('direction', '')
                                pos_info = pos.get('info', {})
                                pos_position_side = pos_info.get('positionSide', '')
                                
                                # 多种匹配条件
                                side_matches = False
                                if side == 'buy':
                                    side_matches = (pos_side == 'long' or 
                                                  pos_type == 'long' or 
                                                  pos_direction == 'long' or
                                                  pos_position_side == 'LONG')
                                else:
                                    side_matches = (pos_side == 'short' or 
                                                  pos_type == 'short' or 
                                                  pos_direction == 'short' or
                                                  pos_position_side == 'SHORT')
                                
                                # 检查持仓大小，确保持仓有效
                                size_valid = (pos.get('contracts', 0) > 0 or 
                                           pos.get('notional', 0) != 0)
                                
                                if side_matches and size_valid:
                                    position = pos
                                    self.log_trading(f"找到匹配的持仓: {pos_side}方向")
                                    break
                            
                            if position:
                                self.log_trading(f"找到对应持仓，正在设置止盈止损...")
                                
                                # 获取实际持仓数量，确保与订单数量匹配
                                pos_amount = position.get('contracts', quantity)
                                self.log_trading(f"持仓数量: {pos_amount}, 订单数量: {quantity}")
                                
                                # 使用实际的设置止盈止损API
                                # 币安合约止盈止损参数
                                tpsl_params = {
                                    "symbol": swap_symbol,
                                    "positionSide": "LONG" if side == "buy" else "SHORT",
                                    "stopPrice": tp_price_str,
                                    "type": "TAKE_PROFIT_MARKET",
                                    "closePosition": True,
                                    "workingType": "MARK_PRICE",
                                    # "reduceOnly": True,  # 移除此参数，币安API不需要
                                    "timeInForce": "GTC"
                                }
                                
                                # 设置止盈
                                try:
                                    # 确定止盈单方向（与开仓方向相反）
                                    tp_side = "sell" if side == "buy" else "buy"
                                    
                                    tp_order = self.exchange.create_order(
                                        symbol=swap_symbol,
                                        type="TAKE_PROFIT_MARKET",
                                        side=tp_side,
                                        amount=float(pos_amount),  # 使用实际持仓数量
                                        params=tpsl_params
                                    )
                                    self.log_trading(f"止盈单已设置，触发价: {tp_price_str}")
                                except Exception as e:
                                    self.log_trading(f"设置止盈单失败: {str(e)}", level='error')
                                    self.log_trading(f"止盈单参数: 类型=TAKE_PROFIT_MARKET, 方向={tp_side}, 数量={pos_amount}")
                                
                                # 设置止损
                                try:
                                    sl_params = tpsl_params.copy()
                                    sl_params["stopPrice"] = sl_price_str
                                    sl_params["type"] = "STOP_MARKET"
                                    
                                    # 确定止损单方向（与开仓方向相反）
                                    sl_side = "sell" if side == "buy" else "buy"
                                    
                                    sl_order = self.exchange.create_order(
                                        symbol=swap_symbol,
                                        type="STOP_MARKET",
                                        side=sl_side,
                                        amount=float(pos_amount),  # 使用实际持仓数量
                                        params=sl_params
                                    )
                                    self.log_trading(f"止损单已设置，触发价: {sl_price_str}")
                                except Exception as e:
                                    self.log_trading(f"设置止损单失败: {str(e)}", level='error')
                                    self.log_trading(f"止损单参数: 类型=STOP_MARKET, 方向={sl_side}, 数量={pos_amount}")
                            else:
                                self.log_trading("未找到对应方向的持仓，尝试备选方法...", level='warning')
                                
                                # 备选方法：使用fetch_positions获取所有持仓
                                try:
                                    all_positions = self.exchange.fetch_positions()
                                    
                                    self.log_trading(f"备选方法获取到 {len(all_positions)} 个持仓")
                                    
                                    # 记录所有持仓信息
                                    for idx, pos in enumerate(all_positions):
                                        self.log_trading(f"备选持仓 {idx+1}: {pos.get('symbol', 'unknown')}, "
                                                      f"方向={pos.get('side', '未知')}, "
                                                      f"大小={pos.get('contracts', 0)}")
                                    
                                    # 按符号和方向过滤
                                    matching_positions = [
                                        p for p in all_positions 
                                        if p.get('symbol') == swap_symbol and 
                                        ((side == 'buy' and p.get('side') == 'long') or 
                                         (side == 'sell' and p.get('side') == 'short'))
                                    ]
                                    
                                    if matching_positions:
                                        position = matching_positions[0]
                                        self.log_trading(f"通过备选方法找到匹配持仓，数量: {position.get('contracts')}")
                                        
                                        # 使用相同的方法设置止盈止损
                                        pos_amount = position.get('contracts', quantity)
                                        
                                        # 设置止盈止损参数，确保不包含reduceOnly参数
                                        alt_tpsl_params = {
                                            "symbol": swap_symbol,
                                            "positionSide": "LONG" if side == "buy" else "SHORT",
                                            "stopPrice": tp_price_str,
                                            "type": "TAKE_PROFIT_MARKET",
                                            "closePosition": True,
                                            "workingType": "MARK_PRICE",
                                            "timeInForce": "GTC"
                                        }
                                        
                                        # 设置止盈
                                        try:
                                            tp_side = "sell" if side == "buy" else "buy"
                                            tp_order = self.exchange.create_order(
                                                symbol=swap_symbol,
                                                type="TAKE_PROFIT_MARKET",
                                                side=tp_side,
                                                amount=float(pos_amount),
                                                params=alt_tpsl_params
                                            )
                                            self.log_trading(f"备选方法止盈单已设置，触发价: {tp_price_str}")
                                        except Exception as e:
                                            self.log_trading(f"备选方法设置止盈单失败: {str(e)}", level='error')
                                        
                                        # 设置止损
                                        try:
                                            sl_params = alt_tpsl_params.copy()
                                            sl_params["stopPrice"] = sl_price_str
                                            sl_params["type"] = "STOP_MARKET"
                                            
                                            sl_side = "sell" if side == "buy" else "buy"
                                            sl_order = self.exchange.create_order(
                                                symbol=swap_symbol,
                                                type="STOP_MARKET",
                                                side=sl_side,
                                                amount=float(pos_amount),
                                                params=sl_params
                                            )
                                            self.log_trading(f"备选方法止损单已设置，触发价: {sl_price_str}")
                                        except Exception as e:
                                            self.log_trading(f"备选方法设置止损单失败: {str(e)}", level='error')
                                        
                                        self.log_trading("备选方法设置止盈止损成功")
                                    else:
                                        self.log_trading("备选方法未找到对应持仓，将等待持仓建立", level='warning')
                                except Exception as e:
                                    self.log_trading(f"备选方法查询持仓失败: {str(e)}", level='error')
                        else:
                            self.log_trading("未查询到持仓信息，等待持仓建立后再设置止盈止损", level='warning')
                            
                            # 可以选择在一段时间后重试
                            def retry_set_tpsl():
                                time.sleep(5)  # 等待5秒
                                self.log_trading("正在重试设置止盈止损...")
                                try:
                                    positions = self.exchange.fetch_positions_risk([swap_symbol])
                                    
                                    if positions and len(positions) > 0:
                                        self.log_trading(f"重试获取到 {len(positions)} 个持仓")
                                        
                                        # 查找对应方向的持仓
                                        position = None
                                        for pos in positions:
                                            pos_side = pos.get('side', '')
                                            pos_info = pos.get('info', {})
                                            pos_position_side = pos_info.get('positionSide', '')
                                            
                                            # 多种匹配条件
                                            side_matches = False
                                            if side == 'buy':
                                                side_matches = (pos_side == 'long' or pos_position_side == 'LONG')
                                            else:
                                                side_matches = (pos_side == 'short' or pos_position_side == 'SHORT')
                                            
                                            if side_matches and pos.get('contracts', 0) > 0:
                                                position = pos
                                                break
                                        
                                        if position:
                                            self.log_trading("重试发现持仓，设置止盈止损...")
                                            pos_amount = position.get('contracts', quantity)
                                            
                                            # 设置止盈止损参数，确保不包含reduceOnly
                                            retry_params = {
                                                "symbol": swap_symbol,
                                                "positionSide": "LONG" if side == "buy" else "SHORT",
                                                "stopPrice": tp_price_str,
                                                "type": "TAKE_PROFIT_MARKET",
                                                "closePosition": True,
                                                "workingType": "MARK_PRICE",
                                                "timeInForce": "GTC"
                                            }
                                            
                                            # 设置止盈
                                            try:
                                                tp_side = "sell" if side == "buy" else "buy"
                                                tp_order = self.exchange.create_order(
                                                    symbol=swap_symbol,
                                                    type="TAKE_PROFIT_MARKET",
                                                    side=tp_side,
                                                    amount=float(pos_amount),
                                                    params=retry_params
                                                )
                                                self.log_trading(f"重试设置止盈单成功，触发价: {tp_price_str}")
                                            except Exception as e:
                                                self.log_trading(f"重试设置止盈单失败: {str(e)}", level='error')
                                            
                                            # 设置止损
                                            try:
                                                sl_params = retry_params.copy()
                                                sl_params["stopPrice"] = sl_price_str
                                                sl_params["type"] = "STOP_MARKET"
                                                
                                                sl_side = "sell" if side == "buy" else "buy"
                                                sl_order = self.exchange.create_order(
                                                    symbol=swap_symbol,
                                                    type="STOP_MARKET",
                                                    side=sl_side,
                                                    amount=float(pos_amount),
                                                    params=sl_params
                                                )
                                                self.log_trading(f"重试设置止损单成功，触发价: {sl_price_str}")
                                            except Exception as e:
                                                self.log_trading(f"重试设置止损单失败: {str(e)}", level='error')
                                        else:
                                            self.log_trading("重试未找到对应方向的持仓，无法设置止盈止损")
                                    else:
                                        self.log_trading("重试仍未获取到持仓，止盈止损设置失败")
                                except Exception as e:
                                    self.log_trading(f"重试设置止盈止损失败: {str(e)}", level='error')
                            
                            # 启动重试线程
                            threading.Thread(target=retry_set_tpsl, daemon=True).start()
                    except Exception as e:
                        self.log_trading(f"设置止盈止损过程发生错误: {str(e)}", level='error')
            except Exception as e:
                self.log_trading(f"设置止盈止损失败: {str(e)}", level='error')
            
            return order

        except ValueError as ve:
            error_msg = f"参数验证失败: {str(ve)}"
            self.log_trading(error_msg)
            return None
        except Exception as e:
            error_msg = f"下单失败: {str(e)}"
            self.log_trading(error_msg)
            self.log_trading("错误详情: " + str(e.__class__.__name__))
            if hasattr(e, 'response'):
                self.log_trading("API响应: " + str(e.response.text))
            return None

    def monitor_order_status(self, order_id, symbol):
        """监控订单状态"""
        def check_status():
            try:
                max_retries = 3  # 最大重试次数
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        order = self.exchange.fetch_order(order_id, symbol)
                        status = order['status']
                        
                        if status == 'closed':
                            self.update_log_signal.emit(f"订单 {order_id} 已成交")
                            return
                        elif status == 'canceled':
                            self.update_log_signal.emit(f"订单 {order_id} 已取消")
                            return
                        
                        time.sleep(2)  # 每2秒检查一次
                        retry_count += 1
                        
                    except OrderNotFound as e:
                        self.update_log_signal.emit(f"订单 {order_id} 不存在或已完成")
                        return
                    except Exception as e:
                        if 'Order does not exist' in str(e):
                            self.update_log_signal.emit(f"订单 {order_id} 已不存在")
                            return
                        # 其他错误重试
                        time.sleep(1)
                        retry_count += 1
                
                self.update_log_signal.emit(f"订单 {order_id} 检查超时")
                
            except Exception as e:
                self.update_log_signal.emit(f"订单监控错误: {str(e)}")
                
        # 启动监控线程
        threading.Thread(target=check_status, daemon=True).start()

    def update_ai_trading_price(self):
        """更新AI交易界面的实时价格"""
        try:
            symbol = self.ai_trading_symbol_combo.currentText()

            # 构造永续合约交易对格式
            base_quote = symbol.split('/')
            if len(base_quote) == 2:
                base, quote = base_quote[0], base_quote[1]
                swap_symbol = f"{base}{quote}"  # 例如：将BTC/USDT转换为BTCUSDT

                # 使用CCXT的默认设置获取期货市场数据
                # 不需要额外的参数，因为exchange已经配置为期货模式
                ticker = self.exchange.fetch_ticker(swap_symbol)
                current_price = round(ticker['last'], 2)  # 保留两位小数
            else:
                # 如果格式不是预期的，使用原始符号
                self.log_trading(f"警告：交易对格式异常 {symbol}，将尝试直接使用", level='warning')
                ticker = self.exchange.fetch_ticker(symbol)
                current_price = round(ticker['last'], 2)  # 保留两位小数

            # 更新动态价格显示
            self.update_dynamic_price_display(ticker, current_price)

            # 更新触发分析显示
            self.update_trigger_analysis_display(current_price)

            # 更新AI交易标签页的价格显示
            change_percent = ticker.get('percentage', 0) or 0

            # 确定趋势
            if change_percent > 0.5:
                trend = 'up'
            elif change_percent < -0.5:
                trend = 'down'
            else:
                trend = 'neutral'

            self.update_ai_price_display(current_price, change_percent, trend)

        except Exception as e:
            self.log_trading(f"更新AI交易价格失败: {str(e)}", level='error')

    def update_dynamic_price_display(self, ticker, current_price):
        """更新动态价格显示"""
        try:
            # 存储上一次的价格用于比较
            if not hasattr(self, 'last_price'):
                self.last_price = current_price

            # 计算价格变化
            price_change = current_price - self.last_price

            # 更新主要价格显示
            price_text = f"${current_price:.2f}"

            # 如果图表组件存在并且有ADX值，则在价格后面显示ADX值
            if hasattr(self, 'chart_widget') and hasattr(self.chart_widget, 'current_adx') and self.chart_widget.current_adx is not None:
                adx_value = self.chart_widget.current_adx
                adx_color = "#10B981" if adx_value >= 25 else "#EF4444"
                price_text += f" <span style='color: {adx_color}; font-size: 14px;'>[ADX: {adx_value}]</span>"
                self.price_display.setText(price_text)
                self.price_display.setTextFormat(Qt.TextFormat.RichText)
            else:
                self.price_display.setText(price_text)

            # 更新价格变化指示器
            if price_change > 0:
                self.price_change_indicator.setText("▲")
                self.price_change_indicator.setStyleSheet("color: #10B981; font-size: 16px; font-weight: 700;")
            elif price_change < 0:
                self.price_change_indicator.setText("▼")
                self.price_change_indicator.setStyleSheet("color: #EF4444; font-size: 16px; font-weight: 700;")
            else:
                self.price_change_indicator.setText("●")
                self.price_change_indicator.setStyleSheet("color: #94A3B8; font-size: 16px; font-weight: 700;")

            # 计算24小时涨幅并更新显示
            open_price = ticker.get('open', 0)
            if open_price > 0:
                change_percent = (current_price - open_price) / open_price * 100
                change_symbol = "+" if change_percent >= 0 else ""

                # 根据涨幅程度设置不同的显示效果
                if abs(change_percent) >= 5:
                    # 大幅波动 - 强烈颜色
                    change_color = "#10B981" if change_percent >= 0 else "#EF4444"
                    self.change_display.setText(f"🚀 {change_symbol}{change_percent:.2f}%" if change_percent >= 0 else f"📉 {change_percent:.2f}%")
                    self.change_display.setStyleSheet(f"""
                        font-size: 12px;
                        color: {change_color};
                        font-weight: 700;
                        background: rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.2);
                        border: 2px solid rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.4);
                        border-radius: 6px;
                        padding: 2px 8px;
                    """)
                elif abs(change_percent) >= 2:
                    # 中等波动 - 中等颜色
                    change_color = "#10B981" if change_percent >= 0 else "#EF4444"
                    self.change_display.setText(f"📈 {change_symbol}{change_percent:.2f}%" if change_percent >= 0 else f"📊 {change_percent:.2f}%")
                    self.change_display.setStyleSheet(f"""
                        font-size: 12px;
                        color: {change_color};
                        font-weight: 600;
                        background: rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.15);
                        border: 1px solid rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.3);
                        border-radius: 6px;
                        padding: 2px 8px;
                    """)
                else:
                    # 小幅波动 - 柔和颜色
                    change_color = "#10B981" if change_percent >= 0 else "#EF4444"
                    self.change_display.setText(f"{change_symbol}{change_percent:.2f}%")
                    self.change_display.setStyleSheet(f"""
                        font-size: 12px;
                        color: {change_color};
                        font-weight: 600;
                        background: rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.1);
                        border: 1px solid rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.2);
                        border-radius: 6px;
                        padding: 2px 8px;
                    """)

                # 更新价格趋势指示器
                if abs(change_percent) >= 5:
                    self.price_trend_label.setText("🔥" if change_percent >= 0 else "❄️")
                    self.price_trend_label.setStyleSheet(f"""
                        color: {change_color};
                        font-size: 12px;
                        font-weight: 700;
                        background: rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.3);
                        border: 2px solid rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.5);
                        border-radius: 8px;
                        padding: 2px 6px;
                        min-width: 20px;
                    """)
                elif abs(change_percent) >= 2:
                    self.price_trend_label.setText("📈" if change_percent >= 0 else "📉")
                    self.price_trend_label.setStyleSheet(f"""
                        color: {change_color};
                        font-size: 12px;
                        font-weight: 600;
                        background: rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.2);
                        border: 1px solid rgba({('16, 185, 129' if change_percent >= 0 else '239, 68, 68')}, 0.3);
                        border-radius: 8px;
                        padding: 2px 6px;
                        min-width: 20px;
                    """)
                else:
                    self.price_trend_label.setText("📊")
                    self.price_trend_label.setStyleSheet("""
                        color: #F59E0B;
                        font-size: 12px;
                        font-weight: 600;
                        background: rgba(245, 158, 11, 0.2);
                        border: 1px solid rgba(245, 158, 11, 0.3);
                        border-radius: 8px;
                        padding: 2px 6px;
                        min-width: 20px;
                    """)

            # 更新24小时统计信息
            high_price = ticker.get('high', 0)
            low_price = ticker.get('low', 0)
            volume = ticker.get('baseVolume', 0)

            self.high_price_label.setText(f"${high_price:.2f}" if high_price else "$0.00")
            self.low_price_label.setText(f"${low_price:.2f}" if low_price else "$0.00")

            # 格式化成交量显示
            if volume >= 1000000:
                volume_text = f"{volume/1000000:.1f}M"
            elif volume >= 1000:
                volume_text = f"{volume/1000:.1f}K"
            else:
                volume_text = f"{volume:.0f}"
            self.volume_label.setText(volume_text)

            # 更新上一次价格
            self.last_price = current_price

        except Exception as e:
            self.log_trading(f"更新动态价格显示失败: {str(e)}", level='error')

    def update_trigger_analysis_display(self, current_price):
        """更新动态触发分析显示"""
        try:
            # 获取触发分析阈值
            trigger_threshold = self.trigger_threshold_spinbox.value()

            # 更新进度条阈值标签
            self.progress_threshold_label.setText(f"阈值: {trigger_threshold:.2f}%")

            # 计算当前波动
            if hasattr(self, 'last_trigger_price') and self.last_trigger_price is not None:
                volatility = abs((current_price - self.last_trigger_price) / self.last_trigger_price * 100)
                volatility = round(volatility, 2)

                # 更新当前波动显示
                self.current_volatility_label.setText(f"{volatility:.2f}%")

                # 计算进度条宽度 (最大显示到阈值的150%)
                max_display = trigger_threshold * 1.5
                progress_ratio = min(volatility / max_display, 1.0)
                container_width = self.progress_container.width()
                if container_width > 0:
                    progress_width = int(container_width * progress_ratio)
                    self.progress_fill.setFixedWidth(progress_width)

                # 根据波动程度设置颜色和状态
                if volatility >= trigger_threshold:
                    # 达到触发条件 - 红色闪烁效果
                    self.current_volatility_label.setText(f"🔥 {volatility:.2f}%")
                    self.current_volatility_label.setStyleSheet("""
                        color: #EF4444;
                        font-size: 11px;
                        font-weight: 700;
                        background: rgba(239, 68, 68, 0.3);
                        border: 2px solid rgba(239, 68, 68, 0.6);
                        border-radius: 6px;
                        padding: 2px 8px;
                        min-width: 45px;
                    """)
                    self.trigger_status_label.setText("🚨 触发中")
                    self.trigger_status_label.setStyleSheet("""
                        color: #EF4444;
                        font-size: 10px;
                        font-weight: 700;
                        background: rgba(239, 68, 68, 0.3);
                        border: 2px solid rgba(239, 68, 68, 0.6);
                        border-radius: 8px;
                        padding: 2px 6px;
                        min-width: 50px;
                    """)
                    # 进度条变为红色
                    self.progress_fill.setStyleSheet("""
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #EF4444, stop:1 #DC2626);
                        border-radius: 4px;
                    """)
                elif volatility >= trigger_threshold * 0.8:
                    # 接近触发条件 - 橙色警告
                    self.current_volatility_label.setText(f"⚠️ {volatility:.2f}%")
                    self.current_volatility_label.setStyleSheet("""
                        color: #F59E0B;
                        font-size: 11px;
                        font-weight: 600;
                        background: rgba(245, 158, 11, 0.25);
                        border: 1px solid rgba(245, 158, 11, 0.4);
                        border-radius: 6px;
                        padding: 2px 8px;
                        min-width: 45px;
                    """)
                    self.trigger_status_label.setText("⚠️ 接近")
                    self.trigger_status_label.setStyleSheet("""
                        color: #F59E0B;
                        font-size: 10px;
                        font-weight: 600;
                        background: rgba(245, 158, 11, 0.25);
                        border: 1px solid rgba(245, 158, 11, 0.4);
                        border-radius: 8px;
                        padding: 2px 6px;
                        min-width: 50px;
                    """)
                    # 进度条变为橙色
                    self.progress_fill.setStyleSheet("""
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #10B981, stop:0.6 #F59E0B, stop:1 #F59E0B);
                        border-radius: 4px;
                    """)
                elif volatility >= trigger_threshold * 0.5:
                    # 中等波动 - 蓝色
                    self.current_volatility_label.setText(f"📊 {volatility:.2f}%")
                    self.current_volatility_label.setStyleSheet("""
                        color: #3B82F6;
                        font-size: 11px;
                        font-weight: 600;
                        background: rgba(59, 130, 246, 0.15);
                        border: 1px solid rgba(59, 130, 246, 0.3);
                        border-radius: 6px;
                        padding: 2px 8px;
                        min-width: 45px;
                    """)
                    self.trigger_status_label.setText("📈 活跃")
                    self.trigger_status_label.setStyleSheet("""
                        color: #3B82F6;
                        font-size: 10px;
                        font-weight: 600;
                        background: rgba(59, 130, 246, 0.15);
                        border: 1px solid rgba(59, 130, 246, 0.3);
                        border-radius: 8px;
                        padding: 2px 6px;
                        min-width: 50px;
                    """)
                    # 进度条为蓝绿渐变
                    self.progress_fill.setStyleSheet("""
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #10B981, stop:1 #3B82F6);
                        border-radius: 4px;
                    """)
                else:
                    # 正常状态 - 绿色
                    self.current_volatility_label.setText(f"✅ {volatility:.2f}%")
                    self.current_volatility_label.setStyleSheet("""
                        color: #10B981;
                        font-size: 11px;
                        font-weight: 600;
                        background: rgba(16, 185, 129, 0.15);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 6px;
                        padding: 2px 8px;
                        min-width: 45px;
                    """)
                    self.trigger_status_label.setText("💚 正常")
                    self.trigger_status_label.setStyleSheet("""
                        color: #10B981;
                        font-size: 10px;
                        font-weight: 600;
                        background: rgba(16, 185, 129, 0.2);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        padding: 2px 6px;
                        min-width: 50px;
                    """)
                    # 进度条为绿色
                    self.progress_fill.setStyleSheet("""
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #10B981, stop:1 #059669);
                        border-radius: 4px;
                    """)
            else:
                # 初始状态 - 灰色待机
                self.current_volatility_label.setText("⏳ 等待数据")
                self.current_volatility_label.setStyleSheet("""
                    color: #94A3B8;
                    font-size: 11px;
                    font-weight: 500;
                    background: rgba(148, 163, 184, 0.1);
                    border: 1px solid rgba(148, 163, 184, 0.2);
                    border-radius: 6px;
                    padding: 2px 8px;
                    min-width: 45px;
                """)
                self.trigger_status_label.setText("⏳ 待机")
                self.trigger_status_label.setStyleSheet("""
                    color: #94A3B8;
                    font-size: 10px;
                    font-weight: 500;
                    background: rgba(148, 163, 184, 0.2);
                    border: 1px solid rgba(148, 163, 184, 0.3);
                    border-radius: 8px;
                    padding: 2px 6px;
                    min-width: 50px;
                """)
                # 进度条为空
                self.progress_fill.setFixedWidth(0)
                self.progress_fill.setStyleSheet("""
                    background: rgba(148, 163, 184, 0.3);
                    border-radius: 4px;
                """)

        except Exception as e:
            self.log_trading(f"更新触发分析显示失败: {str(e)}", level='error')


    def update_last_trigger_time(self):
        """更新最后触发时间显示"""
        try:
            current_time = datetime.now().strftime('%H:%M:%S')
            self.last_trigger_time_label.setText(current_time)
            self.last_trigger_time_label.setStyleSheet("""
                color: #A78BFA;
                font-size: 10px;
                font-weight: 600;
            """)
        except Exception as e:
            self.log_trading(f"更新触发时间显示失败: {str(e)}", level='error')

    def log_trading(self, message, level='info'):
        """记录交易日志，可从任意线程调用"""
        try:
            # 使用锁确保多线程环境下的安全
            with self.log_lock:
                # 发送信号到主线程更新UI
                self.update_log_signal.emit(f"{level}|{message}")

                # 同时输出到控制台以便调试
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                print(f"[{level.upper()}] {timestamp} - {message}")
        except Exception as e:
            print(f"日志记录错误: {str(e)}")

    def log_trading_main_thread(self, message):
        """在主线程中更新日志（由信号触发）"""
        try:
            if hasattr(self, 'trading_log') and self.trading_log is not None:
                # 解析级别和消息
                if '|' in message:
                    level, msg = message.split('|', 1)
                else:
                    # 兼容旧格式
                    if '[INFO]' in message:
                        level = 'info'
                        msg = message.replace('[INFO]', '').strip()
                        msg = msg.split(' - ', 1)[-1] if ' - ' in msg else msg
                    elif '[WARNING]' in message:
                        level = 'warning'
                        msg = message.replace('[WARNING]', '').strip()
                        msg = msg.split(' - ', 1)[-1] if ' - ' in msg else msg
                    elif '[ERROR]' in message:
                        level = 'error'
                        msg = message.replace('[ERROR]', '').strip()
                        msg = msg.split(' - ', 1)[-1] if ' - ' in msg else msg
                    else:
                        level = 'info'
                        msg = message

                # 使用增强的日志组件
                if hasattr(self.trading_log, 'add_log'):
                    self.trading_log.add_log(level, msg)
                else:
                    # 回退到旧方法 - 修复appendHtml问题
                    color = '#10B981' if level == 'info' else '#F59E0B' if level == 'warning' else '#EF4444'
                    html_message = f'<span style="color: {color};">{message}</span>'
                    if hasattr(self.trading_log, 'append'):
                        self.trading_log.append.emit(html_message)
                    else:
                        # 最终回退方案
                        self.trading_log.append(html_message)
        except Exception as e:
            # 记录错误但不再递归调用log_trading以避免无限循环
            print(f"日志更新UI错误: {str(e)}")

    def save_trading_log(self):
        """保存交易日志到文件"""
        try:
            log_content = self.trading_log.toHtml()
            if not log_content:
                self.show_message_signal.emit("警告", "没有可保存的日志内容！", "warning")
                return
            
            # 获取当前时间作为文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"trading_log_{timestamp}.html"
            
            # 保存文件
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write(log_content)
            
            self.show_message_signal.emit("成功", f"日志已保存为 {file_name}", "info")
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"保存日志失败: {str(e)}", "error")

    def delete_trading_log(self):
        """删除当前交易日志"""
        try:
            # 使用自定义对话框替代 QMessageBox
            dialog = QDialog(self)
            dialog.setWindowTitle("确认")
            layout = QVBoxLayout(dialog)
            
            label = QLabel("确定要删除当前交易日志吗？")
            layout.addWidget(label)
            
            button_box = QHBoxLayout()
            yes_button = QPushButton("是")
            no_button = QPushButton("否")
            
            yes_button.clicked.connect(lambda: self._delete_log_confirmed(dialog))
            no_button.clicked.connect(dialog.reject)
            
            button_box.addWidget(yes_button)
            button_box.addWidget(no_button)
            layout.addLayout(button_box)
            
            dialog.exec()
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"删除日志失败: {str(e)}", "error")
            
    def _delete_log_confirmed(self, dialog):
        """确认删除日志"""
        self.trading_log.clear()
        dialog.accept()
        self.show_message_signal.emit("成功", "交易日志已删除", "info")

    def show_message_box(self, title, message, msg_type='info'):
        """在主线程中显示消息框"""
        if msg_type == 'warning':
            QMessageBox.warning(self, title, message)
        elif msg_type == 'error':
            QMessageBox.critical(self, title, message)
        else:
            QMessageBox.information(self, title, message)

    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
                # 加载配置项
            self.tp_percent = config.get('tp_percent', 1.5)  # 默认值为1.5%
            self.sl_percent = config.get('sl_percent', 1.5)  # 默认值为1.5%

            # 更新UI控件
            self.tp_spinbox.setValue(self.tp_percent)
            self.sl_spinbox.setValue(self.sl_percent)

            # 记录加载的配置
            self.log_trading(f"已加载止盈止损配置: TP={self.tp_percent}%, SL={self.sl_percent}%")
        except FileNotFoundError:
            # 使用默认配置
            self.tp_percent = 1.5  # 默认值为1.5%
            self.sl_percent = 1.5  # 默认值为1.5%

            # 记录使用默认配置
            self.log_trading(f"使用默认止盈止损配置: TP={self.tp_percent}%, SL={self.sl_percent}%")

            # 保存默认配置到文件
            with open('config.json', 'w') as f:
                json.dump({
                    'tp_percent': self.tp_percent,
                    'sl_percent': self.sl_percent
                }, f, indent=4)
                
                
    def get_market_data(self, symbol):
        """缓存市场数据"""
        cache = self._market_data_cache.get(symbol)
        if cache is None:
            cache = CachedData()
            self._market_data_cache[symbol] = cache
            
        if not cache.is_valid():
            # 缓存过期或不存在，获取新数据
            try:
                # 使用CCXT的默认设置获取期货市场数据
                # 不需要额外的参数，因为exchange已经配置为期货模式
                cache.data = self.exchange.fetch_ticker(symbol)
                cache.timestamp = datetime.now()
                self.log_trading(f"成功获取{symbol}的市场数据")
            except Exception as e:
                self.log_trading(f"获取{symbol}市场数据失败: {str(e)}", level='error')
                # 如果没有缓存数据，返回一个空对象避免错误
                if cache.data is None:
                    cache.data = {}
            
        return cache.data



    def _generate_technical_summary(self, current_price, dfs, indicators):
        """生成技术分析总结"""
        try:
            # 初始化可能在模板字符串中使用的变量，确保它们在所有代码路径中都有值
            market_state = "RANGING_WEAK_TREND"  # 默认为震荡或弱趋势
            adx_value = 0.0
            plus_di_value = 0.0
            minus_di_value = 0.0
            current_adx_threshold = getattr(self, 'adx_threshold', 25)
            
            # 解包关键指标
            df = dfs.get('1h')
            
            if len(df) < 50:
                return "数据不足，无法生成技术分析摘要"
                
            close_prices = df['close'].values
            high_prices = df['high'].values
            low_prices = df['low'].values
            
            # 计算技术指标值
            rsi = indicators.get('rsi', talib.RSI(close_prices))
            
            # 计算ADX相关指标
            adx = indicators.get('adx', talib.ADX(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14)))
            di_plus = indicators.get('di_plus', talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14)))
            di_minus = indicators.get('di_minus', talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14)))
            
            # 判断市场状态
            market_state = "RANGING_WEAK_TREND"  # 默认为震荡或弱趋势
            adx_value = adx[-1]
            plus_di_value = di_plus[-1]
            minus_di_value = di_minus[-1]
            current_adx_threshold = getattr(self, 'adx_threshold', 25)

            if adx_value > current_adx_threshold:
                if plus_di_value > minus_di_value:
                    market_state = "STRONG_UPTREND"
                elif minus_di_value > plus_di_value:
                    market_state = "STRONG_DOWNTREND"
            
            # 继续计算其他指标
            macd, signal, hist = indicators.get('macd', talib.MACD(close_prices))
            upper, middle, lower = indicators.get('bollinger', talib.BBANDS(
                close_prices, 
                timeperiod=getattr(self, 'bb_period', 20),
                nbdevup=getattr(self, 'bb_std', 2.0),
                nbdevdn=getattr(self, 'bb_std', 2.0)
            ))
            ema20 = indicators.get('ema20', talib.EMA(close_prices, timeperiod=getattr(self, 'ema_short', 20)))
            ema50 = indicators.get('ema50', talib.EMA(close_prices, timeperiod=getattr(self, 'ema_medium', 50)))
            ema200 = indicators.get('ema200', talib.EMA(close_prices, timeperiod=getattr(self, 'ema_long', 200)))
            
            # 解释市场状态
            market_state_desc = {
                "STRONG_UPTREND": "强劲上涨趋势",
                "STRONG_DOWNTREND": "强劲下跌趋势",
                "RANGING_WEAK_TREND": "震荡或弱趋势"
            }.get(market_state, "未知")
            
            # 生成摘要，基于市场状态动态解读指标
            summary = f"【市场状态】{market_state_desc} (ADX: {adx_value:.2f} +DI: {plus_di_value:.2f} -DI: {minus_di_value:.2f})\n"
            
            # 价格相对于均线
            summary += "【均线分析】"
            above_ema20 = current_price > ema20[-1]
            above_ema50 = current_price > ema50[-1]
            above_ema200 = current_price > ema200[-1]
            
            if above_ema20 and above_ema50 and above_ema200:
                summary += "价格位于所有均线之上，整体看多。"
            elif not above_ema20 and not above_ema50 and not above_ema200:
                summary += "价格位于所有均线之下，整体看空。"
            else:
                if above_ema20 and above_ema50:
                    summary += "价格位于中短期均线之上，中期看多。"
                elif not above_ema20 and not above_ema50:
                    summary += "价格位于中短期均线之下，中期看空。"
                else:
                    summary += "价格在均线之间波动，趋势不明确。"
            
            # 均线交叉
            if ema20[-1] > ema50[-1] and ema20[-2] <= ema50[-2]:
                if market_state == "STRONG_UPTREND":
                    summary += " 短期均线上穿中期均线(金叉)，强趋势中信号强烈，大概率继续上行。"
                else:
                    summary += " 短期均线上穿中期均线(金叉)，可能开启上升趋势。"
            elif ema20[-1] < ema50[-1] and ema20[-2] >= ema50[-2]:
                if market_state == "STRONG_DOWNTREND":
                    summary += " 短期均线下穿中期均线(死叉)，强趋势中信号强烈，大概率继续下行。"
                else:
                    summary += " 短期均线下穿中期均线(死叉)，可能开启下降趋势。"
                
            # RSI分析，考虑市场状态
            summary += "\n【RSI分析】"
            rsi_value = rsi[-1]
            rsi_overbought = getattr(self, 'rsi_overbought', 70)
            rsi_oversold = getattr(self, 'rsi_oversold', 30)
            
            if market_state == "STRONG_UPTREND":
                if rsi_value > rsi_overbought:
                    summary += f"RSI位于超买区域({rsi_value:.2f})，但在强上涨趋势中，这常常表示趋势持续而非即将反转。"
                else:
                    summary += f"RSI值为{rsi_value:.2f}，在强上涨趋势中处于健康水平。"
            elif market_state == "STRONG_DOWNTREND":
                if rsi_value < rsi_oversold:
                    summary += f"RSI位于超卖区域({rsi_value:.2f})，但在强下跌趋势中，这可能表示趋势继续而非即将反转。"
                else:
                    summary += f"RSI值为{rsi_value:.2f}，在强下跌趋势中处于正常水平。"
            else: # RANGING_WEAK_TREND
                if rsi_value > rsi_overbought:
                    summary += f"RSI位于超买区域({rsi_value:.2f})，震荡市场中可能即将回调。"
                elif rsi_value < rsi_oversold:
                    summary += f"RSI位于超卖区域({rsi_value:.2f})，震荡市场中可能即将反弹。"
                else:
                    summary += f"RSI值为{rsi_value:.2f}，处于中性区域。"
                    
            # MACD分析，考虑市场状态
            summary += "\n【MACD分析】"
            if macd[-1] > signal[-1] and macd[-2] <= signal[-2]:
                if market_state == "STRONG_UPTREND":
                    summary += "MACD金叉，在强上涨趋势中是强烈的看多信号，建议继续持有多单或考虑加仓。"
                elif market_state == "STRONG_DOWNTREND":
                    summary += "MACD金叉，但在强下跌趋势中需谨慎对待，可能是短期反弹而非趋势反转。"
                else:
                    summary += "MACD金叉，震荡市场中是较好的买入信号。"
            elif macd[-1] < signal[-1] and macd[-2] >= signal[-2]:
                if market_state == "STRONG_DOWNTREND":
                    summary += "MACD死叉，在强下跌趋势中是强烈的看空信号，建议继续持有空单或考虑加仓。"
                elif market_state == "STRONG_UPTREND":
                    summary += "MACD死叉，但在强上涨趋势中需谨慎对待，可能是短期回调而非趋势反转。"
                else:
                    summary += "MACD死叉，震荡市场中是较好的卖出信号。"
            elif macd[-1] > signal[-1]:
                summary += "MACD位于信号线上方，动能偏向多头。"
            else:
                summary += "MACD位于信号线下方，动能偏向空头。"
                
            # 布林带分析，考虑市场状态
            summary += "\n【布林带分析】"
            if market_state == "STRONG_UPTREND":
                if current_price > upper[-1]:
                    summary += "价格突破布林上轨，在强上涨趋势中表示趋势加速，而非超买。"
                else:
                    summary += f"布林带显示上行趋势，当前价格为{current_price:.2f}，上轨{upper[-1]:.2f}，中轨{middle[-1]:.2f}，下轨{lower[-1]:.2f}。"
            elif market_state == "STRONG_DOWNTREND":
                if current_price < lower[-1]:
                    summary += "价格突破布林下轨，在强下跌趋势中表示趋势加速，而非超卖。"
                else:
                    summary += f"布林带显示下行趋势，当前价格为{current_price:.2f}，上轨{upper[-1]:.2f}，中轨{middle[-1]:.2f}，下轨{lower[-1]:.2f}。"
            else:
                if current_price > upper[-1]:
                    summary += "价格突破布林上轨，在震荡市场中通常表示超买，可能即将回调。"
                elif current_price < lower[-1]:
                    summary += "价格突破布林下轨，在震荡市场中通常表示超卖，可能即将反弹。"
                else:
                    width = (upper[-1] - lower[-1]) / middle[-1]
                    if width < 0.1:
                        summary += "布林带收窄，可能即将突破。"
                    else:
                        position = (current_price - lower[-1]) / (upper[-1] - lower[-1])
                        if position > 0.8:
                            summary += "价格接近布林上轨，偏向看空。"
                        elif position < 0.2:
                            summary += "价格接近布林下轨，偏向看多。"
                        else:
                            summary += "价格在布林带中间区域，趋势中性。"
                            
            return summary
        except Exception as e:
            self.log_trading(f"生成技术分析摘要时出错: {str(e)}", level='error')
            return "技术分析摘要生成失败"

    def on_leverage_changed(self, value):
        """处理杠杆倍数变化"""
        try:
            # 更新杠杆风险指示器
            self.update_leverage_risk_indicator(value)

            symbol = self.ai_trading_symbol_combo.currentText()
            base, quote = symbol.split('/')
            swap_symbol = f"{base}{quote}"  # 币安合约格式，如BTCUSDT

            # 使用币安API设置杠杆倍数
            try:
                # 先检查是否有未完成的订单
                try:
                    orders = self.exchange.fetch_open_orders(symbol=swap_symbol)
                    if orders:
                        self.log_trading("请先取消所有未完成订单再修改杠杆", level='warning')
                except Exception as e:
                    self.log_trading(f"检查未完成订单时出错: {str(e)}", level='warning')
                    # 回滚杠杆设置
                    self.ai_leverage_spinbox.blockSignals(True)
                    self.ai_leverage_spinbox.setValue(self.ai_leverage_spinbox.value())
                    self.ai_leverage_spinbox.blockSignals(False)
                    return

                # 设置杠杆倍数
                self.exchange.set_leverage(value, swap_symbol, params={
                    'marginMode': 'isolated',  # 逐仓模式
                    'positionSide': 'BOTH',    # 双向持仓
                    'type': 'future',          # 指定为期货/合约交易
                    'future': True             # 明确指定为合约交易
                })

                self.log_trading(f"杠杆倍数已更新为: {value}x", level='info')

            except Exception as e:
                # 回滚杠杆设置
                self.ai_leverage_spinbox.blockSignals(True)
                self.ai_leverage_spinbox.setValue(self.ai_leverage_spinbox.value())
                self.ai_leverage_spinbox.blockSignals(False)

                # 如果是设置杠杆错误，尝试继续而不是直接失败
                if "setLeverage()" in str(e):
                    self.log_trading(f"设置杠杆失败，但将继续使用当前杠杆: {str(e)}", level='warning')
                else:
                    raise

        except Exception as e:
            self.log_trading(f"更新杠杆倍数失败: {str(e)}", level='error')

    def update_leverage_risk_indicator(self, leverage):
        """更新杠杆风险指示器"""
        try:
            if leverage <= 5:
                risk_text = "安全"
                risk_color = "#10B981"
                risk_rgba = "16, 185, 129"
            elif leverage <= 20:
                risk_text = "中等"
                risk_color = "#F59E0B"
                risk_rgba = "245, 158, 11"
            else:
                risk_text = "危险"
                risk_color = "#EF4444"
                risk_rgba = "239, 68, 68"

            if hasattr(self, 'leverage_risk_indicator'):
                self.leverage_risk_indicator.setText(risk_text)
                self.leverage_risk_indicator.setStyleSheet(f"""
                    color: {risk_color};
                    font-size: 9px;
                    font-weight: 600;
                    background: rgba({risk_rgba}, 0.2);
                    border: 1px solid rgba({risk_rgba}, 0.3);
                    border-radius: 6px;
                    padding: 1px 4px;
                """)
        except Exception as e:
            self.log_trading(f"更新杠杆风险指示器失败: {str(e)}", level='error')





    def show_indicator_settings(self):
        """显示技术指标设置对话框"""
        try:
            # 创建对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("技术指标设置")
            dialog.setMinimumWidth(600)  # 增加宽度以容纳说明
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #0B0E11;
                }
                QLabel {
                    color: #E6E8EA;
                    font-size: 14px;
                    min-height: 25px;
                }
                QLabel[type="description"] {
                    color: #8C8C8C;
                    font-size: 12px;
                    font-style: italic;
                }
                QDoubleSpinBox, QSpinBox {
                    padding: 5px;
                    border: 1px solid #2a2e30;
                    border-radius: 4px;
                    background-color: #1b1e22;
                    color: #E6E8EA;
                    font-size: 13px;
                    min-width: 100px;
                }
                QDoubleSpinBox:focus, QSpinBox:focus {
                    border: 1px solid #35383c;
                }
                QGroupBox {
                    font-weight: bold;
                    font-size: 14px;
                    color: #F0B90B;
                    margin-top: 1.5em;
                    padding-top: 15px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px;
                }
                QPushButton {
                    padding: 8px 15px;
                    background-color: #2EBD85;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 36px;
                }
                QPushButton:hover {
                    background-color: #259C6C;
                }
                QPushButton[type="cancel"] {
                    background-color: #2a2e30;
                    color: #E6E8EA;
                }
                QPushButton[type="cancel"]:hover {
                    background-color: #35383c;
                }
                QPushButton[type="preset"] {
                    background-color: #F0B90B;
                    color: #0B0E11;
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 13px;
                    min-height: 30px;
                }
                QPushButton[type="preset"]:hover {
                    background-color: #F8D33A;
                }
                QComboBox {
                    padding: 5px;
                    border: 1px solid #2a2e30;
                    border-radius: 4px;
                    background-color: #1b1e22;
                    color: #E6E8EA;
                    font-size: 13px;
                    min-width: 100px;
                }
                QComboBox:focus {
                    border: 1px solid #35383c;
                }
                QComboBox::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: center right;
                    width: 20px;
                    border-left-width: 1px;
                    border-left-color: #2a2e30;
                    border-left-style: solid;
                }
                QComboBox QAbstractItemView {
                    background-color: #1b1e22;
                    border: 1px solid #2a2e30;
                    selection-background-color: #2a2e30;
                }
            """)
            
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            
            # 加载当前设置（如果有）
            self.load_indicator_settings()
            
            # 添加时段预设选择部分
            preset_group = QGroupBox("时段预设")
            preset_layout = QVBoxLayout()
            
            # 添加说明标签
            preset_label = QLabel("选择不同时段的预设参数，快速应用于技术指标:")
            preset_label.setWordWrap(True)
            preset_layout.addWidget(preset_label)
            
            # 创建预设时段选择按钮
            preset_buttons_layout = QHBoxLayout()
            preset_buttons_layout.setSpacing(10)
            
            # 短期按钮 (5分钟/15分钟)
            short_term_btn = QPushButton("短期 (5-15分钟)")
            short_term_btn.setProperty("type", "preset")
            short_term_btn.clicked.connect(lambda: self.apply_timeframe_preset("short_term", dialog))
            
            # 中期按钮 (1小时/4小时)
            medium_term_btn = QPushButton("中期 (1-4小时)")
            medium_term_btn.setProperty("type", "preset")
            medium_term_btn.clicked.connect(lambda: self.apply_timeframe_preset("medium_term", dialog))
            
            # 长期按钮 (日线/周线)
            long_term_btn = QPushButton("长期 (日/周)")
            long_term_btn.setProperty("type", "preset")
            long_term_btn.clicked.connect(lambda: self.apply_timeframe_preset("long_term", dialog))
            
            # 添加按钮到布局
            preset_buttons_layout.addWidget(short_term_btn)
            preset_buttons_layout.addWidget(medium_term_btn)
            preset_buttons_layout.addWidget(long_term_btn)
            
            preset_layout.addLayout(preset_buttons_layout)
            
            # 添加市场类型选择
            market_type_layout = QHBoxLayout()
            market_type_layout.addWidget(QLabel("市场类型:"))
            
            self.market_type_combo = QComboBox()
            self.market_type_combo.addItems(["普通市场", "震荡市场", "强趋势市场"])
            # 创建中介函数避免直接引用dialog
            self.market_type_combo.currentIndexChanged.connect(lambda idx: self.apply_market_type_preset(dialog))
            market_type_layout.addWidget(self.market_type_combo)
            
            # 添加币种类型选择
            market_type_layout.addWidget(QLabel("币种类型:"))
            
            self.coin_type_combo = QComboBox()
            self.coin_type_combo.addItems(["主流币", "山寨币", "高波动币"])
            # 创建中介函数避免直接引用dialog
            self.coin_type_combo.currentIndexChanged.connect(lambda idx: self.apply_coin_type_preset(dialog))
            market_type_layout.addWidget(self.coin_type_combo)
            
            preset_layout.addLayout(market_type_layout)
            preset_group.setLayout(preset_layout)
            layout.addWidget(preset_group)
            
            # 创建滚动区域
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: none;
                    background-color: transparent;
                }
                QScrollBar:vertical {
                    background: #2A2A2A;
                    width: 10px;
                    margin: 0px 0px 0px 0px;
                }
                QScrollBar::handle:vertical {
                    background: #444444;
                    min-height: 20px;
                    border-radius: 5px;
                }
                QScrollBar::add-line:vertical,
                QScrollBar::sub-line:vertical {
                    background: none;
                }
                QScrollBar::add-page:vertical,
                QScrollBar::sub-page:vertical {
                    background: none;
                }
            """)
            
            scroll_content = QWidget()
            scroll_layout = QVBoxLayout(scroll_content)
            scroll_layout.setContentsMargins(5, 5, 5, 5)
            scroll_layout.setSpacing(15)
            
            # ============================
            # === 添加动量指标分类分组 ===
            # ============================
            momentum_category = QGroupBox("动量指标")
            momentum_category.setStyleSheet("QGroupBox { color: #2EBD85; font-size: 16px; }")
            momentum_layout = QVBoxLayout()
            
            # === RSI指标设置 ===
            rsi_group = QGroupBox("RSI指标设置")
            rsi_layout = QGridLayout()
            rsi_layout.setVerticalSpacing(10)
            
            # RSI说明
            rsi_desc = QLabel("相对强弱指标，衡量市场超买超卖状态。高于70通常视为超买，低于30视为超卖。")
            rsi_desc.setProperty("type", "description")
            rsi_desc.setWordWrap(True)
            rsi_layout.addWidget(rsi_desc, 0, 0, 1, 2)
            
            # RSI使用场景说明
            rsi_usage = QLabel("使用场景：可用于判断市场反转点，RSI低位反弹时买入，高位回落时卖出")
            rsi_usage.setProperty("type", "description")
            rsi_usage.setWordWrap(True)
            rsi_layout.addWidget(rsi_usage, 1, 0, 1, 2)
            
            # RSI周期
            self.rsi_period_spinbox = QSpinBox()
            self.rsi_period_spinbox.setRange(5, 50)
            self.rsi_period_spinbox.setValue(getattr(self, 'rsi_period', 14))
            rsi_layout.addWidget(QLabel("RSI周期:"), 2, 0)
            rsi_layout.addWidget(self.rsi_period_spinbox, 2, 1)
            
            # RSI超买阈值
            self.rsi_overbought_spinbox = QSpinBox()
            self.rsi_overbought_spinbox.setRange(60, 90)
            self.rsi_overbought_spinbox.setValue(getattr(self, 'rsi_overbought', 70))
            rsi_layout.addWidget(QLabel("超买阈值:"), 3, 0)
            rsi_layout.addWidget(self.rsi_overbought_spinbox, 3, 1)
            
            # RSI超卖阈值
            self.rsi_oversold_spinbox = QSpinBox()
            self.rsi_oversold_spinbox.setRange(10, 40)
            self.rsi_oversold_spinbox.setValue(getattr(self, 'rsi_oversold', 30))
            rsi_layout.addWidget(QLabel("超卖阈值:"), 4, 0)
            rsi_layout.addWidget(self.rsi_oversold_spinbox, 4, 1)
            
            rsi_group.setLayout(rsi_layout)
            momentum_layout.addWidget(rsi_group)
            
            # 随机指标设置
            stoch_group = QGroupBox("随机指标设置")
            stoch_layout = QGridLayout()
            stoch_layout.setVerticalSpacing(10)
            
            # 随机指标说明
            stoch_desc = QLabel("测量价格相对于特定时期内最高/最低价范围的位置，用于识别超买超卖状态和潜在趋势反转。")
            stoch_desc.setProperty("type", "description")
            stoch_desc.setWordWrap(True)
            stoch_layout.addWidget(stoch_desc, 0, 0, 1, 2)
            
            # 随机指标使用场景
            stoch_usage = QLabel("使用场景：当K线和D线从超卖区域向上穿越时产生买入信号；从超买区域向下穿越时产生卖出信号。")
            stoch_usage.setProperty("type", "description")
            stoch_usage.setWordWrap(True)
            stoch_layout.addWidget(stoch_usage, 1, 0, 1, 2)
            
            # 随机指标超买阈值
            self.stoch_overbought_spinbox = QSpinBox()
            self.stoch_overbought_spinbox.setRange(70, 90)
            self.stoch_overbought_spinbox.setValue(getattr(self, 'stoch_overbought', 80))
            stoch_layout.addWidget(QLabel("随机指标超买:"), 2, 0)
            stoch_layout.addWidget(self.stoch_overbought_spinbox, 2, 1)
            
            # 随机指标超卖阈值
            self.stoch_oversold_spinbox = QSpinBox()
            self.stoch_oversold_spinbox.setRange(10, 30)
            self.stoch_oversold_spinbox.setValue(getattr(self, 'stoch_oversold', 20))
            stoch_layout.addWidget(QLabel("随机指标超卖:"), 3, 0)
            stoch_layout.addWidget(self.stoch_oversold_spinbox, 3, 1)
            
            stoch_group.setLayout(stoch_layout)
            momentum_layout.addWidget(stoch_group)
            
            # CCI指标设置
            cci_group = QGroupBox("CCI指标设置")
            cci_layout = QGridLayout()
            cci_layout.setVerticalSpacing(10)
            
            # CCI指标说明
            cci_desc = QLabel("商品通道指数，用于识别周期性市场的动量变化、超买超卖水平及可能的价格反转。")
            cci_desc.setProperty("type", "description")
            cci_desc.setWordWrap(True)
            cci_layout.addWidget(cci_desc, 0, 0, 1, 2)
            
            # CCI使用场景
            cci_usage = QLabel("使用场景：CCI从下方穿越-100可能是买入信号；从上方穿越+100可能是卖出信号。")
            cci_usage.setProperty("type", "description")
            cci_usage.setWordWrap(True)
            cci_layout.addWidget(cci_usage, 1, 0, 1, 2)
            
            # CCI指标超买阈值
            self.cci_overbought_spinbox = QSpinBox()
            self.cci_overbought_spinbox.setRange(80, 200)
            self.cci_overbought_spinbox.setValue(getattr(self, 'cci_overbought', 100))
            cci_layout.addWidget(QLabel("CCI超买阈值:"), 2, 0)
            cci_layout.addWidget(self.cci_overbought_spinbox, 2, 1)
            
            # CCI指标超卖阈值
            self.cci_oversold_spinbox = QSpinBox()
            self.cci_oversold_spinbox.setRange(-200, -80)
            self.cci_oversold_spinbox.setValue(getattr(self, 'cci_oversold', -100))
            cci_layout.addWidget(QLabel("CCI超卖阈值:"), 3, 0)
            cci_layout.addWidget(self.cci_oversold_spinbox, 3, 1)
            
            cci_group.setLayout(cci_layout)
            momentum_layout.addWidget(cci_group)
            
            # MFI指标设置
            mfi_group = QGroupBox("MFI指标设置")
            mfi_layout = QGridLayout()
            mfi_layout.setVerticalSpacing(10)
            
            # MFI指标说明
            mfi_desc = QLabel("资金流量指数，结合价格和成交量衡量市场资金流入和流出情况，用于确认价格趋势和可能的反转点。")
            mfi_desc.setProperty("type", "description")
            mfi_desc.setWordWrap(True)
            mfi_layout.addWidget(mfi_desc, 0, 0, 1, 2)
            
            # MFI使用场景
            mfi_usage = QLabel("使用场景：MFI低于20进入超卖区域考虑买入；高于80进入超买区域考虑卖出；MFI与价格背离可能预示趋势反转。")
            mfi_usage.setProperty("type", "description")
            mfi_usage.setWordWrap(True)
            mfi_layout.addWidget(mfi_usage, 1, 0, 1, 2)
            
            # MFI指标超买阈值
            self.mfi_overbought_spinbox = QSpinBox()
            self.mfi_overbought_spinbox.setRange(70, 90)
            self.mfi_overbought_spinbox.setValue(getattr(self, 'mfi_overbought', 80))
            mfi_layout.addWidget(QLabel("MFI超买阈值:"), 2, 0)
            mfi_layout.addWidget(self.mfi_overbought_spinbox, 2, 1)
            
            # MFI指标超卖阈值
            self.mfi_oversold_spinbox = QSpinBox()
            self.mfi_oversold_spinbox.setRange(10, 30)
            self.mfi_oversold_spinbox.setValue(getattr(self, 'mfi_oversold', 20))
            mfi_layout.addWidget(QLabel("MFI超卖阈值:"), 3, 0)
            mfi_layout.addWidget(self.mfi_oversold_spinbox, 3, 1)
            
            mfi_group.setLayout(mfi_layout)
            momentum_layout.addWidget(mfi_group)
            
            momentum_category.setLayout(momentum_layout)
            scroll_layout.addWidget(momentum_category)
            
            # ============================
            # === 添加趋势指标分类分组 ===
            # ============================
            trend_category = QGroupBox("趋势指标")
            trend_category.setStyleSheet("QGroupBox { color: #F0B90B; font-size: 16px; }")
            trend_layout = QVBoxLayout()
            
            # === MACD指标设置 ===
            macd_group = QGroupBox("MACD指标设置")
            macd_layout = QGridLayout()
            macd_layout.setVerticalSpacing(10)
            
            # MACD说明
            macd_desc = QLabel("移动平均收敛散度，显示短期与长期移动平均线之间的关系，用于识别动量、趋势方向和潜在的转折点。")
            macd_desc.setProperty("type", "description")
            macd_desc.setWordWrap(True)
            macd_layout.addWidget(macd_desc, 0, 0, 1, 2)
            
            # MACD使用场景
            macd_usage = QLabel("使用场景：MACD线从下向上穿越信号线为买入信号；MACD线从上向下穿越信号线为卖出信号；柱状图表示动量大小和变化。")
            macd_usage.setProperty("type", "description")
            macd_usage.setWordWrap(True)
            macd_layout.addWidget(macd_usage, 1, 0, 1, 2)
            
            # MACD快线周期
            self.macd_fast_spinbox = QSpinBox()
            self.macd_fast_spinbox.setRange(5, 30)
            self.macd_fast_spinbox.setValue(getattr(self, 'macd_fast', 12))
            macd_layout.addWidget(QLabel("快线周期:"), 2, 0)
            macd_layout.addWidget(self.macd_fast_spinbox, 2, 1)
            
            # MACD慢线周期
            self.macd_slow_spinbox = QSpinBox()
            self.macd_slow_spinbox.setRange(10, 50)
            self.macd_slow_spinbox.setValue(getattr(self, 'macd_slow', 26))
            macd_layout.addWidget(QLabel("慢线周期:"), 3, 0)
            macd_layout.addWidget(self.macd_slow_spinbox, 3, 1)
            
            # MACD信号线周期
            self.macd_signal_spinbox = QSpinBox()
            self.macd_signal_spinbox.setRange(5, 20)
            self.macd_signal_spinbox.setValue(getattr(self, 'macd_signal', 9))
            macd_layout.addWidget(QLabel("信号线周期:"), 4, 0)
            macd_layout.addWidget(self.macd_signal_spinbox, 4, 1)
            
            macd_group.setLayout(macd_layout)
            trend_layout.addWidget(macd_group)
            
            # === 移动平均线设置 ===
            ma_group = QGroupBox("移动平均线设置")
            ma_layout = QGridLayout()
            ma_layout.setVerticalSpacing(10)
            
            # MA说明
            ma_desc = QLabel("平滑价格数据以帮助识别趋势方向和潜在的支撑/阻力位。短期均线穿越长期均线产生交易信号。")
            ma_desc.setProperty("type", "description")
            ma_desc.setWordWrap(True)
            ma_layout.addWidget(ma_desc, 0, 0, 1, 2)
            
            # MA使用场景
            ma_usage = QLabel("使用场景：价格在均线上方表示上升趋势；在均线下方表示下降趋势；短期均线上穿长期均线为黄金交叉(买入信号)；下穿为死亡交叉(卖出信号)。")
            ma_usage.setProperty("type", "description")
            ma_usage.setWordWrap(True)
            ma_layout.addWidget(ma_usage, 1, 0, 1, 2)
            
            # EMA短周期
            self.ema_short_spinbox = QSpinBox()
            self.ema_short_spinbox.setRange(5, 30)
            self.ema_short_spinbox.setValue(getattr(self, 'ema_short', 20))
            ma_layout.addWidget(QLabel("EMA短周期:"), 2, 0)
            ma_layout.addWidget(self.ema_short_spinbox, 2, 1)
            
            # EMA中周期
            self.ema_medium_spinbox = QSpinBox()
            self.ema_medium_spinbox.setRange(20, 100)
            self.ema_medium_spinbox.setValue(getattr(self, 'ema_medium', 50))
            ma_layout.addWidget(QLabel("EMA中周期:"), 3, 0)
            ma_layout.addWidget(self.ema_medium_spinbox, 3, 1)
            
            # EMA长周期
            self.ema_long_spinbox = QSpinBox()
            self.ema_long_spinbox.setRange(100, 300)
            self.ema_long_spinbox.setValue(getattr(self, 'ema_long', 200))
            ma_layout.addWidget(QLabel("EMA长周期:"), 4, 0)
            ma_layout.addWidget(self.ema_long_spinbox, 4, 1)
            
            ma_group.setLayout(ma_layout)
            trend_layout.addWidget(ma_group)
            
            # ADX指标设置
            adx_group = QGroupBox("ADX指标设置")
            adx_layout = QGridLayout()
            adx_layout.setVerticalSpacing(10)
            
            # ADX说明
            adx_desc = QLabel("平均趋向指数，用于测量市场趋势的强度，无论方向如何。较高的ADX值表示趋势较强，较低的值表示市场处于横盘整理。")
            adx_desc.setProperty("type", "description")
            adx_desc.setWordWrap(True)
            adx_layout.addWidget(adx_desc, 0, 0, 1, 2)
            
            # ADX使用场景
            adx_usage = QLabel("使用场景：ADX高于25表示较强的趋势；低于20表示弱趋势或无趋势；结合DI+和DI-判断趋势方向，DI+>DI-为上升趋势，反之为下降趋势。")
            adx_usage.setProperty("type", "description")
            adx_usage.setWordWrap(True)
            adx_layout.addWidget(adx_usage, 1, 0, 1, 2)
            
            # ADX指标周期
            self.adx_period_spinbox = QSpinBox()
            self.adx_period_spinbox.setRange(5, 50)
            self.adx_period_spinbox.setValue(getattr(self, 'adx_period', 14))
            adx_layout.addWidget(QLabel("ADX周期:"), 2, 0)
            adx_layout.addWidget(self.adx_period_spinbox, 2, 1)
            
            # ADX指标阈值
            self.adx_threshold_spinbox = QSpinBox()
            self.adx_threshold_spinbox.setRange(15, 40)
            self.adx_threshold_spinbox.setValue(getattr(self, 'adx_threshold', 25))
            adx_layout.addWidget(QLabel("ADX趋势阈值:"), 3, 0)
            adx_layout.addWidget(self.adx_threshold_spinbox, 3, 1)
            
            adx_group.setLayout(adx_layout)
            trend_layout.addWidget(adx_group)
            
            trend_category.setLayout(trend_layout)
            scroll_layout.addWidget(trend_category)
            
            # ============================
            # === 添加波动指标分类分组 ===
            # ============================
            volatility_category = QGroupBox("波动指标")
            volatility_category.setStyleSheet("QGroupBox { color: #5973FE; font-size: 16px; }")
            volatility_layout = QVBoxLayout()
            
            # === 布林带设置 ===
            bb_group = QGroupBox("布林带指标设置")
            bb_layout = QGridLayout()
            bb_layout.setVerticalSpacing(10)
            
            # 布林带说明
            bb_desc = QLabel("布林带由中轨(移动平均线)和上下两条标准差带组成，用于衡量价格波动性和潜在的超买超卖水平。")
            bb_desc.setProperty("type", "description")
            bb_desc.setWordWrap(True)
            bb_layout.addWidget(bb_desc, 0, 0, 1, 2)
            
            # 布林带使用场景
            bb_usage = QLabel("使用场景：价格触及上轨可能表示超买，触及下轨可能表示超卖；带宽扩大表示波动性增加；带宽收窄表示波动性减小，可能预示大行情即将到来。")
            bb_usage.setProperty("type", "description")
            bb_usage.setWordWrap(True)
            bb_layout.addWidget(bb_usage, 1, 0, 1, 2)
            
            # 布林带周期
            self.bb_period_spinbox = QSpinBox()
            self.bb_period_spinbox.setRange(5, 50)
            self.bb_period_spinbox.setValue(getattr(self, 'bb_period', 20))
            bb_layout.addWidget(QLabel("布林带周期:"), 2, 0)
            bb_layout.addWidget(self.bb_period_spinbox, 2, 1)
            
            # 布林带标准差
            self.bb_std_spinbox = QDoubleSpinBox()
            self.bb_std_spinbox.setRange(1.0, 4.0)
            self.bb_std_spinbox.setSingleStep(0.1)
            self.bb_std_spinbox.setValue(getattr(self, 'bb_std', 2.0))
            bb_layout.addWidget(QLabel("标准差倍数:"), 3, 0)
            bb_layout.addWidget(self.bb_std_spinbox, 3, 1)
            
            bb_group.setLayout(bb_layout)
            volatility_layout.addWidget(bb_group)
            
            volatility_category.setLayout(volatility_layout)
            scroll_layout.addWidget(volatility_category)
            
            # 添加到滚动区域
            scroll_area.setWidget(scroll_content)
            layout.addWidget(scroll_area)
            
            # 添加按钮布局
            button_layout = QHBoxLayout()
            
            # 保存按钮
            save_btn = QPushButton("保存设置")
            save_btn.clicked.connect(lambda: self.save_indicator_settings(dialog))
            
            # 取消按钮
            cancel_btn = QPushButton("取消")
            cancel_btn.setProperty("type", "cancel")
            cancel_btn.clicked.connect(dialog.reject)
            
            # 重置按钮
            reset_btn = QPushButton("恢复默认")
            reset_btn.setProperty("type", "cancel")
            reset_btn.clicked.connect(self.reset_indicator_settings)
            
            # 添加按钮到布局
            button_layout.addWidget(reset_btn)
            button_layout.addStretch()
            button_layout.addWidget(cancel_btn)
            button_layout.addWidget(save_btn)
            
            layout.addLayout(button_layout)
            
            # 显示对话框
            dialog.exec()
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"显示指标设置对话框失败: {str(e)}", "error")
            import traceback
            traceback.print_exc()
    
    def load_indicator_settings(self):
        """从配置文件加载指标设置"""
        try:
            config_file = "indicator_settings.json"
            if os.path.exists(config_file):
                with open(config_file, "r") as f:
                    settings = json.load(f)
                
                # 加载RSI设置
                self.rsi_period = settings.get("rsi_period", 14)
                self.rsi_overbought = settings.get("rsi_overbought", 70)
                self.rsi_oversold = settings.get("rsi_oversold", 30)
                
                # 加载MACD设置
                self.macd_fast = settings.get("macd_fast", 12)
                self.macd_slow = settings.get("macd_slow", 26)
                self.macd_signal = settings.get("macd_signal", 9)
                
                # 加载移动平均线设置
                self.ema_short = settings.get("ema_short", 20)
                self.ema_medium = settings.get("ema_medium", 50)
                self.ema_long = settings.get("ema_long", 200)
                
                # 加载布林带设置
                self.bb_period = settings.get("bb_period", 20)
                self.bb_std = settings.get("bb_std", 2.0)
                
                # 加载其他指标设置
                self.adx_period = settings.get("adx_period", 14) # 新增ADX周期
                self.adx_threshold = settings.get("adx_threshold", 25)
                self.stoch_overbought = settings.get("stoch_overbought", 80)
                self.stoch_oversold = settings.get("stoch_oversold", 20)
                self.cci_overbought = settings.get("cci_overbought", 100)
                self.cci_oversold = settings.get("cci_oversold", -100)
                self.mfi_overbought = settings.get("mfi_overbought", 80)
                self.mfi_oversold = settings.get("mfi_oversold", 20)
                
                # 加载预设信息
                self.timeframe_preset = settings.get("timeframe_preset", "medium_term")
                self.market_type = settings.get("market_type", "普通市场")
                self.coin_type = settings.get("coin_type", "主流币")
                
                # 如果UI已初始化，更新下拉菜单选择
                if hasattr(self, 'market_type_combo'):
                    index = self.market_type_combo.findText(self.market_type)
                    if index >= 0:
                        self.market_type_combo.setCurrentIndex(index)
                        
                if hasattr(self, 'coin_type_combo'):
                    index = self.coin_type_combo.findText(self.coin_type)
                    if index >= 0:
                        self.coin_type_combo.setCurrentIndex(index)
                
                self.log_trading(f"已加载技术指标设置 (时段预设: {self.timeframe_preset}, 市场类型: {self.market_type}, 币种类型: {self.coin_type})", level='info')
            else:
                # 设置默认值
                self.reset_indicator_settings()
        except Exception as e:
            self.show_message_signal.emit("警告", f"加载技术指标设置失败，将使用默认值: {str(e)}", "warning")
            self.reset_indicator_settings()

    def init_adx_strategy(self):
        """初始化ADX交易策略"""
        try:
            # 从配置文件加载ADX策略参数
            config = {
                'adx_period': getattr(self, 'adx_period', 14),
                'adx_threshold': getattr(self, 'adx_threshold', 25.0),
                'confirmation_periods': 3,  # 连续确认期数
                'timeframe': '15m',  # 分析时间框架
                'risk_per_trade': 0.02,  # 每笔交易风险2%
                'max_position_size': 0.1   # 最大仓位10%
            }

            # 创建ADX策略实例
            self.adx_strategy = create_adx_strategy(config)

            # 初始化策略相关变量
            self.adx_strategy_enabled = False  # 默认关闭ADX策略
            self.adx_last_signal_time = None
            self.adx_signal_cooldown = 300  # 信号冷却时间5分钟

            self.log_trading("ADX交易策略初始化成功", level='info')

        except Exception as e:
            self.log_trading(f"ADX策略初始化失败: {str(e)}", level='error')
            self.adx_strategy = None

    def toggle_adx_strategy(self, state):
        """切换ADX策略状态"""
        try:
            if not hasattr(self, 'adx_strategy') or not self.adx_strategy:
                self.show_message_signal.emit("错误", "ADX策略未初始化", "error")
                self.adx_strategy_checkbox.setChecked(False)
                return

            self.adx_strategy_enabled = state == 2  # Qt.Checked = 2

            if self.adx_strategy_enabled:
                # 更新策略参数
                self.adx_strategy.update_parameters(
                    adx_period=self.adx_period_spinbox.value(),
                    adx_threshold=self.adx_threshold_spinbox.value()
                )

                # 更新状态指示器
                self.adx_strategy_status.setText("已启用")
                self.adx_strategy_status.setStyleSheet("""
                    color: #10B981;
                    font-size: 11px;
                    font-weight: 600;
                    background: rgba(16, 185, 129, 0.2);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    border-radius: 6px;
                    padding: 2px 8px;
                    min-width: 50px;
                """)

                self.log_trading("ADX策略已启用", level='info')

            else:
                # 更新状态指示器
                self.adx_strategy_status.setText("已禁用")
                self.adx_strategy_status.setStyleSheet("""
                    color: #6B7280;
                    font-size: 11px;
                    font-weight: 600;
                    background: rgba(107, 114, 128, 0.2);
                    border: 1px solid rgba(107, 114, 128, 0.3);
                    border-radius: 6px;
                    padding: 2px 8px;
                    min-width: 50px;
                """)

                self.log_trading("ADX策略已禁用", level='info')

        except Exception as e:
            self.log_trading(f"切换ADX策略状态失败: {str(e)}", level='error')
            self.adx_strategy_checkbox.setChecked(False)

    def save_indicator_settings(self, dialog):
        """保存指标设置到配置文件"""
        try:
            # 收集设置
            settings = {
                # RSI设置
                "rsi_period": self.rsi_period_spinbox.value(),
                "rsi_overbought": self.rsi_overbought_spinbox.value(),
                "rsi_oversold": self.rsi_oversold_spinbox.value(),
                
                # MACD设置
                "macd_fast": self.macd_fast_spinbox.value(),
                "macd_slow": self.macd_slow_spinbox.value(),
                "macd_signal": self.macd_signal_spinbox.value(),
                
                # 移动平均线设置
                "ema_short": self.ema_short_spinbox.value(),
                "ema_medium": self.ema_medium_spinbox.value(),
                "ema_long": self.ema_long_spinbox.value(),
                
                # 布林带设置
                "bb_period": self.bb_period_spinbox.value(),
                "bb_std": self.bb_std_spinbox.value(),
                
                # 其他指标设置
                "adx_period": self.adx_period_spinbox.value(), # 新增ADX周期
                "adx_threshold": self.adx_threshold_spinbox.value(),
                "stoch_overbought": self.stoch_overbought_spinbox.value(),
                "stoch_oversold": self.stoch_oversold_spinbox.value(),
                "cci_overbought": self.cci_overbought_spinbox.value(),
                "cci_oversold": self.cci_oversold_spinbox.value(),
                "mfi_overbought": self.mfi_overbought_spinbox.value(),
                "mfi_oversold": self.mfi_oversold_spinbox.value(),
                
                # 保存当前选择的预设
                "market_type": self.market_type_combo.currentText() if hasattr(self, 'market_type_combo') else "普通市场",
                "coin_type": self.coin_type_combo.currentText() if hasattr(self, 'coin_type_combo') else "主流币"
            }
            
            # 判断当前参数最接近哪个时段预设
            # 创建预设参数字典，用于比较
            presets = {
                "short_term": {
                    "rsi_period": 8,
                    "macd_fast": 6,
                    "ema_short": 10,
                    "bb_period": 10
                },
                "medium_term": {
                    "rsi_period": 14,
                    "macd_fast": 12,
                    "ema_short": 20,
                    "bb_period": 20
                },
                "long_term": {
                    "rsi_period": 21,
                    "macd_fast": 15,
                    "ema_short": 30,
                    "bb_period": 30
                }
            }
            
            # 计算当前设置与各预设的差异
            differences = {}
            current_values = {
                "rsi_period": self.rsi_period_spinbox.value(),
                "macd_fast": self.macd_fast_spinbox.value(),
                "ema_short": self.ema_short_spinbox.value(),
                "bb_period": self.bb_period_spinbox.value()
            }
            
            for preset_name, preset_values in presets.items():
                diff_sum = 0
                for key, value in preset_values.items():
                    diff_sum += abs(current_values[key] - value)
                differences[preset_name] = diff_sum
            
            # 找出差异最小的预设
            closest_preset = min(differences, key=differences.get)
            settings["timeframe_preset"] = closest_preset
            
            # 更新类属性
            for key, value in settings.items():
                setattr(self, key, value)
            
            # 保存到文件
            config_file = "indicator_settings.json"
            with open(config_file, "w") as f:
                json.dump(settings, f, indent=4)
            
            self.show_message_signal.emit("成功", "技术指标设置已保存", "info")
            dialog.accept()
        except Exception as e:
            self.show_message_signal.emit("错误", f"保存技术指标设置失败: {str(e)}", "error")
    
    def reset_indicator_settings(self):
        """重置为默认设置"""
        try:
            # RSI默认设置
            self.rsi_period = 14
            self.rsi_overbought = 70
            self.rsi_oversold = 30
            
            # MACD默认设置
            self.macd_fast = 12
            self.macd_slow = 26
            self.macd_signal = 9
            
            # 移动平均线默认设置
            self.ema_short = 20
            self.ema_medium = 50
            self.ema_long = 200
            
            # 布林带默认设置
            self.bb_period = 20
            self.bb_std = 2.0
            
            # 其他指标默认设置
            self.adx_period = 14 # 新增ADX周期
            self.adx_threshold = 25
            self.stoch_overbought = 80
            self.stoch_oversold = 20
            self.cci_overbought = 100
            self.cci_oversold = -100
            self.mfi_overbought = 80
            self.mfi_oversold = 20
            
            # 重置预设信息
            self.timeframe_preset = "medium_term"
            self.market_type = "普通市场"
            self.coin_type = "主流币"
            
            # 如果对话框已打开，更新控件值
            if hasattr(self, 'rsi_period_spinbox'):
                self.rsi_period_spinbox.setValue(self.rsi_period)
                self.rsi_overbought_spinbox.setValue(self.rsi_overbought)
                self.rsi_oversold_spinbox.setValue(self.rsi_oversold)
                
                self.macd_fast_spinbox.setValue(self.macd_fast)
                self.macd_slow_spinbox.setValue(self.macd_slow)
                self.macd_signal_spinbox.setValue(self.macd_signal)
                
                self.ema_short_spinbox.setValue(self.ema_short)
                self.ema_medium_spinbox.setValue(self.ema_medium)
                self.ema_long_spinbox.setValue(self.ema_long)
                
                self.bb_period_spinbox.setValue(self.bb_period)
                self.bb_std_spinbox.setValue(self.bb_std)
                
                self.adx_period_spinbox.setValue(self.adx_period) # 新增ADX周期
                self.adx_threshold_spinbox.setValue(self.adx_threshold)
                self.stoch_overbought_spinbox.setValue(self.stoch_overbought)
                self.stoch_oversold_spinbox.setValue(self.stoch_oversold)
                self.cci_overbought_spinbox.setValue(self.cci_overbought)
                self.cci_oversold_spinbox.setValue(self.cci_oversold)
                self.mfi_overbought_spinbox.setValue(self.mfi_overbought)
                self.mfi_oversold_spinbox.setValue(self.mfi_oversold)
                
                # 更新下拉选择框
                if hasattr(self, 'market_type_combo'):
                    index = self.market_type_combo.findText(self.market_type)
                    if index >= 0:
                        self.market_type_combo.setCurrentIndex(index)
                        
                if hasattr(self, 'coin_type_combo'):
                    index = self.coin_type_combo.findText(self.coin_type)
                    if index >= 0:
                        self.coin_type_combo.setCurrentIndex(index)
            
            self.show_message_signal.emit("提示", "已重置为默认技术指标设置", "info")
        except Exception as e:
            self.show_message_signal.emit("错误", f"重置技术指标设置失败: {str(e)}", "error")

    def refresh_chart_data(self):
        """刷新K线图数据"""
        try:
            # 清除缓存，强制重新获取数据
            symbol = self.symbol_combo.currentText()
            base_quote = symbol.split('/')
            base, quote = base_quote[0], base_quote[1]
            binance_symbol = f"{base}{quote}"
            
            timeframe = '15m'
            if hasattr(self.chart_widget, 'timeframe_combo'):
                selected_tf = self.chart_widget.timeframe_combo.currentText()
                if selected_tf == '5分钟':
                    timeframe = '5m'
                elif selected_tf == '15分钟':
                    timeframe = '15m'
                elif selected_tf == '1小时':
                    timeframe = '1h'
                elif selected_tf == '4小时':
                    timeframe = '4h'
                elif selected_tf == '1天':
                    timeframe = '1d'
            
            # 从缓存中移除该时间周期的数据，强制刷新
            cache_key = f"{binance_symbol}_{timeframe}"
            if cache_key in self.data_cache:
                del self.data_cache[cache_key]
            
            self.log_trading(f"刷新K线图数据: {binance_symbol}, 时间周期: {timeframe}", level='info')
            # 触发数据更新，使用现有的更新机制
            self.update_market_data()
        except Exception as e:
            self.log_trading(f"刷新K线图数据失败: {str(e)}", level='error')

    def apply_timeframe_preset(self, timeframe, dialog):
        """应用特定时段的预设参数"""
        try:
            # 短期交易设置 (5-15分钟)
            if timeframe == "short_term":
                # RSI设置
                self.rsi_period_spinbox.setValue(8)
                self.rsi_overbought_spinbox.setValue(75)
                self.rsi_oversold_spinbox.setValue(25)
                
                # MACD设置
                self.macd_fast_spinbox.setValue(6)
                self.macd_slow_spinbox.setValue(13)
                self.macd_signal_spinbox.setValue(5)
                
                # 移动平均线设置
                self.ema_short_spinbox.setValue(10)
                self.ema_medium_spinbox.setValue(25)
                self.ema_long_spinbox.setValue(100)
                
                # 布林带设置
                self.bb_period_spinbox.setValue(10)
                self.bb_std_spinbox.setValue(2.2)
                
                # 其他指标设置
                self.adx_threshold_spinbox.setValue(20)
                self.stoch_overbought_spinbox.setValue(85)
                self.stoch_oversold_spinbox.setValue(15)
                self.cci_overbought_spinbox.setValue(120)
                self.cci_oversold_spinbox.setValue(-120)
                self.mfi_overbought_spinbox.setValue(85)
                self.mfi_oversold_spinbox.setValue(15)
                
                self.show_message_signal.emit("提示", "已应用短期交易 (5-15分钟) 参数", "info")
            
            # 中期交易设置 (1-4小时)
            elif timeframe == "medium_term":
                # RSI设置
                self.rsi_period_spinbox.setValue(14)
                self.rsi_overbought_spinbox.setValue(70)
                self.rsi_oversold_spinbox.setValue(30)
                
                # MACD设置
                self.macd_fast_spinbox.setValue(12)
                self.macd_slow_spinbox.setValue(26)
                self.macd_signal_spinbox.setValue(9)
                
                # 移动平均线设置
                self.ema_short_spinbox.setValue(20)
                self.ema_medium_spinbox.setValue(50)
                self.ema_long_spinbox.setValue(200)
                
                # 布林带设置
                self.bb_period_spinbox.setValue(20)
                self.bb_std_spinbox.setValue(2.0)
                
                # 其他指标设置
                self.adx_threshold_spinbox.setValue(25)
                self.stoch_overbought_spinbox.setValue(80)
                self.stoch_oversold_spinbox.setValue(20)
                self.cci_overbought_spinbox.setValue(100)
                self.cci_oversold_spinbox.setValue(-100)
                self.mfi_overbought_spinbox.setValue(80)
                self.mfi_oversold_spinbox.setValue(20)
                
                self.show_message_signal.emit("提示", "已应用中期交易 (1-4小时) 参数", "info")
            
            # 长期交易设置 (日/周线)
            elif timeframe == "long_term":
                # RSI设置
                self.rsi_period_spinbox.setValue(21)
                self.rsi_overbought_spinbox.setValue(65)
                self.rsi_oversold_spinbox.setValue(35)
                
                # MACD设置
                self.macd_fast_spinbox.setValue(15)
                self.macd_slow_spinbox.setValue(30)
                self.macd_signal_spinbox.setValue(12)
                
                # 移动平均线设置
                self.ema_short_spinbox.setValue(30)
                self.ema_medium_spinbox.setValue(60)
                self.ema_long_spinbox.setValue(200)
                
                # 布林带设置
                self.bb_period_spinbox.setValue(30)
                self.bb_std_spinbox.setValue(1.8)
                
                # 其他指标设置
                self.adx_threshold_spinbox.setValue(30)
                self.stoch_overbought_spinbox.setValue(75)
                self.stoch_oversold_spinbox.setValue(25)
                self.cci_overbought_spinbox.setValue(80)
                self.cci_oversold_spinbox.setValue(-80)
                self.mfi_overbought_spinbox.setValue(75)
                self.mfi_oversold_spinbox.setValue(25)
                
                self.show_message_signal.emit("提示", "已应用长期交易 (日/周) 参数", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"应用时段预设失败: {str(e)}", "error")

    def apply_market_type_preset(self, dialog):
        """应用市场类型预设"""
        try:
            market_type = self.market_type_combo.currentText()
            
            # 震荡市场参数
            if market_type == "震荡市场":
                # 缩小RSI区间
                self.rsi_overbought_spinbox.setValue(65)
                self.rsi_oversold_spinbox.setValue(35)
                
                # 缩短布林带周期
                self.bb_period_spinbox.setValue(15)
                
                # 调整随机指标
                self.stoch_overbought_spinbox.setValue(75)
                self.stoch_oversold_spinbox.setValue(25)
                
                self.show_message_signal.emit("提示", "已应用震荡市场参数", "info")
                
            # 强趋势市场参数
            elif market_type == "强趋势市场":
                # 放宽RSI区间
                self.rsi_overbought_spinbox.setValue(80)
                self.rsi_oversold_spinbox.setValue(20)
                
                # 增加布林带标准差
                self.bb_std_spinbox.setValue(2.5)
                
                # 调低ADX阈值
                self.adx_threshold_spinbox.setValue(20)
                
                self.show_message_signal.emit("提示", "已应用强趋势市场参数", "info")
                
            # 普通市场 - 使用默认参数
            else:
                # 可以选择不做任何改变，或者加载默认参数
                pass
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"应用市场类型预设失败: {str(e)}", "error")

    def apply_coin_type_preset(self, dialog):
        """应用币种类型预设"""
        try:
            coin_type = self.coin_type_combo.currentText()
            
            # 主流币参数 (BTC/ETH等)
            if coin_type == "主流币":
                # MACD设置 - 适合稳定趋势
                self.macd_fast_spinbox.setValue(10)
                self.macd_slow_spinbox.setValue(21)
                self.macd_signal_spinbox.setValue(8)
                
                # ADX阈值降低 - 主流币趋势更容易形成
                self.adx_threshold_spinbox.setValue(20)
                
                self.show_message_signal.emit("提示", "已应用主流币参数", "info")
                
            # 山寨币参数
            elif coin_type == "山寨币":
                # 默认参数适用
                
                self.show_message_signal.emit("提示", "已应用山寨币参数", "info")
                
            # 高波动币参数 (迷因币等)
            elif coin_type == "高波动币":
                # RSI设置 - 更严格的超买超卖条件
                self.rsi_overbought_spinbox.setValue(75)
                self.rsi_oversold_spinbox.setValue(25)
                
                # 布林带标准差增加 - 适应更大波动
                self.bb_std_spinbox.setValue(2.5)
                
                # 随机指标范围扩大 - 适应更大波动
                self.stoch_overbought_spinbox.setValue(85)
                self.stoch_oversold_spinbox.setValue(15)
                
                # CCI范围扩大
                self.cci_overbought_spinbox.setValue(150)
                self.cci_oversold_spinbox.setValue(-150)
                
                # MFI范围扩大
                self.mfi_overbought_spinbox.setValue(85)
                self.mfi_oversold_spinbox.setValue(15)
                
                self.show_message_signal.emit("提示", "已应用高波动币参数", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"应用币种类型预设失败: {str(e)}", "error")

    def check_network_status(self):
        """检查网络状态"""
        if not hasattr(self, 'network_thread') or not self.network_thread.is_alive():
            self.network_thread = threading.Thread(target=self._check_network_status_thread, daemon=True)
            self.network_thread.start()

    def _check_network_status_thread(self):
        """在后台线程中检查网络状态"""
        check_results = {}
        
        # 检查交易所API连接
        try:
            for attempt in range(3):  # 最多尝试3次
                try:
                    status = self.exchange.fetch_status()
                    if status.get('status') == 'ok':
                        check_results["exchange"] = True
                        break
                    else:
                        time.sleep(1)
                except Exception:
                    if attempt < 2:  # 如果不是最后一次尝试
                        time.sleep(1)
                    else:
                        check_results["exchange"] = False
            
            if "exchange" not in check_results:
                check_results["exchange"] = False
                
        except Exception:
            check_results["exchange"] = False
            
        # 检查News API连接
        if self.news_api_key_value:
            try:
                for attempt in range(3):  # 最多尝试3次
                    try:
                        url = 'https://newsapi.org/v2/everything'
                        params = {
                            'q': 'bitcoin',
                            'pageSize': 1,
                            'apiKey': self.news_api_key_value
                        }
                        response = requests.get(url, params=params, timeout=(10, 30))  # (连接超时, 读取超时)
                        if response.status_code == 200:
                            check_results["news_api"] = True
                            break
                        else:
                            time.sleep(1)
                    except Exception:
                        if attempt < 2:  # 如果不是最后一次尝试
                            time.sleep(1)
                        else:
                            check_results["news_api"] = False
                
                if "news_api" not in check_results:
                    check_results["news_api"] = False
            except Exception:
                check_results["news_api"] = False
        else:
            check_results["news_api"] = False
            
        # 检查DeepSeek AI API连接
        if self.deepseek_api_key_value:
            try:
                for attempt in range(3):  # 最多尝试3次
                    try:
                        headers = {
                            'Authorization': f'Bearer {self.deepseek_api_key_value}',
                            'Content-Type': 'application/json'
                        }
                        api_url = "https://api.deepseek.com/v1/chat/completions"
                        api_data = {
                            "model": "deepseek-chat",
                            "messages": [{"role": "user", "content": "Hi"}],
                            "max_tokens": 5
                        }
                        response = requests.post(api_url, json=api_data, headers=headers, timeout=(10, 30))  # (连接超时, 读取超时)
                        if response.status_code == 200:
                            check_results["ai_api"] = True
                            break
                        else:
                            time.sleep(1)
                    except Exception:
                        if attempt < 2:  # 如果不是最后一次尝试
                            time.sleep(1)
                        else:
                            check_results["ai_api"] = False
                
                if "ai_api" not in check_results:
                    check_results["ai_api"] = False
            except Exception:
                check_results["ai_api"] = False
        else:
            check_results["ai_api"] = False
        
        # 更新所有状态
        for api_name, is_connected in check_results.items():
            self.network_status_signal.emit(api_name, is_connected)
            

    def update_network_status(self, api_name, is_connected):
        """更新网络状态指示器"""
        if api_name == "exchange":
            status_text = "交易所连接: "
            status_label = self.exchange_status
        elif api_name == "news_api":
            status_text = "News API: "
            status_label = self.news_api_status
        elif api_name == "ai_api":
            status_text = "AI API: "
            status_label = self.ai_api_status
        else:
            return
            
        if is_connected:
            status_text += "已连接"
            status_label.setStyleSheet("color: #2EBD85;")  # 绿色表示连接成功
        else:
            status_text += "未连接"
            status_label.setStyleSheet("color: #F23645;")  # 红色表示连接失败
            
        status_label.setText(status_text)
        
        # 添加时间戳到状态栏提示
        timestamp = datetime.now().strftime('%H:%M:%S')
        status_label.setToolTip(f"上次检查: {timestamp}")



    def update_ai_symbol_status(self, symbol):
        """更新AI交易标签页中的交易对状态"""
        try:
            symbol_info = self.get_symbol_info(symbol)
            symbol_type = symbol_info['type']
            type_color = symbol_info['type_color']
            type_rgba = symbol_info['type_rgba']

            self.ai_symbol_status.setText(symbol_type)
            self.ai_symbol_status.setStyleSheet(f"""
                color: {type_color};
                font-size: 9px;
                font-weight: 600;
                background: rgba({type_rgba}, 0.2);
                border: 1px solid rgba({type_rgba}, 0.3);
                border-radius: 6px;
                padding: 1px 4px;
            """)
        except Exception as e:
            self.log_trading(f"更新AI交易对状态失败: {str(e)}", level='error')





    def update_adx_display(self):
        """更新ADX实时显示（优化版本，使用缓存）"""
        try:
            # 获取当前选择的交易对
            symbol_text = self.symbol_combo.currentText()
            if not symbol_text:
                return

            # 提取交易对符号
            symbol = symbol_text.split(' ')[-1].replace('/', '')  # 例如: "BTC/USDT" -> "BTCUSDT"

            # 检查缓存
            cache_key = f"adx_{symbol}_15m"
            current_time = datetime.now()

            # 如果缓存存在且未过期（30秒内），直接使用缓存
            if cache_key in self.adx_cache:
                cache_data = self.adx_cache[cache_key]
                if (current_time - cache_data['timestamp']).total_seconds() < 30:
                    self.current_adx = cache_data['adx']
                    self.current_plus_di = cache_data['plus_di']
                    self.current_minus_di = cache_data['minus_di']
                    self.adx_last_update = cache_data['timestamp']
                    self.update_adx_ui()
                    return

            # 获取15分钟K线数据
            klines = self.exchange.fetch_ohlcv(symbol, '15m', limit=50)
            if not klines or len(klines) < 30:
                self.log_trading("ADX更新: K线数据不足", level='warning')
                return

            # 计算ADX（使用优化的方法）
            adx_data = self.calculate_adx_optimized(klines)
            if adx_data:
                self.current_adx = adx_data['adx']
                self.current_plus_di = adx_data['plus_di']
                self.current_minus_di = adx_data['minus_di']
                self.adx_last_update = current_time

                # 更新缓存
                self.adx_cache[cache_key] = {
                    'adx': self.current_adx,
                    'plus_di': self.current_plus_di,
                    'minus_di': self.current_minus_di,
                    'timestamp': current_time
                }

                # 清理过期缓存
                self.cleanup_adx_cache()

                # 更新显示
                self.update_adx_ui()
            else:
                self.log_trading("ADX计算结果无效", level='warning')

        except Exception as e:
            self.log_trading(f"更新ADX显示失败: {str(e)}", level='error')

    def calculate_adx_optimized(self, klines):
        """优化的ADX计算方法"""
        try:
            # 转换为numpy数组
            import numpy as np
            high_prices = np.array([float(k[2]) for k in klines])
            low_prices = np.array([float(k[3]) for k in klines])
            close_prices = np.array([float(k[4]) for k in klines])

            # 计算ADX指标
            adx_period = getattr(self, 'adx_period', 14)
            adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=adx_period)
            plus_di = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)
            minus_di = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=adx_period)

            # 获取最新值
            if len(adx) > 0 and not np.isnan(adx[-1]):
                return {
                    'adx': round(float(adx[-1]), 2),
                    'plus_di': round(float(plus_di[-1]), 2),
                    'minus_di': round(float(minus_di[-1]), 2)
                }
            return None

        except Exception as e:
            self.log_trading(f"ADX计算失败: {str(e)}", level='error')
            return None

    def cleanup_adx_cache(self):
        """清理过期的ADX缓存"""
        try:
            current_time = datetime.now()
            expired_keys = []

            for key, data in self.adx_cache.items():
                # 清理超过5分钟的缓存
                if (current_time - data['timestamp']).total_seconds() > 300:
                    expired_keys.append(key)

            for key in expired_keys:
                del self.adx_cache[key]

        except Exception as e:
            self.log_trading(f"清理ADX缓存失败: {str(e)}", level='error')

    def update_adx_ui(self):
        """更新ADX用户界面显示"""
        try:
            if self.current_adx is None:
                return

            # 更新ADX主值显示
            self.adx_value_display.setText(f"{self.current_adx:.1f}")

            # 更新+DI和-DI显示
            self.plus_di_display.setText(f"{self.current_plus_di:.1f}")
            self.minus_di_display.setText(f"{self.current_minus_di:.1f}")

            # 更新趋势状态
            adx_threshold = getattr(self, 'adx_threshold', 25)
            if self.current_adx >= adx_threshold:
                if self.current_plus_di > self.current_minus_di:
                    trend_status = "🚀 强势上涨"
                    trend_color = "#10B981"
                    trend_rgba = "16, 185, 129"
                elif self.current_minus_di > self.current_plus_di:
                    trend_status = "📉 强势下跌"
                    trend_color = "#EF4444"
                    trend_rgba = "239, 68, 68"
                else:
                    trend_status = "⚡ 强势震荡"
                    trend_color = "#F59E0B"
                    trend_rgba = "245, 158, 11"

                strength_text = "强趋势"
                strength_color = "#10B981"
            else:
                trend_status = "📊 弱势震荡"
                trend_color = "#94A3B8"
                trend_rgba = "148, 163, 184"
                strength_text = "弱趋势"
                strength_color = "#94A3B8"

            # 更新趋势状态标签
            self.adx_trend_status_label.setText(trend_status)
            self.adx_trend_status_label.setStyleSheet(f"""
                color: {trend_color};
                font-size: 11px;
                font-weight: 700;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba({trend_rgba}, 0.25), stop:1 rgba({trend_rgba}, 0.15));
                border: 2px solid rgba({trend_rgba}, 0.4);
                border-radius: 10px;
                padding: 4px 8px;
                min-width: 60px;
            """)

            # 更新趋势强度标签
            self.adx_strength_label.setText(strength_text)
            if strength_color == "#10B981":
                strength_rgba = "16, 185, 129"
            else:
                strength_rgba = "148, 163, 184"

            self.adx_strength_label.setStyleSheet(f"""
                color: {strength_color};
                font-size: 11px;
                font-weight: 600;
                background: rgba({strength_rgba}, 0.1);
                border-radius: 6px;
                padding: 2px 8px;
                min-width: 45px;
            """)

            # 更新ADX进度条
            if hasattr(self, 'adx_progress_container') and hasattr(self, 'adx_progress_fill'):
                container_width = self.adx_progress_container.width()
                if container_width > 0:
                    progress_width = min(int((self.current_adx / 100) * container_width), container_width)
                    self.adx_progress_fill.setFixedWidth(progress_width)

            # 更新阈值标签
            self.adx_threshold_label.setText(f"阈值: {adx_threshold}")

            # 更新最后更新时间
            if self.adx_last_update:
                time_str = self.adx_last_update.strftime("%H:%M:%S")
                self.adx_last_update_label.setText(time_str)
                self.adx_last_update_label.setStyleSheet("""
                    color: #10B981;
                    font-size: 10px;
                    font-weight: 500;
                """)

        except Exception as e:
            self.log_trading(f"更新ADX UI失败: {str(e)}", level='error')

    def update_adx_from_ohlcv(self, ohlcv_data):
        """从OHLCV数据更新ADX显示（优化版本，使用缓存）"""
        try:
            if not ohlcv_data or len(ohlcv_data) < 30:
                return

            # 获取当前交易对
            symbol_text = self.symbol_combo.currentText()
            if not symbol_text:
                return
            symbol = symbol_text.split(' ')[-1].replace('/', '')

            # 生成数据哈希用于缓存键
            import hashlib
            data_hash = hashlib.md5(str(ohlcv_data[-5:]).encode()).hexdigest()[:8]  # 使用最后5个数据点的哈希
            cache_key = f"adx_ohlcv_{symbol}_{data_hash}"
            current_time = datetime.now()

            # 检查缓存
            if cache_key in self.adx_cache:
                cache_data = self.adx_cache[cache_key]
                if (current_time - cache_data['timestamp']).total_seconds() < 60:  # 1分钟缓存
                    self.current_adx = cache_data['adx']
                    self.current_plus_di = cache_data['plus_di']
                    self.current_minus_di = cache_data['minus_di']
                    self.adx_last_update = cache_data['timestamp']
                    self.update_adx_ui()
                    self.check_adx_signals()
                    return

            # 计算ADX（使用优化的方法）
            adx_data = self.calculate_adx_optimized(ohlcv_data)
            if adx_data:
                self.current_adx = adx_data['adx']
                self.current_plus_di = adx_data['plus_di']
                self.current_minus_di = adx_data['minus_di']
                self.adx_last_update = current_time

                # 更新缓存
                self.adx_cache[cache_key] = {
                    'adx': self.current_adx,
                    'plus_di': self.current_plus_di,
                    'minus_di': self.current_minus_di,
                    'timestamp': current_time
                }

                # 更新显示
                self.update_adx_ui()

                # 检查ADX信号
                self.check_adx_signals()

        except Exception as e:
            self.log_trading(f"从OHLCV数据更新ADX失败: {str(e)}", level='error')

    def check_adx_signals(self):
        """检查ADX交易信号（增强版本）"""
        try:
            if self.current_adx is None:
                return

            adx_threshold = getattr(self, 'adx_threshold', 25)
            adx_strong_threshold = getattr(self, 'adx_strong_threshold', 40)

            # 初始化历史数据
            if not hasattr(self, 'adx_history'):
                self.adx_history = []
                self.plus_di_history = []
                self.minus_di_history = []

            # 添加当前值到历史
            self.adx_history.append(self.current_adx)
            self.plus_di_history.append(self.current_plus_di)
            self.minus_di_history.append(self.current_minus_di)

            # 保持历史数据长度不超过10
            if len(self.adx_history) > 10:
                self.adx_history.pop(0)
                self.plus_di_history.pop(0)
                self.minus_di_history.pop(0)

            # 需要至少2个数据点才能检测信号
            if len(self.adx_history) < 2:
                return

            previous_adx = self.adx_history[-2]
            previous_plus_di = self.plus_di_history[-2]
            previous_minus_di = self.minus_di_history[-2]

            # 1. ADX突破阈值信号
            if previous_adx < adx_threshold and self.current_adx >= adx_threshold:
                if self.current_plus_di > self.current_minus_di:
                    signal_msg = f"🚀 ADX突破{adx_threshold}，强势上涨趋势开始！ADX: {self.current_adx:.1f}, +DI: {self.current_plus_di:.1f}"
                    self.log_trading(signal_msg, level='info')
                    self.show_adx_notification("上涨趋势信号", signal_msg)
                else:
                    signal_msg = f"📉 ADX突破{adx_threshold}，强势下跌趋势开始！ADX: {self.current_adx:.1f}, -DI: {self.current_minus_di:.1f}"
                    self.log_trading(signal_msg, level='info')
                    self.show_adx_notification("下跌趋势信号", signal_msg)

            # 2. ADX跌破阈值信号
            elif previous_adx >= adx_threshold and self.current_adx < adx_threshold:
                signal_msg = f"📊 ADX跌破{adx_threshold}，趋势结束，进入震荡！ADX: {self.current_adx:.1f}"
                self.log_trading(signal_msg, level='info')
                self.show_adx_notification("震荡信号", signal_msg)

            # 3. ADX极强信号（突破40）
            if previous_adx < adx_strong_threshold and self.current_adx >= adx_strong_threshold:
                signal_msg = f"⚡ ADX突破{adx_strong_threshold}，极强趋势！ADX: {self.current_adx:.1f}"
                self.log_trading(signal_msg, level='info')
                self.show_adx_notification("极强趋势信号", signal_msg)

            # 4. DI交叉信号
            if (previous_plus_di <= previous_minus_di and
                self.current_plus_di > self.current_minus_di and
                self.current_adx >= adx_threshold):
                signal_msg = f"🔄 +DI上穿-DI，强势买入信号！+DI: {self.current_plus_di:.1f}, -DI: {self.current_minus_di:.1f}"
                self.log_trading(signal_msg, level='info')
                self.show_adx_notification("买入信号", signal_msg)

            elif (previous_plus_di >= previous_minus_di and
                  self.current_plus_di < self.current_minus_di and
                  self.current_adx >= adx_threshold):
                signal_msg = f"🔄 -DI上穿+DI，强势卖出信号！+DI: {self.current_plus_di:.1f}, -DI: {self.current_minus_di:.1f}"
                self.log_trading(signal_msg, level='info')
                self.show_adx_notification("卖出信号", signal_msg)

            # 5. ADX持续上升信号（连续3次上升）
            if len(self.adx_history) >= 3:
                if (self.adx_history[-3] < self.adx_history[-2] < self.current_adx and
                    self.current_adx >= adx_threshold):
                    signal_msg = f"📈 ADX持续上升，趋势加强！ADX: {self.current_adx:.1f}"
                    self.log_trading(signal_msg, level='debug')

        except Exception as e:
            self.log_trading(f"检查ADX信号失败: {str(e)}", level='error')

    def show_adx_notification(self, title, message):
        """显示ADX信号通知"""
        try:
            # 更新ADX趋势状态标签以显示最新信号
            current_time = datetime.now().strftime("%H:%M:%S")
            notification_text = f"{title} ({current_time})"

            # 根据信号类型设置不同颜色
            if "上涨" in title or "买入" in title:
                color = "#10B981"
                rgba = "16, 185, 129"
            elif "下跌" in title or "卖出" in title:
                color = "#EF4444"
                rgba = "239, 68, 68"
            elif "极强" in title:
                color = "#8B5CF6"
                rgba = "139, 92, 246"
            else:
                color = "#F59E0B"
                rgba = "245, 158, 11"

            # 临时更新状态标签显示信号
            original_text = self.adx_trend_status_label.text()
            self.adx_trend_status_label.setText(notification_text)
            self.adx_trend_status_label.setStyleSheet(f"""
                color: {color};
                font-size: 11px;
                font-weight: 700;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba({rgba}, 0.35), stop:1 rgba({rgba}, 0.25));
                border: 2px solid rgba({rgba}, 0.6);
                border-radius: 10px;
                padding: 4px 8px;
                min-width: 60px;
            """)

            # 3秒后恢复原始状态
            QTimer.singleShot(3000, lambda: self.restore_adx_status_label(original_text))

        except Exception as e:
            self.log_trading(f"显示ADX通知失败: {str(e)}", level='error')

    def restore_adx_status_label(self, original_text):
        """恢复ADX状态标签"""
        try:
            self.adx_trend_status_label.setText(original_text)
            # 恢复原始样式
            self.update_adx_ui()
        except Exception as e:
            self.log_trading(f"恢复ADX状态标签失败: {str(e)}", level='error')

    def set_adx_parameters(self, period=14, threshold=25, strong_threshold=40):
        """设置ADX参数"""
        try:
            self.adx_period = period
            self.adx_threshold = threshold
            self.adx_strong_threshold = strong_threshold

            # 更新阈值标签
            if hasattr(self, 'adx_threshold_label'):
                self.adx_threshold_label.setText(f"阈值: {threshold}")

            self.log_trading(f"ADX参数已更新: 周期={period}, 阈值={threshold}, 强阈值={strong_threshold}", level='info')

        except Exception as e:
            self.log_trading(f"设置ADX参数失败: {str(e)}", level='error')

    def get_adx_status(self):
        """获取当前ADX状态"""
        try:
            if self.current_adx is None:
                return {
                    'status': 'no_data',
                    'adx': None,
                    'plus_di': None,
                    'minus_di': None,
                    'trend': 'unknown',
                    'strength': 'unknown'
                }

            # 判断趋势方向
            if self.current_plus_di > self.current_minus_di:
                trend_direction = 'bullish'
            elif self.current_minus_di > self.current_plus_di:
                trend_direction = 'bearish'
            else:
                trend_direction = 'neutral'

            # 判断趋势强度
            if self.current_adx >= self.adx_strong_threshold:
                strength = 'very_strong'
            elif self.current_adx >= self.adx_threshold:
                strength = 'strong'
            else:
                strength = 'weak'

            return {
                'status': 'active',
                'adx': self.current_adx,
                'plus_di': self.current_plus_di,
                'minus_di': self.current_minus_di,
                'trend': trend_direction,
                'strength': strength,
                'last_update': self.adx_last_update
            }

        except Exception as e:
            self.log_trading(f"获取ADX状态失败: {str(e)}", level='error')
            return {'status': 'error', 'error': str(e)}

    def update_ai_status_display(self, status, progress=0, confidence=0):
        """更新AI状态显示"""
        try:
            # 更新AI状态指示器
            status_configs = {
                'analyzing': {
                    'text': '🔍 分析中',
                    'color': '#3B82F6',
                    'rgba': '59, 130, 246'
                },
                'ready': {
                    'text': '✅ 就绪',
                    'color': '#10B981',
                    'rgba': '16, 185, 129'
                },
                'trading': {
                    'text': '🚀 交易中',
                    'color': '#F59E0B',
                    'rgba': '245, 158, 11'
                },
                'waiting': {
                    'text': '⏳ 待机中',
                    'color': '#94A3B8',
                    'rgba': '148, 163, 184'
                },
                'error': {
                    'text': '❌ 错误',
                    'color': '#EF4444',
                    'rgba': '239, 68, 68'
                }
            }

            config = status_configs.get(status, status_configs['waiting'])

            if hasattr(self, 'ai_status_indicator'):
                self.ai_status_indicator.setText(config['text'])
                self.ai_status_indicator.setStyleSheet(f"""
                    color: {config['color']};
                    font-size: 11px;
                    font-weight: 600;
                    background: rgba({config['rgba']}, 0.2);
                    border: 1px solid rgba({config['rgba']}, 0.3);
                    border-radius: 8px;
                    padding: 3px 8px;
                    min-width: 60px;
                """)

            # 更新进度条
            if hasattr(self, 'ai_progress_container') and hasattr(self, 'ai_progress_fill'):
                container_width = self.ai_progress_container.width()
                if container_width > 0:
                    fill_width = int(container_width * progress / 100)
                    self.ai_progress_fill.setFixedWidth(fill_width)

            # 更新信心度
            if hasattr(self, 'ai_confidence_label'):
                confidence_color = "#10B981" if confidence >= 70 else "#F59E0B" if confidence >= 40 else "#EF4444"
                confidence_rgba = "16, 185, 129" if confidence >= 70 else "245, 158, 11" if confidence >= 40 else "239, 68, 68"

                self.ai_confidence_label.setText(f"{confidence}%")
                self.ai_confidence_label.setStyleSheet(f"""
                    color: {confidence_color};
                    font-size: 11px;
                    font-weight: 600;
                    background: rgba({confidence_rgba}, 0.1);
                    border-radius: 6px;
                    padding: 2px 8px;
                    min-width: 35px;
                """)

        except Exception as e:
            self.log_trading(f"更新AI状态显示失败: {str(e)}", level='error')

    def update_ai_price_display(self, current_price, change_percent=0, trend='neutral'):
        """更新AI交易价格显示"""
        try:
            # 更新当前价格
            if hasattr(self, 'ai_current_price_label'):
                self.ai_current_price_label.setText(f"${current_price:,.2f}")

            # 更新价格变化
            if hasattr(self, 'ai_price_change_label'):
                change_symbol = "+" if change_percent >= 0 else ""
                change_color = "#10B981" if change_percent >= 0 else "#EF4444"
                change_rgba = "16, 185, 129" if change_percent >= 0 else "239, 68, 68"

                self.ai_price_change_label.setText(f"{change_symbol}{change_percent:.2f}%")
                self.ai_price_change_label.setStyleSheet(f"""
                    color: {change_color};
                    font-size: 12px;
                    font-weight: 600;
                    background: rgba({change_rgba}, 0.1);
                    border-radius: 6px;
                    padding: 2px 8px;
                """)

            # 更新趋势指示器
            if hasattr(self, 'ai_trend_indicator'):
                trend_configs = {
                    'up': {'symbol': '▲', 'color': '#10B981'},
                    'down': {'symbol': '▼', 'color': '#EF4444'},
                    'neutral': {'symbol': '●', 'color': '#94A3B8'}
                }

                trend_config = trend_configs.get(trend, trend_configs['neutral'])
                self.ai_trend_indicator.setText(trend_config['symbol'])
                self.ai_trend_indicator.setStyleSheet(f"color: {trend_config['color']}; font-size: 20px; font-weight: 700;")

        except Exception as e:
            self.log_trading(f"更新AI价格显示失败: {str(e)}", level='error')

    def cleanup_memory(self):
        """定期清理内存，优化性能"""
        try:
            # 清理过期的缓存数据
            current_time = time.time()
            expired_keys = []

            # 检查市场数据缓存
            for key, cached_data in self._market_data_cache.items():
                if hasattr(cached_data, 'timestamp') and cached_data.timestamp:
                    age = (datetime.now() - cached_data.timestamp).seconds
                    if age > 600:  # 10分钟过期
                        expired_keys.append(key)

            # 清理过期缓存
            for key in expired_keys:
                del self._market_data_cache[key]

            # 清理数据缓存
            expired_data_keys = []
            for key, cached_data in self.data_cache.items():
                if not cached_data.is_valid(300):  # 5分钟过期
                    expired_data_keys.append(key)

            for key in expired_data_keys:
                del self.data_cache[key]

            # 强制垃圾回收
            gc.collect()

            if expired_keys or expired_data_keys:
                self.log_trading(f"内存清理完成，清理了 {len(expired_keys + expired_data_keys)} 个过期缓存项", level='debug')

        except Exception as e:
            self.log_trading(f"内存清理失败: {str(e)}", level='warning')

    def update_trading_mode_indicator(self, is_auto_mode):
        """更新交易模式指示器"""
        try:
            if hasattr(self, 'trading_mode_indicator'):
                if is_auto_mode:
                    mode_text = "自动模式"
                    mode_color = "#10B981"
                    mode_rgba = "16, 185, 129"
                else:
                    mode_text = "手动模式"
                    mode_color = "#94A3B8"
                    mode_rgba = "148, 163, 184"

                self.trading_mode_indicator.setText(mode_text)
                self.trading_mode_indicator.setStyleSheet(f"""
                    color: {mode_color};
                    font-size: 10px;
                    font-weight: 600;
                    background: rgba({mode_rgba}, 0.2);
                    border: 1px solid rgba({mode_rgba}, 0.3);
                    border-radius: 8px;
                    padding: 2px 6px;
                    min-width: 50px;
                """)

            if hasattr(self, 'auto_trading_status_indicator'):
                if is_auto_mode:
                    status_text = "运行中"
                    status_color = "#10B981"
                    status_rgba = "16, 185, 129"
                else:
                    status_text = "已停止"
                    status_color = "#EF4444"
                    status_rgba = "239, 68, 68"

                self.auto_trading_status_indicator.setText(status_text)
                self.auto_trading_status_indicator.setStyleSheet(f"""
                    color: {status_color};
                    font-size: 11px;
                    font-weight: 600;
                    background: rgba({status_rgba}, 0.2);
                    border: 1px solid rgba({status_rgba}, 0.3);
                    border-radius: 6px;
                    padding: 2px 8px;
                    min-width: 50px;
                """)

        except Exception as e:
            self.log_trading(f"更新交易模式指示器失败: {str(e)}", level='error')

    def filter_logs(self, filter_type):
        """过滤日志显示"""
        try:
            if hasattr(self.trading_log, 'filter_logs'):
                self.trading_log.filter_logs(filter_type)
        except Exception as e:
            self.log_trading(f"过滤日志失败: {str(e)}", level='error')

    def toggle_auto_scroll(self, enabled):
        """切换自动滚动"""
        try:
            self.auto_scroll_enabled = enabled
            if hasattr(self.trading_log, 'set_auto_scroll'):
                self.trading_log.set_auto_scroll(enabled)

            # 更新按钮样式
            if enabled:
                self.auto_scroll_button.setStyleSheet("""
                    QPushButton {
                        font-size: 10px;
                        font-weight: 600;
                        background: rgba(16, 185, 129, 0.3);
                        border: 1px solid rgba(16, 185, 129, 0.5);
                        border-radius: 6px;
                        color: #10B981;
                        padding: 2px 6px;
                    }
                    QPushButton:hover {
                        background: rgba(16, 185, 129, 0.4);
                    }
                """)
            else:
                self.auto_scroll_button.setStyleSheet("""
                    QPushButton {
                        font-size: 10px;
                        font-weight: 600;
                        background: rgba(148, 163, 184, 0.2);
                        border: 1px solid rgba(148, 163, 184, 0.3);
                        border-radius: 6px;
                        color: #94A3B8;
                        padding: 2px 6px;
                    }
                    QPushButton:hover {
                        background: rgba(148, 163, 184, 0.3);
                    }
                """)
        except Exception as e:
            self.log_trading(f"切换自动滚动失败: {str(e)}", level='error')

    def update_log_stats(self, total, info, warning, error):
        """更新日志统计信息"""
        try:
            # 更新计数器标签
            self.log_counter_label.setText(f"{total} 条")

            # 更新统计标签
            self.log_stats_label.setText(f"总计: {total} | 信息: {info} | 警告: {warning} | 错误: {error}")

            # 根据错误数量更新状态指示器
            if error > 0:
                status_text = "⚠️ 有错误"
                status_color = "#EF4444"
                status_rgba = "239, 68, 68"
            elif warning > 0:
                status_text = "⚠️ 有警告"
                status_color = "#F59E0B"
                status_rgba = "245, 158, 11"
            else:
                status_text = "📝 记录中"
                status_color = "#10B981"
                status_rgba = "16, 185, 129"

            self.log_status_indicator.setText(status_text)
            self.log_status_indicator.setStyleSheet(f"""
                color: {status_color};
                font-size: 10px;
                font-weight: 600;
                background: rgba({status_rgba}, 0.2);
                border: 1px solid rgba({status_rgba}, 0.3);
                border-radius: 8px;
                padding: 2px 6px;
                min-width: 50px;
            """)
        except Exception as e:
            print(f"更新日志统计失败: {str(e)}")

    def on_log_added(self, level, message):
        """日志添加时的回调"""
        try:
            # 可以在这里添加额外的处理逻辑
            # 比如声音提示、桌面通知等
            pass
        except Exception as e:
            print(f"日志添加回调失败: {str(e)}")

    def clear_trading_log(self):
        """清空交易日志"""
        try:
            if hasattr(self.trading_log, 'clear'):
                self.trading_log.clear()
                # 重置统计
                if hasattr(self.trading_log, 'log_counts'):
                    self.trading_log.log_counts = {
                        'total': 0,
                        'info': 0,
                        'warning': 0,
                        'error': 0
                    }
                    self.trading_log.all_logs = []
                    self.update_log_stats(0, 0, 0, 0)

                self.log_trading("日志已清空", level='info')
        except Exception as e:
            self.log_trading(f"清空日志失败: {str(e)}", level='error')

    def export_trading_log(self):
        """导出交易日志"""
        try:
            if hasattr(self.trading_log, 'export_logs'):
                # 导出为文本格式
                text_content = self.trading_log.export_logs('text')
                if not text_content:
                    self.show_message_signal.emit("警告", "没有可导出的日志内容！", "warning")
                    return

                # 获取当前时间作为文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_name = f"trading_log_export_{timestamp}.txt"

                # 保存文件
                with open(file_name, 'w', encoding='utf-8') as f:
                    f.write(text_content)

                self.show_message_signal.emit("成功", f"日志已导出为 {file_name}", "info")
            else:
                # 回退到旧方法
                self.save_trading_log()
        except Exception as e:
            self.show_message_signal.emit("错误", f"导出日志失败: {str(e)}", "error")

    def closeEvent(self, event):
        """处理窗口关闭事件，确保资源正确释放"""
        try:
            # 停止所有计时器
            if hasattr(self, 'update_timer'):
                self.update_timer.stop()
            if hasattr(self, 'network_check_timer'):
                self.network_check_timer.stop()
            if hasattr(self, 'memory_cleanup_timer'):
                self.memory_cleanup_timer.stop()
            if hasattr(self, 'account_update_timer'):
                self.account_update_timer.stop()
            if hasattr(self, 'adx_update_timer'):
                self.adx_update_timer.stop()
            if hasattr(self, 'background_animation_timer'):
                self.background_animation_timer.stop()
            if hasattr(self, 'animation_timer'):
                self.animation_timer.stop()

            # 停止自动交易（如果正在进行）
            if hasattr(self, 'auto_trading_enabled') and self.auto_trading_enabled:
                self.auto_trading_enabled = False
                self.log_trading("正在停止自动交易...")
                # 等待自动交易线程结束
                if hasattr(self, 'auto_trading_thread') and self.auto_trading_thread.is_alive():
                    self.auto_trading_thread.join(timeout=2)

            # 等待所有线程完成
            if hasattr(self, 'thread_pool'):
                self.thread_pool.waitForDone(3000)  # 等待最多3秒

            # 记录程序正常退出
            self.log_trading("程序正常退出")

            # 调用父类方法完成关闭
            super().closeEvent(event)
        except Exception as e:
            print(f"关闭程序时发生错误: {str(e)}")
            super().closeEvent(event)


class EnhancedTradingLog(QTextEdit):
    """
    增强的交易日志组件

    提供动态显示效果、日志过滤、统计信息等功能
    支持线程安全的日志添加和丰富的视觉效果

    Signals:
        log_added: 添加日志时发出的信号 (level, message)
        log_stats_updated: 日志统计更新信号 (total, info, warning, error)
    """

    log_added = pyqtSignal(str, str)  # level, message
    log_stats_updated = pyqtSignal(int, int, int, int)  # total, info, warning, error
    append_signal = pyqtSignal(str)

    def __init__(self) -> None:
        """初始化增强的交易日志组件"""
        super().__init__()

        # 连接信号
        self.append_signal.connect(self._append_html)

        # 日志统计
        self.log_counts = {
            'total': 0,
            'info': 0,
            'warning': 0,
            'error': 0
        }

        # 日志存储（用于过滤）
        self.all_logs = []
        self.current_filter = "全部"

        # 设置最大日志行数
        self.max_lines = 1000

        # 自动滚动设置
        self.auto_scroll = True

    def add_log(self, level: str, message: str, timestamp: str = None):
        """
        添加日志条目

        Args:
            level: 日志级别 (INFO, WARNING, ERROR)
            message: 日志消息
            timestamp: 时间戳，如果为None则自动生成
        """
        if timestamp is None:
            timestamp = datetime.now().strftime('%H:%M:%S')

        # 创建日志条目
        log_entry = {
            'level': level.upper(),
            'message': message,
            'timestamp': timestamp,
            'full_timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 添加到日志存储
        self.all_logs.append(log_entry)

        # 更新统计
        self.log_counts['total'] += 1
        if level.upper() == 'INFO':
            self.log_counts['info'] += 1
        elif level.upper() == 'WARNING':
            self.log_counts['warning'] += 1
        elif level.upper() == 'ERROR':
            self.log_counts['error'] += 1

        # 发出统计更新信号
        self.log_stats_updated.emit(
            self.log_counts['total'],
            self.log_counts['info'],
            self.log_counts['warning'],
            self.log_counts['error']
        )

        # 如果当前过滤器匹配，显示日志
        if self._should_show_log(log_entry):
            html = self._format_log_html(log_entry)
            self.append_signal.emit(html)

        # 发出日志添加信号
        self.log_added.emit(level, message)

        # 限制日志数量
        if len(self.all_logs) > self.max_lines:
            removed_log = self.all_logs.pop(0)
            # 更新统计
            self.log_counts['total'] -= 1
            if removed_log['level'] == 'INFO':
                self.log_counts['info'] -= 1
            elif removed_log['level'] == 'WARNING':
                self.log_counts['warning'] -= 1
            elif removed_log['level'] == 'ERROR':
                self.log_counts['error'] -= 1

    def _format_log_html(self, log_entry: dict) -> str:
        """格式化日志为HTML"""
        level = log_entry['level']
        message = log_entry['message']
        timestamp = log_entry['timestamp']

        # 根据级别设置颜色和图标
        if level == 'INFO':
            color = '#10B981'  # 绿色
            bg_color = 'rgba(16, 185, 129, 0.1)'
            icon = '✅'
            level_text = 'INFO'
        elif level == 'WARNING':
            color = '#F59E0B'  # 黄色
            bg_color = 'rgba(245, 158, 11, 0.1)'
            icon = '⚠️'
            level_text = 'WARN'
        elif level == 'ERROR':
            color = '#EF4444'  # 红色
            bg_color = 'rgba(239, 68, 68, 0.1)'
            icon = '❌'
            level_text = 'ERROR'
        else:
            color = '#94A3B8'  # 灰色
            bg_color = 'rgba(148, 163, 184, 0.1)'
            icon = 'ℹ️'
            level_text = level

        # 创建HTML格式的日志条目
        html = f'''
        <div style="
            margin: 4px 0;
            padding: 8px 12px;
            background: {bg_color};
            border-left: 3px solid {color};
            border-radius: 6px;
            font-family: 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        ">
            <span style="color: #64748B; font-size: 10px; font-weight: 600;">
                [{timestamp}]
            </span>
            <span style="
                color: {color};
                font-weight: 700;
                margin: 0 8px;
                padding: 2px 6px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
                font-size: 10px;
            ">
                {icon} {level_text}
            </span>
            <span style="color: #F8FAFC; font-weight: 500;">
                {message}
            </span>
        </div>
        '''

        return html

    def _should_show_log(self, log_entry: dict) -> bool:
        """判断是否应该显示该日志条目"""
        if self.current_filter == "全部":
            return True
        elif self.current_filter == "信息" and log_entry['level'] == 'INFO':
            return True
        elif self.current_filter == "警告" and log_entry['level'] == 'WARNING':
            return True
        elif self.current_filter == "错误" and log_entry['level'] == 'ERROR':
            return True
        return False

    def _append_html(self, html: str):
        """在主线程中添加HTML内容"""
        super().append(html)

        # 自动滚动到底部
        if self.auto_scroll:
            scrollbar = self.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def filter_logs(self, filter_type: str):
        """过滤日志显示"""
        self.current_filter = filter_type

        # 清空当前显示
        self.clear()

        # 重新显示符合过滤条件的日志
        for log_entry in self.all_logs:
            if self._should_show_log(log_entry):
                html = self._format_log_html(log_entry)
                self._append_html(html)

    def set_auto_scroll(self, enabled: bool):
        """设置自动滚动"""
        self.auto_scroll = enabled

    def get_log_stats(self) -> dict:
        """获取日志统计信息"""
        return self.log_counts.copy()

    def export_logs(self, format_type: str = 'html') -> str:
        """导出日志"""
        if format_type == 'html':
            return self.toHtml()
        elif format_type == 'text':
            lines = []
            for log_entry in self.all_logs:
                line = f"[{log_entry['full_timestamp']}] [{log_entry['level']}] {log_entry['message']}"
                lines.append(line)
            return '\n'.join(lines)
        else:
            return self.toPlainText()

    def appendHtml(self, html: str) -> None:
        """
        添加HTML内容到日志（兼容性方法）
        这个方法提供与ThreadSafeTextEdit的兼容性
        """
        self.append_signal.emit(html)

class ThreadSafeTextEdit(QTextEdit):
    """
    线程安全的文本编辑组件（保持向后兼容）
    """

    append = pyqtSignal(str)

    def __init__(self) -> None:
        super().__init__()
        self.append.connect(self._append)
        self.max_lines: int = 1000

    def appendHtml(self, html: str) -> None:
        self.append.emit(html)

    def _append(self, html: str) -> None:
        super().append(html)

        document = self.document()
        if document.blockCount() > self.max_lines:
            blocks_to_remove = document.blockCount() - self.max_lines
            cursor = QTextCursor(document)
            cursor.movePosition(QTextCursor.MoveOperation.Start)
            cursor.movePosition(QTextCursor.MoveOperation.NextBlock,
                               QTextCursor.MoveMode.KeepAnchor, blocks_to_remove)
            cursor.removeSelectedText()

class Worker(QRunnable):
    """
    工作线程类
    
    将函数包装成可在线程池中运行的任务，
    并通过信号机制与主线程通信
    
    Attributes:
        fn: 要运行的函数
        args: 函数的位置参数
        kwargs: 函数的关键字参数
        signals: 工作线程信号对象
    """
    
    def __init__(self, fn: Callable, *args: Any, **kwargs: Any) -> None:
        """
        初始化工作线程
        
        Args:
            fn: 要在线程中执行的函数
            *args: 传递给函数的位置参数
            **kwargs: 传递给函数的关键字参数
        """
        super().__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()
        
    def run(self) -> None:
        """
        执行工作线程的主要任务
        
        调用指定的函数并通过信号发送结果或异常
        """
        try:
            result = self.fn(*self.args, **self.kwargs)
            self.signals.result.emit(result)
        except Exception as e:
            self.signals.error.emit(e)
        finally:
            self.signals.finished.emit()


class WorkerSignals(QObject):
    """
    工作线程信号类
    
    定义了工作线程可以发出的信号类型
    
    Signals:
        result: 函数执行成功时发出，携带执行结果
        error: 函数执行出错时发出，携带异常对象
        finished: 函数执行完成时发出，无论成功或失败
    """
    
    result = pyqtSignal(object)
    error = pyqtSignal(Exception)
    finished = pyqtSignal()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 