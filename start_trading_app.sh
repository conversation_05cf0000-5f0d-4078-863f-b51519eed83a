#!/bin/bash

# 币安交易系统启动脚本
# 解决Qt平台插件问题

echo "🚀 启动币安交易系统..."

# 设置Qt环境变量
export QT_QPA_PLATFORM_PLUGIN_PATH=""
export QT_QPA_PLATFORM=""

# 尝试不同的Qt插件路径
POSSIBLE_PATHS=(
    "/opt/anaconda3/lib/python3.12/site-packages/PyQt5/Qt5/plugins"
    "/opt/anaconda3/plugins"
    "/usr/local/lib/python3.12/site-packages/PyQt5/Qt5/plugins"
    "/opt/homebrew/lib/python3.12/site-packages/PyQt5/Qt5/plugins"
)

for path in "${POSSIBLE_PATHS[@]}"; do
    if [ -d "$path/platforms" ]; then
        echo "✅ 找到Qt插件路径: $path"
        export QT_QPA_PLATFORM_PLUGIN_PATH="$path"
        break
    fi
done

# 如果仍然找不到，尝试使用系统默认
if [ -z "$QT_QPA_PLATFORM_PLUGIN_PATH" ]; then
    echo "⚠️  未找到Qt插件路径，使用系统默认设置"
    unset QT_QPA_PLATFORM_PLUGIN_PATH
fi

# 检查Python和依赖
echo "🔍 检查Python环境..."
python -c "import PyQt5; print('✅ PyQt5 可用')" 2>/dev/null || {
    echo "❌ PyQt5 不可用，请安装: pip install PyQt5"
    exit 1
}

python -c "import talib; print('✅ TA-Lib 可用')" 2>/dev/null || {
    echo "❌ TA-Lib 不可用，请安装: pip install TA-Lib"
    exit 1
}

python -c "import ccxt; print('✅ CCXT 可用')" 2>/dev/null || {
    echo "❌ CCXT 不可用，请安装: pip install ccxt"
    exit 1
}

# 启动应用程序
echo "🎯 启动交易应用程序..."
cd "$(dirname "$0")"

# 尝试不同的启动方式
if command -v pythonw >/dev/null 2>&1; then
    echo "使用 pythonw 启动..."
    pythonw main_window.py
else
    echo "使用 python 启动..."
    python main_window.py
fi

echo "📱 应用程序已退出"