#!/usr/bin/env python3
"""
ADX交易策略测试脚本

测试ADX策略的各项功能，包括：
- 信号生成准确性
- 风险管理功能
- 仓位计算
- 历史数据回测

作者: [李兴]
版本: 1.0.0
创建日期: 2025-07-28
"""

import sys
import os
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import ccxt
import talib

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adx_trading_strategy import ADXTradingStrategy, create_adx_strategy, validate_adx_signal


def generate_test_data(periods=100):
    """生成测试用的OHLCV数据"""
    np.random.seed(42)  # 确保结果可重现

    # 生成基础价格走势
    base_price = 50000
    price_changes = np.random.normal(0, 0.02, periods)  # 2%的日波动

    # 添加趋势
    trend = np.linspace(0, 0.1, periods)  # 10%的整体上涨趋势
    price_changes += trend

    prices = [base_price]
    for change in price_changes:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    # 生成OHLCV数据
    ohlcv_data = []
    for i in range(periods):
        timestamp = (datetime.now() - timedelta(minutes=(periods-i)*15)).timestamp() * 1000
        close = prices[i+1]
        open_price = prices[i]

        # 生成高低价
        volatility = abs(close - open_price) * 2
        high = max(open_price, close) + volatility * np.random.random()
        low = min(open_price, close) - volatility * np.random.random()

        # 生成成交量
        volume = np.random.uniform(1000, 10000)

        ohlcv_data.append([timestamp, open_price, high, low, close, volume])

    return ohlcv_data


def test_adx_calculation():
    """测试ADX指标计算"""
    print("=== 测试ADX指标计算 ===")

    # 创建策略实例
    strategy = create_adx_strategy()

    # 生成测试数据
    test_data = generate_test_data(50)

    # 更新数据
    strategy.update_data(test_data)

    # 检查数据是否正确更新
    if len(strategy.adx_history) > 0:
        print(f"✓ ADX历史数据长度: {len(strategy.adx_history)}")
        print(f"✓ 当前ADX值: {strategy.adx_history[-1]:.2f}")
        print(f"✓ 当前+DI值: {strategy.plus_di_history[-1]:.2f}")
        print(f"✓ 当前-DI值: {strategy.minus_di_history[-1]:.2f}")
        return True
    else:
        print("✗ ADX计算失败")
        return False


def test_signal_generation():
    """测试信号生成"""
    print("\n=== 测试信号生成 ===")

    strategy = create_adx_strategy()

    # 生成强上涨趋势数据
    print("测试强上涨趋势信号...")
    uptrend_data = generate_uptrend_data()
    strategy.update_data(uptrend_data)

    # 获取信号
    signals = strategy.get_current_signals()
    long_signal = signals['long_signal']
    short_signal = signals['short_signal']

    print(f"做多信号: {long_signal['signal']}")
    if long_signal['signal'] != 'NONE':
        print(f"  原因: {long_signal['reason']}")
        print(f"  强度: {long_signal['strength']:.3f}")

    print(f"做空信号: {short_signal['signal']}")
    if short_signal['signal'] != 'NONE':
        print(f"  原因: {short_signal['reason']}")
        print(f"  强度: {short_signal['strength']:.3f}")

    return long_signal['signal'] == 'LONG' or short_signal['signal'] == 'SHORT'


def generate_uptrend_data(periods=50):
    """生成强上涨趋势数据"""
    base_price = 50000
    prices = [base_price]

    # 生成连续上涨的价格
    for i in range(periods):
        # 每期上涨1-3%
        change = np.random.uniform(0.01, 0.03)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)

    # 转换为OHLCV格式
    ohlcv_data = []
    for i in range(periods):
        timestamp = (datetime.now() - timedelta(minutes=(periods-i)*15)).timestamp() * 1000
        close = prices[i+1]
        open_price = prices[i]

        high = max(open_price, close) * 1.005  # 高点稍高
        low = min(open_price, close) * 0.995   # 低点稍低
        volume = np.random.uniform(5000, 15000)

        ohlcv_data.append([timestamp, open_price, high, low, close, volume])

    return ohlcv_data


def test_risk_management():
    """测试风险管理功能"""
    print("\n=== 测试风险管理功能 ===")

    strategy = create_adx_strategy()

    # 测试参数
    entry_price = 50000
    account_balance = 10000

    # 测试止损计算
    long_stop_loss = strategy.calculate_stop_loss(entry_price, 'LONG')
    short_stop_loss = strategy.calculate_stop_loss(entry_price, 'SHORT')

    print(f"做多止损价格: {long_stop_loss:.2f} (风险: {((entry_price - long_stop_loss) / entry_price * 100):.2f}%)")
    print(f"做空止损价格: {short_stop_loss:.2f} (风险: {((short_stop_loss - entry_price) / entry_price * 100):.2f}%)")

    # 测试止盈计算
    long_take_profit = strategy.calculate_take_profit(entry_price, long_stop_loss, 'LONG')
    short_take_profit = strategy.calculate_take_profit(entry_price, short_stop_loss, 'SHORT')

    print(f"做多止盈价格: {long_take_profit:.2f} (收益: {((long_take_profit - entry_price) / entry_price * 100):.2f}%)")
    print(f"做空止盈价格: {short_take_profit:.2f} (收益: {((entry_price - short_take_profit) / entry_price * 100):.2f}%)")

    # 测试仓位计算
    long_position_size = strategy.calculate_position_size(account_balance, entry_price, long_stop_loss)
    short_position_size = strategy.calculate_position_size(account_balance, entry_price, short_stop_loss)

    print(f"做多建议仓位: {long_position_size:.6f}")
    print(f"做空建议仓位: {short_position_size:.6f}")

    # 验证风险控制
    long_risk = (entry_price - long_stop_loss) * long_position_size
    short_risk = (short_stop_loss - entry_price) * short_position_size

    print(f"做多实际风险: {long_risk:.2f} USDT ({long_risk/account_balance*100:.2f}%)")
    print(f"做空实际风险: {short_risk:.2f} USDT ({short_risk/account_balance*100:.2f}%)")

    return True


def test_confirmation_filters():
    """测试确认过滤器"""
    print("\n=== 测试确认过滤器 ===")

    strategy = create_adx_strategy()
    test_data = generate_test_data(100)

    # 测试过滤器
    filters = strategy.add_confirmation_filter(test_data)

    print("确认过滤器结果:")
    for filter_name, result in filters.items():
        if isinstance(result, bool):
            status = "✓" if result else "✗"
            print(f"  {status} {filter_name}: {result}")
        else:
            print(f"  📊 {filter_name}: {result:.2f}")

    return True


def test_strategy_statistics():
    """测试策略统计功能"""
    print("\n=== 测试策略统计功能 ===")

    strategy = create_adx_strategy()

    # 模拟一些交易
    strategy.total_trades = 10
    strategy.winning_trades = 6
    strategy.losing_trades = 4
    strategy.total_pnl = 1250.50

    stats = strategy.get_strategy_stats()

    print("策略统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    return True


def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始ADX交易策略综合测试")
    print("=" * 50)

    tests = [
        ("ADX指标计算", test_adx_calculation),
        ("信号生成", test_signal_generation),
        ("风险管理", test_risk_management),
        ("确认过滤器", test_confirmation_filters),
        ("策略统计", test_strategy_statistics)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！ADX策略运行正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

    return passed == total


if __name__ == "__main__":
    run_comprehensive_test()