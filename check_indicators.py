#!/usr/bin/env python3
"""
技术指标应用检查脚本
用于检查所有技术指标是否正确应用到AI交易分析中
"""

import os
import re
from datetime import datetime

def check_indicator_calculations():
    """检查指标计算是否完整"""
    print("=== 技术指标计算检查 ===")
    
    if not os.path.exists('main_window.py'):
        print("✗ main_window.py文件不存在")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查指标计算
    calculations = [
        {
            'name': 'RSI',
            'pattern': 'talib.RSI(close_prices',
            'description': '相对强弱指数计算'
        },
        {
            'name': 'MACD',
            'pattern': 'talib.MACD(',
            'description': 'MACD指标计算'
        },
        {
            'name': '布林带',
            'pattern': 'talib.BBANDS(',
            'description': '布林带指标计算'
        },
        {
            'name': 'EMA',
            'pattern': 'talib.EMA(close_prices',
            'description': '指数移动平均线计算'
        },
        {
            'name': 'ADX',
            'pattern': 'talib.ADX(high_prices, low_prices, close_prices',
            'description': 'ADX趋势强度指标计算'
        },
        {
            'name': '+DI/-DI',
            'pattern': 'talib.PLUS_DI(',
            'description': '方向性指标计算'
        },
        {
            'name': 'KDJ/Stoch',
            'pattern': 'talib.STOCH(',
            'description': '随机指标计算'
        },
        {
            'name': 'CCI',
            'pattern': 'talib.CCI(',
            'description': '商品通道指数计算'
        },
        {
            'name': 'MFI',
            'pattern': 'talib.MFI(',
            'description': '资金流量指标计算'
        },
        {
            'name': 'ATR',
            'pattern': 'talib.ATR(',
            'description': '平均真实波幅计算'
        },
        {
            'name': 'OBV',
            'pattern': 'talib.OBV(',
            'description': '能量潮指标计算'
        },
        {
            'name': 'A/D Line',
            'pattern': 'talib.AD(',
            'description': '累积/派发线计算'
        }
    ]
    
    all_calculated = True
    for calc in calculations:
        if calc['pattern'] in content:
            print(f"✓ {calc['name']}: {calc['description']}")
        else:
            print(f"✗ {calc['name']}: 缺失 - {calc['description']}")
            all_calculated = False
    
    return all_calculated

def check_indicator_applications():
    """检查指标在交易分析中的应用"""
    print("\n=== 技术指标应用检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查指标应用逻辑
    applications = [
        {
            'name': 'RSI超买超卖',
            'pattern': 'rsi_value > rsi_overbought',
            'description': 'RSI超买超卖信号判断'
        },
        {
            'name': 'MACD金叉死叉',
            'pattern': 'macd_value > signal_value and prev_macd_value <= prev_signal_value',
            'description': 'MACD金叉死叉信号判断'
        },
        {
            'name': '布林带突破',
            'pattern': 'current_price > upper_band',
            'description': '布林带上下轨突破判断'
        },
        {
            'name': 'EMA均线交叉',
            'pattern': 'ema20_value > ema50_value and prev_ema20_value <= prev_ema50_value',
            'description': 'EMA均线金叉死叉判断'
        },
        {
            'name': 'ADX趋势强度',
            'pattern': 'adx_value > current_adx_threshold',
            'description': 'ADX趋势强度判断'
        },
        {
            'name': 'KDJ随机指标',
            'pattern': 'stoch_k_value > stoch_overbought',
            'description': '随机指标超买超卖判断'
        },
        {
            'name': 'CCI商品通道',
            'pattern': 'cci_value > cci_overbought',
            'description': 'CCI超买超卖判断'
        },
        {
            'name': 'MFI资金流量',
            'pattern': 'mfi_value > mfi_overbought',
            'description': 'MFI超买超卖判断'
        },
        {
            'name': 'OBV成交量趋势',
            'pattern': 'obv[-1] > obv[-2]',
            'description': 'OBV成交量趋势判断'
        }
    ]
    
    all_applied = True
    for app in applications:
        if app['pattern'] in content:
            print(f"✓ {app['name']}: {app['description']}")
        else:
            print(f"✗ {app['name']}: 缺失 - {app['description']}")
            all_applied = False
    
    return all_applied

def check_market_state_adaptation():
    """检查指标在不同市场状态下的适应性"""
    print("\n=== 市场状态适应性检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查市场状态适应逻辑
    adaptations = [
        {
            'name': '强上涨趋势适应',
            'pattern': 'if market_state == "STRONG_UPTREND"',
            'description': '强上涨趋势下的指标权重调整'
        },
        {
            'name': '强下跌趋势适应',
            'pattern': 'if market_state == "STRONG_DOWNTREND"',
            'description': '强下跌趋势下的指标权重调整'
        },
        {
            'name': '震荡市场适应',
            'pattern': 'market_state == "RANGING_WEAK_TREND"',
            'description': '震荡市场下的指标权重调整'
        },
        {
            'name': '趋势强度加权',
            'pattern': 'trend_strength +=',
            'description': '根据信号强度调整趋势分数'
        },
        {
            'name': '信号记录',
            'pattern': 'trend_signals.append',
            'description': '记录各指标信号'
        }
    ]
    
    all_adapted = True
    for adapt in adaptations:
        count = content.count(adapt['pattern'])
        if count > 0:
            print(f"✓ {adapt['name']}: {adapt['description']} (出现{count}次)")
        else:
            print(f"✗ {adapt['name']}: 缺失 - {adapt['description']}")
            all_adapted = False
    
    return all_adapted

def check_indicator_parameters():
    """检查指标参数配置"""
    print("\n=== 指标参数配置检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查参数获取
    parameters = [
        {
            'name': 'RSI参数',
            'pattern': "getattr(self, 'rsi_period', 14)",
            'description': 'RSI周期参数'
        },
        {
            'name': 'MACD参数',
            'pattern': "getattr(self, 'macd_fast', 12)",
            'description': 'MACD快线参数'
        },
        {
            'name': '布林带参数',
            'pattern': "getattr(self, 'bb_period', 20)",
            'description': '布林带周期参数'
        },
        {
            'name': 'EMA参数',
            'pattern': "getattr(self, 'ema_short', 20)",
            'description': 'EMA短期参数'
        },
        {
            'name': 'ADX参数',
            'pattern': "getattr(self, 'adx_period', 14)",
            'description': 'ADX周期参数'
        },
        {
            'name': 'KDJ参数',
            'pattern': "getattr(self, 'stoch_overbought', 80)",
            'description': 'KDJ超买参数'
        },
        {
            'name': 'CCI参数',
            'pattern': "getattr(self, 'cci_overbought', 100)",
            'description': 'CCI超买参数'
        },
        {
            'name': 'MFI参数',
            'pattern': "getattr(self, 'mfi_overbought', 80)",
            'description': 'MFI超买参数'
        }
    ]
    
    all_configured = True
    for param in parameters:
        if param['pattern'] in content:
            print(f"✓ {param['name']}: {param['description']}")
        else:
            print(f"✗ {param['name']}: 缺失 - {param['description']}")
            all_configured = False
    
    return all_configured

def check_indicator_weights():
    """检查指标权重设置"""
    print("\n=== 指标权重设置检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取权重设置
    weight_patterns = [
        r'trend_strength \+= ([\d.]+)',
        r'trend_strength -= ([\d.]+)'
    ]
    
    weights = []
    for pattern in weight_patterns:
        matches = re.findall(pattern, content)
        weights.extend([float(w) for w in matches])
    
    if weights:
        print(f"✓ 发现权重设置: {len(weights)}个")
        print(f"  权重范围: {min(weights):.2f} - {max(weights):.2f}")
        print(f"  平均权重: {sum(weights)/len(weights):.2f}")
        
        # 统计权重分布
        weight_counts = {}
        for w in weights:
            weight_counts[w] = weight_counts.get(w, 0) + 1
        
        print("  权重分布:")
        for weight, count in sorted(weight_counts.items()):
            print(f"    {weight}: {count}次")
        
        return True
    else:
        print("✗ 未发现权重设置")
        return False

def check_missing_indicators():
    """检查可能遗漏的指标"""
    print("\n=== 遗漏指标检查 ===")
    
    # 常见但可能遗漏的指标
    missing_indicators = [
        {
            'name': '威廉指标 (Williams %R)',
            'pattern': 'WILLR',
            'description': '威廉指标，类似KDJ的超买超卖指标'
        },
        {
            'name': '抛物线SAR',
            'pattern': 'SAR',
            'description': '抛物线转向指标'
        },
        {
            'name': '动量指标 (Momentum)',
            'pattern': 'MOM',
            'description': '价格动量指标'
        },
        {
            'name': '变化率 (ROC)',
            'pattern': 'ROC',
            'description': '价格变化率指标'
        }
    ]
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    for indicator in missing_indicators:
        if indicator['pattern'] in content:
            print(f"✓ {indicator['name']}: 已应用")
        else:
            print(f"⚠️  {indicator['name']}: 未应用 - {indicator['description']}")

def generate_indicator_summary():
    """生成指标应用总结"""
    print("\n=== 指标应用总结 ===")
    
    applied_indicators = [
        "✓ RSI - 相对强弱指数 (超买超卖判断)",
        "✓ MACD - 指数平滑移动平均线 (趋势转换)",
        "✓ 布林带 - 价格通道 (突破信号)",
        "✓ EMA - 指数移动平均线 (趋势方向)",
        "✓ ADX - 平均趋向指数 (趋势强度)",
        "✓ +DI/-DI - 方向性指标 (趋势方向)",
        "✓ KDJ/Stoch - 随机指标 (震荡市信号)",
        "✓ CCI - 商品通道指数 (超买超卖)",
        "✓ MFI - 资金流量指标 (成交量加权RSI)",
        "✓ ATR - 平均真实波幅 (波动性)",
        "✓ OBV - 能量潮 (成交量趋势)",
        "✓ A/D Line - 累积/派发线 (成交量分析)"
    ]
    
    print("已应用的技术指标:")
    for indicator in applied_indicators:
        print(f"  {indicator}")
    
    print("\n指标应用特点:")
    features = [
        "• 市场状态适应: 根据ADX判断的市场状态调整指标权重",
        "• 动态权重: 强趋势市场中趋势指标权重增加，震荡指标权重降低",
        "• 信号确认: 多指标综合判断，避免单一指标误导",
        "• 参数可配置: 所有指标参数都可通过配置文件调整",
        "• 实时计算: 每次AI分析都重新计算所有指标",
        "• 详细记录: 记录每个指标的具体信号和权重贡献"
    ]
    
    for feature in features:
        print(f"  {feature}")

def main():
    """主函数"""
    print("技术指标应用检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    calc_ok = check_indicator_calculations()
    app_ok = check_indicator_applications()
    adapt_ok = check_market_state_adaptation()
    param_ok = check_indicator_parameters()
    weight_ok = check_indicator_weights()
    
    # 检查遗漏指标
    check_missing_indicators()
    
    # 生成总结
    generate_indicator_summary()
    
    # 总结
    print("\n=== 检查总结 ===")
    print(f"指标计算: {'✓' if calc_ok else '✗'}")
    print(f"指标应用: {'✓' if app_ok else '✗'}")
    print(f"市场适应: {'✓' if adapt_ok else '✗'}")
    print(f"参数配置: {'✓' if param_ok else '✗'}")
    print(f"权重设置: {'✓' if weight_ok else '✗'}")
    
    overall_status = calc_ok and app_ok and adapt_ok and param_ok and weight_ok
    
    if overall_status:
        print("\n🎉 所有主要技术指标都已正确应用！")
        print("\n系统特性:")
        print("✓ 12种主要技术指标全面覆盖")
        print("✓ 智能市场状态适应")
        print("✓ 动态权重调整")
        print("✓ 参数完全可配置")
        print("✓ 多指标综合判断")
        print("✓ 详细信号记录")
    else:
        print("\n⚠️  技术指标应用存在问题")
        print("请检查上述失败项目")

if __name__ == "__main__":
    main()
