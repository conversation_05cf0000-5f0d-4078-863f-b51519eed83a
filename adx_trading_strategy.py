#!/usr/bin/env python3
"""
ADX交易策略模块

实现基于ADX（平均方向指数）的专业交易策略，包括：
- 精确的多空信号生成
- 连续期数趋势确认
- 风险管理和仓位控制
- 止盈止损优化

作者: [李兴]
版本: 1.0.0
创建日期: 2025-07-28
"""

import numpy as np
import talib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging


class ADXTradingStrategy:
    """
    ADX交易策略类

    实现基于ADX指标的专业交易策略，包含精确的信号生成逻辑、
    风险管理和仓位控制功能。
    """

    def __init__(self,
                 adx_period: int = 14,
                 adx_threshold: float = 25.0,
                 confirmation_periods: int = 3,
                 timeframe: str = '15m',
                 risk_per_trade: float = 0.02,
                 max_position_size: float = 0.1):
        """
        初始化ADX交易策略

        Args:
            adx_period: ADX计算周期，默认14
            adx_threshold: ADX强度阈值，默认25
            confirmation_periods: 确认信号所需的连续期数，默认3
            timeframe: 分析时间框架，默认15分钟
            risk_per_trade: 每笔交易的风险比例，默认2%
            max_position_size: 最大仓位比例，默认10%
        """
        self.adx_period = adx_period
        self.adx_threshold = adx_threshold
        self.confirmation_periods = confirmation_periods
        self.timeframe = timeframe
        self.risk_per_trade = risk_per_trade
        self.max_position_size = max_position_size

        # 历史数据存储
        self.adx_history: List[float] = []
        self.plus_di_history: List[float] = []
        self.minus_di_history: List[float] = []
        self.price_history: List[float] = []
        self.timestamp_history: List[datetime] = []

        # 当前持仓状态
        self.current_position: Optional[Dict[str, Any]] = None
        self.entry_price: Optional[float] = None
        self.stop_loss: Optional[float] = None
        self.take_profit: Optional[float] = None

        # 策略统计
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.total_pnl = 0.0

        # 日志设置
        self.logger = logging.getLogger(__name__)

    def update_data(self, ohlcv_data: List[List[float]]) -> None:
        """
        更新OHLCV数据并计算ADX指标

        Args:
            ohlcv_data: OHLCV数据列表 [[timestamp, open, high, low, close, volume], ...]
        """
        if len(ohlcv_data) < self.adx_period + 5:
            self.logger.warning(f"数据不足，需要至少{self.adx_period + 5}个数据点")
            return

        # 提取价格数据
        high_prices = np.array([float(candle[2]) for candle in ohlcv_data])
        low_prices = np.array([float(candle[3]) for candle in ohlcv_data])
        close_prices = np.array([float(candle[4]) for candle in ohlcv_data])
        timestamps = [datetime.fromtimestamp(candle[0] / 1000) for candle in ohlcv_data]

        # 计算ADX指标
        try:
            adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=self.adx_period)
            plus_di = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=self.adx_period)
            minus_di = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=self.adx_period)

            # 更新历史数据（只保留最新的数据）
            valid_indices = ~np.isnan(adx)
            if np.any(valid_indices):
                self.adx_history = adx[valid_indices].tolist()
                self.plus_di_history = plus_di[valid_indices].tolist()
                self.minus_di_history = minus_di[valid_indices].tolist()
                self.price_history = close_prices[valid_indices].tolist()
                self.timestamp_history = [timestamps[i] for i in range(len(timestamps)) if valid_indices[i]]

                # 限制历史数据长度
                max_history = 100
                if len(self.adx_history) > max_history:
                    self.adx_history = self.adx_history[-max_history:]
                    self.plus_di_history = self.plus_di_history[-max_history:]
                    self.minus_di_history = self.minus_di_history[-max_history:]
                    self.price_history = self.price_history[-max_history:]
                    self.timestamp_history = self.timestamp_history[-max_history:]

        except Exception as e:
            self.logger.error(f"计算ADX指标失败: {str(e)}")

    def check_trend_direction(self, periods: int = None) -> Tuple[bool, bool, bool]:
        """
        检查趋势方向的连续性

        Args:
            periods: 检查的连续期数，默认使用confirmation_periods

        Returns:
            Tuple[bool, bool, bool]: (ADX趋势, +DI趋势, -DI趋势)
            True表示上升，False表示下降
        """
        if periods is None:
            periods = self.confirmation_periods

        if len(self.adx_history) < periods + 1:
            return False, False, False

        # 检查ADX趋势
        adx_trend = all(
            self.adx_history[-i-1] < self.adx_history[-i]
            for i in range(1, periods + 1)
        )

        # 检查+DI趋势
        plus_di_trend = all(
            self.plus_di_history[-i-1] < self.plus_di_history[-i]
            for i in range(1, periods + 1)
        )

        # 检查-DI趋势
        minus_di_trend = all(
            self.minus_di_history[-i-1] < self.minus_di_history[-i]
            for i in range(1, periods + 1)
        )

        return adx_trend, plus_di_trend, minus_di_trend

    def check_trend_decline(self, periods: int = None) -> Tuple[bool, bool, bool]:
        """
        检查趋势下降的连续性

        Args:
            periods: 检查的连续期数，默认使用confirmation_periods

        Returns:
            Tuple[bool, bool, bool]: (ADX下降, +DI下降, -DI下降)
            True表示下降趋势
        """
        if periods is None:
            periods = self.confirmation_periods

        if len(self.adx_history) < periods + 1:
            return False, False, False

        # 检查ADX下降趋势
        adx_decline = all(
            self.adx_history[-i-1] > self.adx_history[-i]
            for i in range(1, periods + 1)
        )

        # 检查+DI下降趋势
        plus_di_decline = all(
            self.plus_di_history[-i-1] > self.plus_di_history[-i]
            for i in range(1, periods + 1)
        )

        # 检查-DI下降趋势
        minus_di_decline = all(
            self.minus_di_history[-i-1] > self.minus_di_history[-i]
            for i in range(1, periods + 1)
        )

        return adx_decline, plus_di_decline, minus_di_decline

    def generate_long_signal(self) -> Dict[str, Any]:
        """
        生成做多信号

        根据策略规则检查做多条件：
        - ADX上升（连续2-3期）
        - +DI上升（连续2-3期）
        - -DI下降（连续2-3期）
        - ADX > 25

        Returns:
            Dict: 信号信息，包含信号强度、确认状态等
        """
        if len(self.adx_history) < self.confirmation_periods + 1:
            return {'signal': 'NONE', 'reason': '数据不足'}

        current_adx = self.adx_history[-1]
        current_plus_di = self.plus_di_history[-1]
        current_minus_di = self.minus_di_history[-1]

        # 检查ADX强度阈值
        if current_adx < self.adx_threshold:
            return {
                'signal': 'NONE',
                'reason': f'ADX({current_adx:.2f}) < 阈值({self.adx_threshold})',
                'adx': current_adx,
                'plus_di': current_plus_di,
                'minus_di': current_minus_di
            }

        # 检查趋势方向
        adx_rising, plus_di_rising, minus_di_rising = self.check_trend_direction()
        adx_decline, plus_di_decline, minus_di_decline = self.check_trend_decline()

        # 做多条件检查
        long_conditions = {
            'adx_rising': adx_rising,
            'plus_di_rising': plus_di_rising,
            'minus_di_declining': minus_di_decline,
            'adx_above_threshold': current_adx >= self.adx_threshold
        }

        # 所有条件都满足才生成做多信号
        if all(long_conditions.values()):
            signal_strength = self._calculate_signal_strength(current_adx, current_plus_di, current_minus_di)

            return {
                'signal': 'LONG',
                'strength': signal_strength,
                'adx': current_adx,
                'plus_di': current_plus_di,
                'minus_di': current_minus_di,
                'conditions': long_conditions,
                'timestamp': self.timestamp_history[-1] if self.timestamp_history else datetime.now(),
                'reason': f'做多信号确认: ADX={current_adx:.2f}, +DI={current_plus_di:.2f}↑, -DI={current_minus_di:.2f}↓'
            }

        # 返回未满足的条件
        failed_conditions = [k for k, v in long_conditions.items() if not v]
        return {
            'signal': 'NONE',
            'reason': f'做多条件未满足: {", ".join(failed_conditions)}',
            'adx': current_adx,
            'plus_di': current_plus_di,
            'minus_di': current_minus_di,
            'conditions': long_conditions
        }

    def generate_short_signal(self) -> Dict[str, Any]:
        """
        生成做空信号

        根据策略规则检查做空条件：
        - ADX下降（连续2-3期）
        - +DI下降（连续2-3期）
        - -DI上升（连续2-3期）
        - ADX > 25

        Returns:
            Dict: 信号信息，包含信号强度、确认状态等
        """
        if len(self.adx_history) < self.confirmation_periods + 1:
            return {'signal': 'NONE', 'reason': '数据不足'}

        current_adx = self.adx_history[-1]
        current_plus_di = self.plus_di_history[-1]
        current_minus_di = self.minus_di_history[-1]

        # 检查ADX强度阈值
        if current_adx < self.adx_threshold:
            return {
                'signal': 'NONE',
                'reason': f'ADX({current_adx:.2f}) < 阈值({self.adx_threshold})',
                'adx': current_adx,
                'plus_di': current_plus_di,
                'minus_di': current_minus_di
            }

        # 检查趋势方向
        adx_rising, plus_di_rising, minus_di_rising = self.check_trend_direction()
        adx_decline, plus_di_decline, minus_di_decline = self.check_trend_decline()

        # 做空条件检查
        short_conditions = {
            'adx_declining': adx_decline,
            'plus_di_declining': plus_di_decline,
            'minus_di_rising': minus_di_rising,
            'adx_above_threshold': current_adx >= self.adx_threshold
        }

        # 所有条件都满足才生成做空信号
        if all(short_conditions.values()):
            signal_strength = self._calculate_signal_strength(current_adx, current_plus_di, current_minus_di)

            return {
                'signal': 'SHORT',
                'strength': signal_strength,
                'adx': current_adx,
                'plus_di': current_plus_di,
                'minus_di': current_minus_di,
                'conditions': short_conditions,
                'timestamp': self.timestamp_history[-1] if self.timestamp_history else datetime.now(),
                'reason': f'做空信号确认: ADX={current_adx:.2f}↓, +DI={current_plus_di:.2f}↓, -DI={current_minus_di:.2f}↑'
            }

        # 返回未满足的条件
        failed_conditions = [k for k, v in short_conditions.items() if not v]
        return {
            'signal': 'NONE',
            'reason': f'做空条件未满足: {", ".join(failed_conditions)}',
            'adx': current_adx,
            'plus_di': current_plus_di,
            'minus_di': current_minus_di,
            'conditions': short_conditions
        }

    def _calculate_signal_strength(self, adx: float, plus_di: float, minus_di: float) -> float:
        """
        计算信号强度

        Args:
            adx: 当前ADX值
            plus_di: 当前+DI值
            minus_di: 当前-DI值

        Returns:
            float: 信号强度 (0.0-1.0)
        """
        # 基于ADX强度的权重
        adx_strength = min(adx / 50.0, 1.0)  # ADX越高，信号越强

        # 基于DI差值的权重
        di_diff = abs(plus_di - minus_di)
        di_strength = min(di_diff / 30.0, 1.0)  # DI差值越大，信号越强

        # 综合信号强度
        signal_strength = (adx_strength * 0.6 + di_strength * 0.4)

        return round(signal_strength, 3)

    def calculate_position_size(self, account_balance: float, current_price: float,
                              stop_loss_price: float) -> float:
        """
        计算仓位大小

        Args:
            account_balance: 账户余额
            current_price: 当前价格
            stop_loss_price: 止损价格

        Returns:
            float: 建议的仓位大小
        """
        # 计算风险金额
        risk_amount = account_balance * self.risk_per_trade

        # 计算每单位的风险
        price_risk = abs(current_price - stop_loss_price)
        if price_risk == 0:
            return 0.0

        # 计算基础仓位大小
        base_position_size = risk_amount / price_risk

        # 限制最大仓位
        max_position_value = account_balance * self.max_position_size
        max_position_size = max_position_value / current_price

        # 返回较小的值
        position_size = min(base_position_size, max_position_size)

        return round(position_size, 6)

    def calculate_stop_loss(self, entry_price: float, signal_type: str,
                          atr_value: float = None) -> float:
        """
        计算止损价格

        Args:
            entry_price: 入场价格
            signal_type: 信号类型 ('LONG' 或 'SHORT')
            atr_value: ATR值，用于动态止损

        Returns:
            float: 止损价格
        """
        if len(self.adx_history) == 0:
            # 默认止损比例
            default_stop_percent = 0.02  # 2%
            if signal_type == 'LONG':
                return entry_price * (1 - default_stop_percent)
            else:
                return entry_price * (1 + default_stop_percent)

        current_adx = self.adx_history[-1]

        # 基于ADX强度调整止损距离
        if current_adx >= 40:
            # 强趋势，止损距离较远
            stop_percent = 0.025  # 2.5%
        elif current_adx >= 30:
            # 中等趋势
            stop_percent = 0.02   # 2%
        else:
            # 弱趋势，止损距离较近
            stop_percent = 0.015  # 1.5%

        # 如果有ATR值，使用ATR动态止损
        if atr_value:
            atr_multiplier = 2.0  # ATR倍数
            atr_stop_distance = atr_value * atr_multiplier
            price_stop_distance = entry_price * stop_percent

            # 使用较大的止损距离
            stop_distance = max(atr_stop_distance, price_stop_distance)
        else:
            stop_distance = entry_price * stop_percent

        if signal_type == 'LONG':
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    def calculate_take_profit(self, entry_price: float, stop_loss_price: float,
                            signal_type: str, risk_reward_ratio: float = 2.0) -> float:
        """
        计算止盈价格

        Args:
            entry_price: 入场价格
            stop_loss_price: 止损价格
            signal_type: 信号类型 ('LONG' 或 'SHORT')
            risk_reward_ratio: 风险回报比，默认2:1

        Returns:
            float: 止盈价格
        """
        # 计算风险距离
        risk_distance = abs(entry_price - stop_loss_price)

        # 基于ADX强度调整风险回报比
        if len(self.adx_history) > 0:
            current_adx = self.adx_history[-1]
            if current_adx >= 40:
                # 强趋势，提高风险回报比
                risk_reward_ratio = 3.0
            elif current_adx >= 30:
                risk_reward_ratio = 2.5
            else:
                risk_reward_ratio = 2.0

        # 计算止盈距离
        profit_distance = risk_distance * risk_reward_ratio

        if signal_type == 'LONG':
            return entry_price + profit_distance
        else:
            return entry_price - profit_distance

    def check_exit_conditions(self, current_price: float) -> Dict[str, Any]:
        """
        检查退出条件

        Args:
            current_price: 当前价格

        Returns:
            Dict: 退出信号信息
        """
        if not self.current_position:
            return {'exit': False, 'reason': '无持仓'}

        position_type = self.current_position['type']
        entry_price = self.current_position['entry_price']

        # 检查止损
        if self.stop_loss:
            if position_type == 'LONG' and current_price <= self.stop_loss:
                return {
                    'exit': True,
                    'reason': 'STOP_LOSS',
                    'message': f'触发止损: 当前价格{current_price} <= 止损价{self.stop_loss}'
                }
            elif position_type == 'SHORT' and current_price >= self.stop_loss:
                return {
                    'exit': True,
                    'reason': 'STOP_LOSS',
                    'message': f'触发止损: 当前价格{current_price} >= 止损价{self.stop_loss}'
                }

        # 检查止盈
        if self.take_profit:
            if position_type == 'LONG' and current_price >= self.take_profit:
                return {
                    'exit': True,
                    'reason': 'TAKE_PROFIT',
                    'message': f'触发止盈: 当前价格{current_price} >= 止盈价{self.take_profit}'
                }
            elif position_type == 'SHORT' and current_price <= self.take_profit:
                return {
                    'exit': True,
                    'reason': 'TAKE_PROFIT',
                    'message': f'触发止盈: 当前价格{current_price} <= 止盈价{self.take_profit}'
                }

        # 检查趋势反转
        if len(self.adx_history) >= self.confirmation_periods + 1:
            trend_reversal = self._check_trend_reversal(position_type)
            if trend_reversal:
                return {
                    'exit': True,
                    'reason': 'TREND_REVERSAL',
                    'message': f'趋势反转，建议平仓'
                }

        return {'exit': False, 'reason': '持仓中'}

    def _check_trend_reversal(self, position_type: str) -> bool:
        """
        检查趋势反转

        Args:
            position_type: 持仓类型 ('LONG' 或 'SHORT')

        Returns:
            bool: 是否发生趋势反转
        """
        if len(self.adx_history) < self.confirmation_periods + 1:
            return False

        current_adx = self.adx_history[-1]
        current_plus_di = self.plus_di_history[-1]
        current_minus_di = self.minus_di_history[-1]

        # ADX跌破阈值，趋势结束
        if current_adx < self.adx_threshold:
            return True

        # 检查DI交叉
        if position_type == 'LONG':
            # 做多持仓，检查是否-DI上穿+DI
            if current_minus_di > current_plus_di:
                # 检查是否连续确认
                di_cross_confirmed = all(
                    self.minus_di_history[-i] > self.plus_di_history[-i]
                    for i in range(1, min(3, len(self.adx_history)) + 1)
                )
                return di_cross_confirmed
        else:  # SHORT
            # 做空持仓，检查是否+DI上穿-DI
            if current_plus_di > current_minus_di:
                # 检查是否连续确认
                di_cross_confirmed = all(
                    self.plus_di_history[-i] > self.minus_di_history[-i]
                    for i in range(1, min(3, len(self.adx_history)) + 1)
                )
                return di_cross_confirmed

        return False

    def open_position(self, signal: Dict[str, Any], entry_price: float,
                     position_size: float) -> bool:
        """
        开仓

        Args:
            signal: 交易信号
            entry_price: 入场价格
            position_size: 仓位大小

        Returns:
            bool: 是否成功开仓
        """
        try:
            signal_type = signal['signal']

            # 计算止损和止盈
            stop_loss = self.calculate_stop_loss(entry_price, signal_type)
            take_profit = self.calculate_take_profit(entry_price, stop_loss, signal_type)

            # 记录持仓信息
            self.current_position = {
                'type': signal_type,
                'entry_price': entry_price,
                'size': position_size,
                'timestamp': datetime.now(),
                'signal_strength': signal.get('strength', 0.0),
                'adx_at_entry': signal.get('adx', 0.0)
            }

            self.entry_price = entry_price
            self.stop_loss = stop_loss
            self.take_profit = take_profit

            self.logger.info(f"开仓成功: {signal_type}, 入场价: {entry_price}, 止损: {stop_loss}, 止盈: {take_profit}")
            return True

        except Exception as e:
            self.logger.error(f"开仓失败: {str(e)}")
            return False

    def close_position(self, exit_price: float, reason: str) -> Dict[str, Any]:
        """
        平仓

        Args:
            exit_price: 平仓价格
            reason: 平仓原因

        Returns:
            Dict: 交易结果
        """
        if not self.current_position:
            return {'success': False, 'reason': '无持仓'}

        try:
            position_type = self.current_position['type']
            entry_price = self.current_position['entry_price']
            position_size = self.current_position['size']

            # 计算盈亏
            if position_type == 'LONG':
                pnl = (exit_price - entry_price) * position_size
            else:  # SHORT
                pnl = (entry_price - exit_price) * position_size

            # 计算盈亏百分比
            pnl_percent = (pnl / (entry_price * position_size)) * 100

            # 更新统计
            self.total_trades += 1
            self.total_pnl += pnl

            if pnl > 0:
                self.winning_trades += 1
            else:
                self.losing_trades += 1

            # 交易结果
            trade_result = {
                'success': True,
                'type': position_type,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'size': position_size,
                'pnl': round(pnl, 4),
                'pnl_percent': round(pnl_percent, 2),
                'reason': reason,
                'duration': datetime.now() - self.current_position['timestamp'],
                'signal_strength': self.current_position.get('signal_strength', 0.0)
            }

            # 清除持仓信息
            self.current_position = None
            self.entry_price = None
            self.stop_loss = None
            self.take_profit = None

            self.logger.info(f"平仓成功: {reason}, 盈亏: {pnl:.4f} ({pnl_percent:.2f}%)")
            return trade_result

        except Exception as e:
            self.logger.error(f"平仓失败: {str(e)}")
            return {'success': False, 'reason': str(e)}

    def get_strategy_stats(self) -> Dict[str, Any]:
        """
        获取策略统计信息

        Returns:
            Dict: 策略统计数据
        """
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        avg_pnl = self.total_pnl / self.total_trades if self.total_trades > 0 else 0

        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': round(win_rate, 2),
            'total_pnl': round(self.total_pnl, 4),
            'avg_pnl': round(avg_pnl, 4),
            'current_position': self.current_position is not None,
            'position_type': self.current_position['type'] if self.current_position else None
        }

    def get_current_signals(self) -> Dict[str, Any]:
        """
        获取当前交易信号

        Returns:
            Dict: 包含多空信号的完整信息
        """
        long_signal = self.generate_long_signal()
        short_signal = self.generate_short_signal()

        # 获取当前ADX状态
        current_status = {
            'adx': self.adx_history[-1] if self.adx_history else 0.0,
            'plus_di': self.plus_di_history[-1] if self.plus_di_history else 0.0,
            'minus_di': self.minus_di_history[-1] if self.minus_di_history else 0.0,
            'timestamp': self.timestamp_history[-1] if self.timestamp_history else datetime.now()
        }

        return {
            'long_signal': long_signal,
            'short_signal': short_signal,
            'current_status': current_status,
            'strategy_params': {
                'adx_period': self.adx_period,
                'adx_threshold': self.adx_threshold,
                'confirmation_periods': self.confirmation_periods,
                'timeframe': self.timeframe
            }
        }

    def add_confirmation_filter(self, ohlcv_data: List[List[float]]) -> Dict[str, bool]:
        """
        添加确认信号和过滤器

        Args:
            ohlcv_data: OHLCV数据

        Returns:
            Dict: 各种确认信号的状态
        """
        if len(ohlcv_data) < 50:
            return {'volume_filter': False, 'volatility_filter': False, 'trend_filter': False}

        # 提取数据
        volumes = np.array([float(candle[5]) for candle in ohlcv_data])
        close_prices = np.array([float(candle[4]) for candle in ohlcv_data])
        high_prices = np.array([float(candle[2]) for candle in ohlcv_data])
        low_prices = np.array([float(candle[3]) for candle in ohlcv_data])

        # 成交量过滤器
        avg_volume = np.mean(volumes[-20:])  # 20期平均成交量
        current_volume = volumes[-1]
        volume_filter = current_volume > avg_volume * 1.2  # 当前成交量大于平均值的120%

        # 波动率过滤器
        atr = talib.ATR(high_prices, low_prices, close_prices, timeperiod=14)
        current_atr = atr[-1] if len(atr) > 0 and not np.isnan(atr[-1]) else 0
        avg_atr = np.mean(atr[-10:]) if len(atr) >= 10 else current_atr
        volatility_filter = current_atr > avg_atr * 0.8  # 当前波动率不能太低

        # 趋势过滤器（使用EMA）
        ema_short = talib.EMA(close_prices, timeperiod=12)
        ema_long = talib.EMA(close_prices, timeperiod=26)

        if len(ema_short) > 0 and len(ema_long) > 0:
            trend_filter = abs(ema_short[-1] - ema_long[-1]) / ema_long[-1] > 0.005  # 短期和长期EMA差距大于0.5%
        else:
            trend_filter = False

        return {
            'volume_filter': volume_filter,
            'volatility_filter': volatility_filter,
            'trend_filter': trend_filter,
            'current_volume': current_volume,
            'avg_volume': avg_volume,
            'current_atr': current_atr,
            'avg_atr': avg_atr
        }

    def reset_strategy(self):
        """
        重置策略状态
        """
        self.adx_history.clear()
        self.plus_di_history.clear()
        self.minus_di_history.clear()
        self.price_history.clear()
        self.timestamp_history.clear()

        self.current_position = None
        self.entry_price = None
        self.stop_loss = None
        self.take_profit = None

        self.logger.info("策略状态已重置")

    def update_parameters(self, **kwargs):
        """
        更新策略参数

        Args:
            **kwargs: 要更新的参数
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                self.logger.info(f"参数已更新: {key} = {value}")
            else:
                self.logger.warning(f"未知参数: {key}")


# 辅助函数
def create_adx_strategy(config: Dict[str, Any] = None) -> ADXTradingStrategy:
    """
    创建ADX交易策略实例

    Args:
        config: 配置参数字典

    Returns:
        ADXTradingStrategy: 策略实例
    """
    if config is None:
        config = {}

    return ADXTradingStrategy(
        adx_period=config.get('adx_period', 14),
        adx_threshold=config.get('adx_threshold', 25.0),
        confirmation_periods=config.get('confirmation_periods', 3),
        timeframe=config.get('timeframe', '15m'),
        risk_per_trade=config.get('risk_per_trade', 0.02),
        max_position_size=config.get('max_position_size', 0.1)
    )


def validate_adx_signal(signal: Dict[str, Any], filters: Dict[str, bool]) -> bool:
    """
    验证ADX信号的有效性

    Args:
        signal: ADX信号
        filters: 确认过滤器

    Returns:
        bool: 信号是否有效
    """
    if signal['signal'] == 'NONE':
        return False

    # 检查信号强度
    if signal.get('strength', 0) < 0.3:
        return False

    # 检查确认过滤器
    required_filters = ['volume_filter', 'volatility_filter']
    for filter_name in required_filters:
        if not filters.get(filter_name, False):
            return False

    return True

    def calculate_position_size(self, account_balance: float, current_price: float,
                              stop_loss_price: float) -> float:
        """
        计算仓位大小

        Args:
            account_balance: 账户余额
            current_price: 当前价格
            stop_loss_price: 止损价格

        Returns:
            float: 建议的仓位大小
        """
        # 计算风险金额
        risk_amount = account_balance * self.risk_per_trade

        # 计算每单位的风险
        price_risk = abs(current_price - stop_loss_price)
        if price_risk == 0:
            return 0.0

        # 计算基础仓位大小
        base_position_size = risk_amount / price_risk

        # 限制最大仓位
        max_position_value = account_balance * self.max_position_size
        max_position_size = max_position_value / current_price

        # 返回较小的值
        position_size = min(base_position_size, max_position_size)

        return round(position_size, 6)

    def calculate_stop_loss(self, entry_price: float, signal_type: str,
                          atr_value: float = None) -> float:
        """
        计算止损价格

        Args:
            entry_price: 入场价格
            signal_type: 信号类型 ('LONG' 或 'SHORT')
            atr_value: ATR值，用于动态止损

        Returns:
            float: 止损价格
        """
        if len(self.adx_history) == 0:
            # 默认止损比例
            default_stop_percent = 0.02  # 2%
            if signal_type == 'LONG':
                return entry_price * (1 - default_stop_percent)
            else:
                return entry_price * (1 + default_stop_percent)

        current_adx = self.adx_history[-1]

        # 基于ADX强度调整止损距离
        if current_adx >= 40:
            # 强趋势，止损距离较远
            stop_percent = 0.025  # 2.5%
        elif current_adx >= 30:
            # 中等趋势
            stop_percent = 0.02   # 2%
        else:
            # 弱趋势，止损距离较近
            stop_percent = 0.015  # 1.5%

        # 如果有ATR值，使用ATR动态止损
        if atr_value:
            atr_multiplier = 2.0  # ATR倍数
            atr_stop_distance = atr_value * atr_multiplier
            price_stop_distance = entry_price * stop_percent

            # 使用较大的止损距离
            stop_distance = max(atr_stop_distance, price_stop_distance)
        else:
            stop_distance = entry_price * stop_percent

        if signal_type == 'LONG':
            return entry_price - stop_distance
        else:
            return entry_price + stop_distance

    def calculate_take_profit(self, entry_price: float, stop_loss_price: float,
                            signal_type: str, risk_reward_ratio: float = 2.0) -> float:
        """
        计算止盈价格

        Args:
            entry_price: 入场价格
            stop_loss_price: 止损价格
            signal_type: 信号类型 ('LONG' 或 'SHORT')
            risk_reward_ratio: 风险回报比，默认2:1

        Returns:
            float: 止盈价格
        """
        # 计算风险距离
        risk_distance = abs(entry_price - stop_loss_price)

        # 基于ADX强度调整风险回报比
        if len(self.adx_history) > 0:
            current_adx = self.adx_history[-1]
            if current_adx >= 40:
                # 强趋势，提高风险回报比
                risk_reward_ratio = 3.0
            elif current_adx >= 30:
                risk_reward_ratio = 2.5
            else:
                risk_reward_ratio = 2.0

        # 计算止盈距离
        profit_distance = risk_distance * risk_reward_ratio

        if signal_type == 'LONG':
            return entry_price + profit_distance
        else:
            return entry_price - profit_distance

    def check_exit_conditions(self, current_price: float) -> Dict[str, Any]:
        """
        检查退出条件

        Args:
            current_price: 当前价格

        Returns:
            Dict: 退出信号信息
        """
        if not self.current_position:
            return {'exit': False, 'reason': '无持仓'}

        position_type = self.current_position['type']
        entry_price = self.current_position['entry_price']

        # 检查止损
        if self.stop_loss:
            if position_type == 'LONG' and current_price <= self.stop_loss:
                return {
                    'exit': True,
                    'reason': 'STOP_LOSS',
                    'message': f'触发止损: 当前价格{current_price} <= 止损价{self.stop_loss}'
                }
            elif position_type == 'SHORT' and current_price >= self.stop_loss:
                return {
                    'exit': True,
                    'reason': 'STOP_LOSS',
                    'message': f'触发止损: 当前价格{current_price} >= 止损价{self.stop_loss}'
                }

        # 检查止盈
        if self.take_profit:
            if position_type == 'LONG' and current_price >= self.take_profit:
                return {
                    'exit': True,
                    'reason': 'TAKE_PROFIT',
                    'message': f'触发止盈: 当前价格{current_price} >= 止盈价{self.take_profit}'
                }
            elif position_type == 'SHORT' and current_price <= self.take_profit:
                return {
                    'exit': True,
                    'reason': 'TAKE_PROFIT',
                    'message': f'触发止盈: 当前价格{current_price} <= 止盈价{self.take_profit}'
                }

        # 检查趋势反转
        if len(self.adx_history) >= self.confirmation_periods + 1:
            trend_reversal = self._check_trend_reversal(position_type)
            if trend_reversal:
                return {
                    'exit': True,
                    'reason': 'TREND_REVERSAL',
                    'message': f'趋势反转，建议平仓'
                }

        return {'exit': False, 'reason': '持仓中'}