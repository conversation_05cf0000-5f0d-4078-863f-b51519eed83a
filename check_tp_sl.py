#!/usr/bin/env python3
"""
止盈止损功能检查脚本
用于诊断止盈止损设置和应用是否正常工作
"""

import os
import json
import re
from datetime import datetime

def check_config_files():
    """检查配置文件中的止盈止损设置"""
    print("=== 配置文件检查 ===")
    
    config_files = {
        'config.json': ['tp_percent', 'sl_percent'],
        'trading_settings.json': ['tp_percent', 'sl_percent']
    }
    
    for file_name, keys in config_files.items():
        if os.path.exists(file_name):
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print(f"\n{file_name}:")
                for key in keys:
                    if key in config:
                        value = config[key]
                        print(f"  ✓ {key}: {value}%")
                    else:
                        print(f"  ✗ {key}: 未设置")
                        
            except Exception as e:
                print(f"  ✗ {file_name} 读取失败: {str(e)}")
        else:
            print(f"  ✗ {file_name} 不存在")

def check_code_implementation():
    """检查代码中的止盈止损实现"""
    print("\n=== 代码实现检查 ===")
    
    if not os.path.exists('main_window.py'):
        print("✗ main_window.py文件不存在")
        return False
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键函数和逻辑
    checks = [
        {
            'name': 'apply_tp_sl_settings函数',
            'pattern': 'def apply_tp_sl_settings',
            'description': '应用止盈止损设置的函数'
        },
        {
            'name': 'update_tp_sl_display函数',
            'pattern': 'def update_tp_sl_display',
            'description': '更新止盈止损显示的函数'
        },
        {
            'name': '止盈止损UI控件',
            'pattern': 'self.tp_spinbox = QDoubleSpinBox()',
            'description': '止盈设置控件'
        },
        {
            'name': '止盈止损变量',
            'pattern': 'self.tp_percent',
            'description': '止盈百分比变量'
        },
        {
            'name': '配置加载',
            'pattern': 'def load_config',
            'description': '加载配置文件的函数'
        },
        {
            'name': '止盈单创建',
            'pattern': 'TAKE_PROFIT_MARKET',
            'description': '创建止盈订单'
        },
        {
            'name': '止损单创建',
            'pattern': 'STOP_MARKET',
            'description': '创建止损订单'
        }
    ]
    
    all_passed = True
    for check in checks:
        if check['pattern'] in content:
            print(f"✓ {check['name']}: {check['description']}")
        else:
            print(f"✗ {check['name']}: 缺失 - {check['description']}")
            all_passed = False
    
    return all_passed

def check_apply_function_implementation():
    """检查apply_tp_sl_settings函数的具体实现"""
    print("\n=== apply_tp_sl_settings函数实现检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取apply_tp_sl_settings函数的内容
    pattern = r'def apply_tp_sl_settings\(self\).*?(?=def|\Z)'
    match = re.search(pattern, content, re.DOTALL)
    
    if match:
        function_content = match.group(0)
        print("函数内容:")
        print("-" * 40)
        # 只显示关键行
        lines = function_content.split('\n')
        for line in lines[:15]:  # 显示前15行
            print(line)
        print("-" * 40)
        
        # 检查是否有实际的保存逻辑
        issues = []
        
        if 'self.tp_percent =' not in function_content:
            issues.append("未更新self.tp_percent变量")
        
        if 'self.sl_percent =' not in function_content:
            issues.append("未更新self.sl_percent变量")
        
        if 'json.dump' not in function_content and 'config' not in function_content:
            issues.append("未保存配置到文件")
        
        if issues:
            print("\n发现的问题:")
            for issue in issues:
                print(f"  ⚠️  {issue}")
            return False
        else:
            print("\n✓ 函数实现看起来正常")
            return True
    else:
        print("✗ 未找到apply_tp_sl_settings函数")
        return False

def check_trading_logic():
    """检查交易逻辑中的止盈止损使用"""
    print("\n=== 交易逻辑中的止盈止损使用检查 ===")
    
    with open('main_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找交易相关的止盈止损逻辑
    tp_sl_patterns = [
        'tp_price_to_use',
        'sl_price_to_use', 
        'self.tp_percent',
        'self.sl_percent',
        'tp_percent / 100',
        'sl_percent / 100'
    ]
    
    found_patterns = []
    for pattern in tp_sl_patterns:
        if pattern in content:
            count = content.count(pattern)
            found_patterns.append(f"✓ {pattern}: 使用了{count}次")
        else:
            found_patterns.append(f"✗ {pattern}: 未使用")
    
    for pattern in found_patterns:
        print(f"  {pattern}")
    
    # 检查是否有止盈止损订单创建逻辑
    if 'create_order' in content and 'TAKE_PROFIT' in content:
        print("  ✓ 发现止盈订单创建逻辑")
    else:
        print("  ✗ 未发现止盈订单创建逻辑")
    
    if 'create_order' in content and 'STOP_MARKET' in content:
        print("  ✓ 发现止损订单创建逻辑")
    else:
        print("  ✗ 未发现止损订单创建逻辑")

def analyze_tp_sl_flow():
    """分析止盈止损的完整流程"""
    print("\n=== 止盈止损流程分析 ===")
    
    flow_steps = [
        "1. UI设置 → 用户在界面设置止盈止损百分比",
        "2. 应用设置 → 点击'应用设置'按钮",
        "3. 保存配置 → 设置保存到配置文件和内存变量",
        "4. 交易触发 → AI分析触发交易",
        "5. 计算价格 → 根据入场价格和百分比计算止盈止损价格",
        "6. 创建订单 → 创建主订单后设置止盈止损订单",
        "7. 监控执行 → 监控订单执行状态"
    ]
    
    for step in flow_steps:
        print(f"  {step}")
    
    print("\n可能的问题点:")
    problems = [
        "• apply_tp_sl_settings函数没有实际保存设置",
        "• UI控件值没有同步到内存变量",
        "• 交易时使用了错误的变量或默认值",
        "• 止盈止损订单创建失败但没有重试",
        "• 配置文件权限问题导致无法保存"
    ]
    
    for problem in problems:
        print(f"  {problem}")

def generate_fix_suggestions():
    """生成修复建议"""
    print("\n=== 修复建议 ===")
    
    suggestions = [
        "1. 修复apply_tp_sl_settings函数，确保实际保存设置",
        "2. 在apply_tp_sl_settings中更新self.tp_percent和self.sl_percent",
        "3. 在apply_tp_sl_settings中保存配置到config.json文件",
        "4. 添加配置验证，确保设置生效",
        "5. 在交易日志中记录使用的止盈止损参数",
        "6. 添加止盈止损订单创建的错误处理和重试机制",
        "7. 在UI中显示当前生效的止盈止损设置"
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def test_tp_sl_calculation():
    """测试止盈止损价格计算"""
    print("\n=== 止盈止损价格计算测试 ===")

    test_scenarios = [
        {
            "name": "做多交易",
            "side": "buy",
            "entry_price": 50000.0,
            "tp_percent": 2.0,
            "sl_percent": 1.5
        },
        {
            "name": "做空交易",
            "side": "sell",
            "entry_price": 50000.0,
            "tp_percent": 2.0,
            "sl_percent": 1.5
        }
    ]

    for scenario in test_scenarios:
        print(f"\n  {scenario['name']}:")
        entry_price = scenario['entry_price']
        tp_percent = scenario['tp_percent']
        sl_percent = scenario['sl_percent']
        side = scenario['side']

        if side == 'buy':
            tp_price = entry_price * (1 + tp_percent / 100)
            sl_price = entry_price * (1 - sl_percent / 100)
        else:  # sell
            tp_price = entry_price * (1 - tp_percent / 100)
            sl_price = entry_price * (1 + sl_percent / 100)

        print(f"    入场价格: ${entry_price}")
        print(f"    止盈设置: {tp_percent}% → ${tp_price:.2f}")
        print(f"    止损设置: {sl_percent}% → ${sl_price:.2f}")

        # 计算盈亏比
        if side == 'buy':
            profit_potential = tp_price - entry_price
            loss_potential = entry_price - sl_price
        else:
            profit_potential = entry_price - tp_price
            loss_potential = sl_price - entry_price

        risk_reward_ratio = profit_potential / loss_potential if loss_potential > 0 else 0
        print(f"    盈亏比: 1:{risk_reward_ratio:.2f}")

def main():
    """主函数"""
    print("止盈止损功能检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 执行各项检查
    check_config_files()
    code_ok = check_code_implementation()
    apply_ok = check_apply_function_implementation()
    check_trading_logic()
    test_tp_sl_calculation()
    analyze_tp_sl_flow()

    # 重新检查修复后的apply函数
    print("\n=== 重新检查apply_tp_sl_settings函数 ===")
    apply_ok_after_fix = check_apply_function_implementation()

    if apply_ok_after_fix:
        print("✓ apply_tp_sl_settings函数已修复")
    else:
        print("✗ apply_tp_sl_settings函数仍有问题")
        generate_fix_suggestions()

    # 总结
    print("\n=== 检查总结 ===")
    print(f"代码实现: {'✓' if code_ok else '✗'}")
    print(f"应用函数: {'✓' if apply_ok_after_fix else '✗'}")

    if code_ok and apply_ok_after_fix:
        print("\n🎉 止盈止损功能已修复并正常工作！")
        print("\n使用说明:")
        print("1. 在界面中设置止盈止损百分比")
        print("2. 点击'应用设置'按钮保存配置")
        print("3. 启动自动交易，系统将使用您的设置")
        print("4. 查看交易日志确认使用的止盈止损参数")
    else:
        print("\n⚠️  止盈止损功能仍存在问题，需要进一步修复")

if __name__ == "__main__":
    main()
