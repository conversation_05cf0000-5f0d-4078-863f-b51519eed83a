"""
币安量化交易机器人登录模块

该模块提供了一个基于PyQt6的登录界面，用于：
- 收集和验证币安API密钥和Secret密钥
- 保存和加载已保存的凭证
- 提供友好的用户界面和错误处理
- 成功验证后启动主应用程序

作者: [李兴]
版本: 1.0.0
创建日期: 2024
"""

import sys
import os
import math
from typing import Tuple, Optional, Union, Dict, Any, cast
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox,
                           QCheckBox, QGraphicsDropShadowEffect, QFrame, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QPoint, QPropertyAnimation, QEasingCurve, QRect, QParallelAnimationGroup
from PyQt6.QtGui import QFont, QIcon, QMovie, QPixmap, QPainter, QLinearGradient, QColor, QPaintEvent, QFontDatabase
from dotenv import load_dotenv
import ccxt
from main_window import MainWindow

class AnimatedBackground(QWidget):
    """动态背景组件 - 创建现代化的动态渐变背景"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(800, 600)

        # 动画参数
        self.animation_offset = 0
        self.particles = []

        # 创建粒子
        for i in range(20):
            particle = {
                'x': i * 40,
                'y': i * 30,
                'speed': 0.5 + (i % 3) * 0.3,
                'size': 2 + (i % 4),
                'opacity': 0.1 + (i % 5) * 0.1
            }
            self.particles.append(particle)

        # 动画定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)  # 20 FPS

    def update_animation(self):
        """更新动画状态"""
        self.animation_offset += 1

        # 更新粒子位置
        for particle in self.particles:
            particle['x'] += particle['speed']
            particle['y'] += particle['speed'] * 0.5

            # 边界检查
            if particle['x'] > self.width():
                particle['x'] = -particle['size']
            if particle['y'] > self.height():
                particle['y'] = -particle['size']

        self.update()

    def paintEvent(self, event: QPaintEvent):
        """绘制动态背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 主渐变背景
        gradient = QLinearGradient(0, 0, self.width(), self.height())
        gradient.setColorAt(0, QColor(15, 20, 25))  # #0F1419
        gradient.setColorAt(0.3, QColor(26, 31, 46))  # #1A1F2E
        gradient.setColorAt(0.7, QColor(17, 24, 39))  # #111827
        gradient.setColorAt(1, QColor(11, 14, 17))   # #0B0E11

        painter.fillRect(self.rect(), gradient)

        # 动态波浪效果
        wave_gradient = QLinearGradient(0, 0, self.width(), 0)
        wave_gradient.setColorAt(0, QColor(240, 185, 11, 30))  # 币安金色
        wave_gradient.setColorAt(0.5, QColor(59, 130, 246, 20))  # 蓝色
        wave_gradient.setColorAt(1, QColor(139, 92, 246, 25))   # 紫色

        painter.setBrush(wave_gradient)
        painter.setPen(Qt.PenStyle.NoPen)

        # 绘制波浪
        for i in range(3):
            wave_height = 100 + i * 50
            wave_y = self.height() - wave_height + math.sin((self.animation_offset + i * 60) * 0.02) * 30

            # 创建波浪路径
            points = []
            for x in range(0, self.width() + 10, 10):
                y = wave_y + math.sin((x + self.animation_offset * 2) * 0.01) * 20
                points.append(QPoint(x, int(y)))

            # 添加底部点以形成闭合区域
            points.append(QPoint(self.width(), self.height()))
            points.append(QPoint(0, self.height()))

            painter.drawPolygon(points)

        # 绘制动态粒子
        for particle in self.particles:
            painter.setBrush(QColor(240, 185, 11, int(particle['opacity'] * 255)))
            painter.drawEllipse(
                int(particle['x']),
                int(particle['y']),
                particle['size'],
                particle['size']
            )

class ModernCard(QFrame):
    """现代化卡片组件 - 玻璃态效果"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("modern_card")

        # 设置阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.setGraphicsEffect(shadow)

        # 设置样式
        self.setStyleSheet("""
            #modern_card {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:1 rgba(255, 255, 255, 0.05));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                backdrop-filter: blur(20px);
            }
        """)

class AnimatedButton(QPushButton):
    """动画按钮组件"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setFixedHeight(50)

        # 动画属性
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.Type.OutCubic)

        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #F0B90B, stop:1 #FCD34D);
                color: #0B0E11;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: 700;
                padding: 0 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FCD34D, stop:1 #F59E0B);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #D97706, stop:1 #F59E0B);
            }
            QPushButton:disabled {
                background: rgba(148, 163, 184, 0.3);
                color: rgba(148, 163, 184, 0.7);
            }
        """)

    def enterEvent(self, event):
        """鼠标进入事件 - 添加悬停动画"""
        super().enterEvent(event)
        current_rect = self.geometry()
        new_rect = QRect(current_rect.x(), current_rect.y() - 2, current_rect.width(), current_rect.height())

        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(new_rect)
        self.hover_animation.start()

    def leaveEvent(self, event):
        """鼠标离开事件 - 恢复原位置"""
        super().leaveEvent(event)
        current_rect = self.geometry()
        new_rect = QRect(current_rect.x(), current_rect.y() + 2, current_rect.width(), current_rect.height())

        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(new_rect)
        self.hover_animation.start()

class LoginWindow(QMainWindow):
    """
    币安API登录窗口类
    
    提供用户界面用于输入和验证API凭证，以及保存和加载凭证。
    成功验证后，启动主应用程序窗口。
    
    Attributes:
        api_key_valid: API密钥是否有效
        secret_key_valid: Secret密钥是否有效
        remember_me: 是否记住凭证
    """
    
    def __init__(self) -> None:
        """初始化现代化登录窗口"""
        super().__init__()
        self.setWindowTitle("🚀 Binance Quant Trading Bot")
        self.setFixedSize(800, 600)
        self.setWindowIcon(QIcon("logo.png"))

        # 移除窗口边框，创建现代化外观
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 验证状态
        self.api_key_valid: bool = False
        self.secret_key_valid: bool = False
        self.remember_me: bool = False

        # UI元素
        self.background: AnimatedBackground
        self.main_card: ModernCard
        self.api_key_input: QLineEdit
        self.secret_key_input: QLineEdit
        self.remember_me_checkbox: QCheckBox
        self.login_button: AnimatedButton
        self.loading_label: QLabel
        self.loading_movie: QMovie
        self.main_window: Optional[MainWindow] = None

        # 动画组
        self.entrance_animation: QParallelAnimationGroup

        self.init_ui()
        self.load_saved_credentials()
        self.start_entrance_animation()

    def init_ui(self) -> None:
        """初始化现代化用户界面"""
        # 创建主容器
        main_container = QWidget()
        self.setCentralWidget(main_container)

        # 创建动态背景
        self.background = AnimatedBackground(main_container)

        # 创建主布局
        main_layout = QHBoxLayout(main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 左侧装饰区域
        left_spacer = QSpacerItem(200, 0, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)
        main_layout.addItem(left_spacer)

        # 中央登录卡片
        self.main_card = ModernCard()
        self.main_card.setFixedSize(400, 540)
        main_layout.addWidget(self.main_card)

        # 右侧装饰区域
        right_spacer = QSpacerItem(200, 0, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)
        main_layout.addItem(right_spacer)

        # 设置卡片内容
        self._setup_card_content()

        # 添加关闭按钮
        self._create_close_button()

    def _setup_card_content(self) -> None:
        """设置卡片内容"""
        card_layout = QVBoxLayout(self.main_card)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(0)

        # 创建头部
        self._create_modern_header(card_layout)

        # 创建表单
        self._create_modern_form(card_layout)

        # 创建底部
        self._create_modern_footer(card_layout)

    def _create_close_button(self) -> None:
        """创建关闭按钮"""
        close_button = QPushButton("✕")
        close_button.setParent(self)
        close_button.setGeometry(750, 20, 30, 30)
        close_button.setStyleSheet("""
            QPushButton {
                background: rgba(239, 68, 68, 0.8);
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 1.0);
                transform: scale(1.1);
            }
        """)
        close_button.clicked.connect(self.close)
        close_button.show()

    def _create_modern_header(self, layout: QVBoxLayout) -> None:
        """创建现代化头部"""
        # Logo区域
        logo_container = QWidget()
        logo_layout = QHBoxLayout(logo_container)
        logo_layout.setContentsMargins(0, 0, 0, 0)

        # Logo图标
        logo_label = QLabel("🚀")
        logo_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #F0B90B;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 185, 11, 0.2),
                    stop:1 rgba(252, 211, 77, 0.1));
                border-radius: 30px;
                padding: 15px;
                min-width: 60px;
                min-height: 60px;
            }
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        logo_layout.addStretch()
        logo_layout.addWidget(logo_label)
        logo_layout.addStretch()

        layout.addWidget(logo_container)

        # 标题
        title_label = QLabel("Welcome Back")
        title_label.setStyleSheet("""
            QLabel {
                color: #F8FAFC;
                font-size: 28px;
                font-weight: 700;
                margin: 10px 0;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("Sign in to your trading account")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #94A3B8;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 20px;
            }
        """)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(subtitle_label)

    def _create_modern_form(self, layout: QVBoxLayout) -> None:
        """创建现代化表单"""
        # API Key输入
        api_key_container = QWidget()
        api_key_layout = QVBoxLayout(api_key_container)
        api_key_layout.setContentsMargins(0, 0, 0, 0)
        api_key_layout.setSpacing(8)

        api_key_label = QLabel("🔑 API Key")
        api_key_label.setStyleSheet("""
            QLabel {
                color: #F8FAFC;
                font-size: 14px;
                font-weight: 600;
                margin-left: 5px;
            }
        """)
        api_key_layout.addWidget(api_key_label)

        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("Enter your Binance API key")
        self.api_key_input.setStyleSheet("""
            QLineEdit {
                padding: 15px 20px;
                border: 2px solid rgba(148, 163, 184, 0.3);
                border-radius: 12px;
                background: rgba(15, 20, 25, 0.8);
                color: #F8FAFC;
                font-size: 14px;
                font-weight: 500;
            }
            QLineEdit:focus {
                border: 2px solid #F0B90B;
                background: rgba(15, 20, 25, 0.9);
            }
            QLineEdit:hover {
                border: 2px solid rgba(240, 185, 11, 0.6);
            }
        """)
        self.api_key_input.textChanged.connect(lambda: self.validate_field("api_key"))
        api_key_layout.addWidget(self.api_key_input)

        layout.addWidget(api_key_container)

        # Secret Key输入
        secret_key_container = QWidget()
        secret_key_layout = QVBoxLayout(secret_key_container)
        secret_key_layout.setContentsMargins(0, 0, 0, 0)
        secret_key_layout.setSpacing(8)

        secret_key_label = QLabel("🔐 Secret Key")
        secret_key_label.setStyleSheet("""
            QLabel {
                color: #F8FAFC;
                font-size: 14px;
                font-weight: 600;
                margin-left: 5px;
            }
        """)
        secret_key_layout.addWidget(secret_key_label)

        self.secret_key_input = QLineEdit()
        self.secret_key_input.setPlaceholderText("Enter your Binance secret key")
        self.secret_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.secret_key_input.setStyleSheet("""
            QLineEdit {
                padding: 15px 20px;
                border: 2px solid rgba(148, 163, 184, 0.3);
                border-radius: 12px;
                background: rgba(15, 20, 25, 0.8);
                color: #F8FAFC;
                font-size: 14px;
                font-weight: 500;
            }
            QLineEdit:focus {
                border: 2px solid #F0B90B;
                background: rgba(15, 20, 25, 0.9);
            }
            QLineEdit:hover {
                border: 2px solid rgba(240, 185, 11, 0.6);
            }
        """)
        self.secret_key_input.textChanged.connect(lambda: self.validate_field("secret_key"))
        secret_key_layout.addWidget(self.secret_key_input)

        layout.addWidget(secret_key_container)

    def _create_modern_footer(self, layout: QVBoxLayout) -> None:
        """创建现代化底部"""
        # 选项区域
        options_container = QWidget()
        options_layout = QHBoxLayout(options_container)
        options_layout.setContentsMargins(5, 0, 5, 0)

        # 记住我复选框
        self.remember_me_checkbox = QCheckBox("Remember me")
        self.remember_me_checkbox.setChecked(True)
        self.remember_me = True
        self.remember_me_checkbox.setStyleSheet("""
            QCheckBox {
                color: #94A3B8;
                font-size: 14px;
                font-weight: 500;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 6px;
                border: 2px solid rgba(148, 163, 184, 0.5);
                background: rgba(15, 20, 25, 0.8);
            }
            QCheckBox::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #F0B90B, stop:1 #FCD34D);
                border: 2px solid #F0B90B;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #F0B90B;
            }
        """)
        self.remember_me_checkbox.stateChanged.connect(self.toggle_remember_me)

        # 删除保存的凭证按钮
        delete_button = QPushButton("🗑️ Clear saved")
        delete_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                color: #94A3B8;
                border: none;
                font-size: 13px;
                font-weight: 500;
                padding: 5px 10px;
                border-radius: 6px;
            }
            QPushButton:hover {
                color: #EF4444;
                background: rgba(239, 68, 68, 0.1);
            }
        """)
        delete_button.clicked.connect(self.delete_credentials)

        options_layout.addWidget(self.remember_me_checkbox)
        options_layout.addStretch()
        options_layout.addWidget(delete_button)

        layout.addWidget(options_container)

        # 登录按钮
        self.login_button = AnimatedButton("Sign In")
        self.login_button.setEnabled(False)
        self.login_button.clicked.connect(self.login)
        layout.addWidget(self.login_button)

        # 加载动画
        self.loading_label = QLabel()
        self.loading_movie = QMovie("loading.gif")
        if self.loading_movie.isValid():
            self.loading_movie.setScaledSize(self.loading_label.sizeHint())
            self.loading_label.setMovie(self.loading_movie)
        self.loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.loading_label.hide()
        layout.addWidget(self.loading_label)

        # 帮助链接
        help_label = QLabel("Need help? <a href='https://www.binance.com/cn/my/settings/api-management' style='color: #F0B90B; text-decoration: none;'>Get API keys →</a>")
        help_label.setOpenExternalLinks(True)
        help_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        help_label.setStyleSheet("""
            QLabel {
                color: #64748B;
                font-size: 13px;
                font-weight: 500;
                margin-top: 15px;
            }
        """)
        layout.addWidget(help_label)

    def start_entrance_animation(self) -> None:
        """启动入场动画"""
        # 初始状态：卡片在屏幕外
        self.main_card.move(800, 50)  # 从右侧进入
        self.main_card.setStyleSheet(self.main_card.styleSheet() + "opacity: 0;")

        # 创建动画组
        self.entrance_animation = QParallelAnimationGroup()

        # 位置动画
        position_animation = QPropertyAnimation(self.main_card, b"pos")
        position_animation.setDuration(800)
        position_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        position_animation.setStartValue(QPoint(800, 50))
        position_animation.setEndValue(QPoint(200, 50))

        # 透明度动画（通过样式表模拟）
        opacity_timer = QTimer()
        opacity_steps = 0

        def update_opacity():
            nonlocal opacity_steps
            opacity_steps += 1
            opacity = min(1.0, opacity_steps / 20.0)
            if opacity >= 1.0:
                self.main_card.setStyleSheet(self.main_card.styleSheet().replace("opacity: 0;", ""))
                opacity_timer.stop()

        opacity_timer.timeout.connect(update_opacity)
        opacity_timer.start(40)  # 每40ms更新一次

        self.entrance_animation.addAnimation(position_animation)
        self.entrance_animation.start()

    def mousePressEvent(self, event):
        """鼠标按下事件 - 用于拖拽窗口"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖拽窗口"""
        if event.buttons() == Qt.MouseButton.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def validate_field(self, field_name: str) -> None:
        """
        验证单个字段并提供现代化视觉反馈

        Args:
            field_name: 要验证的字段名称 ("api_key" 或 "secret_key")
        """
        api_key = self.api_key_input.text().strip()
        secret_key = self.secret_key_input.text().strip()

        self.api_key_valid = len(api_key) >= 10
        self.secret_key_valid = len(secret_key) >= 10

        def set_modern_style(widget: QLineEdit, valid: bool, has_content: bool) -> None:
            if not has_content:
                # 默认样式
                border_color = "rgba(148, 163, 184, 0.3)"
                bg_color = "rgba(15, 20, 25, 0.8)"
            elif valid:
                # 有效样式
                border_color = "#10B981"
                bg_color = "rgba(16, 185, 129, 0.1)"
            else:
                # 无效样式
                border_color = "#EF4444"
                bg_color = "rgba(239, 68, 68, 0.1)"

            style = f"""
                QLineEdit {{
                    padding: 15px 20px;
                    border: 2px solid {border_color};
                    border-radius: 12px;
                    background: {bg_color};
                    color: #F8FAFC;
                    font-size: 14px;
                    font-weight: 500;
                }}
                QLineEdit:focus {{
                    border: 2px solid #F0B90B;
                    background: rgba(15, 20, 25, 0.9);
                }}
                QLineEdit:hover {{
                    border: 2px solid rgba(240, 185, 11, 0.6);
                }}
            """
            widget.setStyleSheet(style)

        if field_name == "api_key":
            set_modern_style(self.api_key_input, self.api_key_valid, bool(api_key))
        elif field_name == "secret_key":
            set_modern_style(self.secret_key_input, self.secret_key_valid, bool(secret_key))

        self.update_login_button_state()
        
    def update_login_button_state(self) -> None:
        """根据字段验证结果更新登录按钮状态"""
        if self.api_key_input.text().strip() and self.secret_key_input.text().strip():
            self.login_button.setEnabled(self.api_key_valid and self.secret_key_valid)
        else:
            self.login_button.setEnabled(False)
                
    def load_saved_credentials(self) -> None:
        """
        从环境变量加载保存的API凭证
        
        尝试从.env文件加载API密钥和Secret密钥，
        如果找到有效凭证则自动填充到输入框中
        """
        try:
            load_dotenv()
            api_key = os.getenv('BINANCE_API_KEY', '')
            secret_key = os.getenv('BINANCE_SECRET_KEY', '')
            
            self.api_key_input.setText(api_key)
            self.secret_key_input.setText(secret_key)
            
            if api_key and secret_key:
                self.remember_me_checkbox.setChecked(True)
                self.remember_me = True
                self.validate_field("api_key")
                self.validate_field("secret_key")
                
        except Exception as e:
            print(f"Error loading credentials: {str(e)}")
            
    def save_credentials(self) -> None:
        """
        保存API凭证到.env文件
        
        将当前输入的API密钥和Secret密钥写入.env文件
        以便下次启动应用程序时自动加载
        """
        try:
            api_key = self.api_key_input.text().strip()
            secret_key = self.secret_key_input.text().strip()
            
            if not (api_key and secret_key):
                # No warning needed, just don't save if fields are empty
                return
                
            with open('.env', 'w') as f:
                f.write(f"BINANCE_API_KEY={api_key}\n")
                f.write(f"BINANCE_SECRET_KEY={secret_key}\n")
            
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save credentials: {str(e)}")
            
    def delete_credentials(self) -> None:
        """
        删除保存的API凭证
        
        删除.env文件并清除当前UI中的凭证数据
        """
        try:
            reply = QMessageBox.question(self, "Confirm Deletion", 
                                      "Are you sure you want to delete the saved API configuration?",
                                      QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                if os.path.exists('.env'):
                    os.remove('.env')
                    
                self.api_key_input.clear()
                self.secret_key_input.clear()
                
                self.api_key_valid = False
                self.secret_key_valid = False
                
                self.remember_me_checkbox.setChecked(False)
                self.remember_me = False
                
                self.api_key_input.setStyleSheet("")
                self.secret_key_input.setStyleSheet("")
                
                self.update_login_button_state()
                
                QMessageBox.information(self, "Success", "Configuration deleted successfully!")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to delete credentials: {str(e)}")
        
    def validate_credentials(self, api_key: str, secret_key: str) -> Tuple[bool, str]:
        """
        验证API凭证是否有效
        
        Args:
            api_key: 币安API密钥
            secret_key: 币安Secret密钥
            
        Returns:
            Tuple[bool, str]: (是否验证成功, 消息)，
                成功返回(True, 成功消息)，
                失败返回(False, 错误消息)
        """
        try:
            exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': secret_key,
                'enableRateLimit': True,
                'options': {'adjustForTimeDifference': True}
            })
            
            balance = exchange.fetch_balance()
            return True, "API credentials verified successfully!"
            
        except ccxt.AuthenticationError:
            return False, "API validation failed: Invalid API Key or Secret."
        except ccxt.NetworkError as e:
            return False, f"API validation failed: Network error - {str(e)}"
        except Exception as e:
            return False, f"An unexpected error occurred during API validation: {str(e)}"
        
    def toggle_remember_me(self, state: Qt.CheckState) -> None:
        """
        切换记住凭证状态
        
        Args:
            state: 复选框状态
        """
        self.remember_me = state == Qt.CheckState.Checked

    def validate_inputs(self) -> bool:
        """
        验证所有输入字段
        
        Returns:
            bool: 如果所有字段验证通过返回True，否则返回False
        """
        # 再次验证所有字段
        self.validate_field("api_key")
        self.validate_field("secret_key")
        
        return self.api_key_valid and self.secret_key_valid

    def login(self) -> None:
        """
        处理现代化登录操作

        验证输入，显示加载动画，并开始API验证过程
        """
        if not self.validate_inputs():
            self.show_modern_message("Input Error", "Please ensure all fields are correctly filled out.", "warning")
            return

        # 显示加载状态
        self.loading_label.show()
        if self.loading_movie.isValid():
            self.loading_movie.start()

        self.login_button.setEnabled(False)
        self.login_button.setText("🔄 Verifying...")
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #6366F1, stop:1 #8B5CF6);
                color: white;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: 700;
                padding: 0 30px;
            }
        """)

        QApplication.processEvents()

        api_key = self.api_key_input.text().strip()
        secret_key = self.secret_key_input.text().strip()

        # Run validation in a separate thread/timer to keep UI responsive
        QTimer.singleShot(100, lambda: self._perform_login(api_key, secret_key))

    def _perform_login(self, api_key: str, secret_key: str) -> None:
        """
        执行实际的登录验证

        Args:
            api_key: API密钥
            secret_key: Secret密钥
        """
        try:
            success, message = self.validate_credentials(api_key, secret_key)

            if self.loading_movie.isValid():
                self.loading_movie.stop()
            self.loading_label.hide()

            if success:
                if self.remember_me:
                    self.save_credentials()
                else:
                    # If not remembering, ensure .env is deleted
                    if os.path.exists('.env'):
                        os.remove('.env')

                # 成功动画
                self.login_button.setText("✅ Success!")
                self.login_button.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #10B981, stop:1 #34D399);
                        color: white;
                        border: none;
                        border-radius: 25px;
                        font-size: 16px;
                        font-weight: 700;
                        padding: 0 30px;
                    }
                """)
                QApplication.processEvents()

                QTimer.singleShot(1200, self.open_main_window)
            else:
                self.reset_login_button()
                self.show_modern_message("Login Failed", message, "error")

        except Exception as e:
            if self.loading_movie.isValid():
                self.loading_movie.stop()
            self.loading_label.hide()
            self.reset_login_button()
            self.show_modern_message("Error", f"An error occurred during login: {str(e)}", "error")

    def reset_login_button(self) -> None:
        """重置登录按钮到初始状态"""
        self.login_button.setEnabled(True)
        self.login_button.setText("Sign In")
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #F0B90B, stop:1 #FCD34D);
                color: #0B0E11;
                border: none;
                border-radius: 25px;
                font-size: 16px;
                font-weight: 700;
                padding: 0 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FCD34D, stop:1 #F59E0B);
            }
        """)

    def show_modern_message(self, title: str, message: str, msg_type: str = "info") -> None:
        """显示现代化消息框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)

        # 根据类型设置图标
        if msg_type == "error":
            msg_box.setIcon(QMessageBox.Icon.Critical)
        elif msg_type == "warning":
            msg_box.setIcon(QMessageBox.Icon.Warning)
        else:
            msg_box.setIcon(QMessageBox.Icon.Information)

        # 现代化样式
        msg_box.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1E293B, stop:1 #0F172A);
                color: #F8FAFC;
                border-radius: 12px;
            }
            QMessageBox QPushButton {
                background: #F0B90B;
                color: #0B0E11;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 600;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background: #FCD34D;
            }
        """)

        msg_box.exec()
    
    def open_main_window(self) -> None:
        """打开主交易界面窗口并关闭登录窗口"""
        self.main_window = MainWindow()
        self.main_window.show()
        self.close()


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    sys.exit(app.exec()) 