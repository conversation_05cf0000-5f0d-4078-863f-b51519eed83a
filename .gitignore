# 环境变量文件 - 包含敏感的API密钥
.env

# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 虚拟环境
venv/
env/
ENV/
.venv/

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
app_error.log

# 配置文件备份
*.bak
backup/

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 测试报告
*test_report*.json

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 编译文件
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
